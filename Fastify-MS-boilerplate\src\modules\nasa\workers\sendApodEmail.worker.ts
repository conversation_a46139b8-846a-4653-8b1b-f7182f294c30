import 'dotenv/config';
import { Worker, Job } from 'bullmq';
import * as nasaService from '../service/nasa.service';

/**
 * Worker responsável por enviar o e-mail do APOD da NASA.
 * O worker deve ser executado em um ambiente de produção com o comando:
 * `npm run worker:nasa`
 */
const worker = new Worker(
  'nasa',
  async (job: Job) => {
    if (job.name === 'send-apod-email') {
      const { email } = job.data;
      await nasaService.sendApodEmailToRecipient(email);
    }
  },
  {
    connection: {
      host: process.env.REDIS_HOST,
      port: Number(process.env.REDIS_PORT),
      // password: process.env.REDIS_PASSWORD, // se necessário
    },
  }
);

worker.on('completed', (job) => {
  console.log(`Job ${job.id} completed!`);
});

worker.on('failed', (job, err) => {
  console.error(`Job ${job?.id} failed:`, err);
});

worker.on('error', err => {
  // log the error
  console.error(err);
}); 