FROM node:18-alpine as builder

WORKDIR /app

# Copia arquivos de dependência
COPY package*.json ./
COPY prisma ./prisma/

# Instala dependências
RUN npm install

# Copia código fonte
COPY . .

# Gera o cliente Prisma e compila o código
RUN npx prisma generate
RUN npm run build

# Stage de produção
FROM node:18-alpine as production

WORKDIR /app

# Define argumentos e variáveis de ambiente
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

# Copia apenas o necessário para produção
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/prisma ./prisma

# Expõe a porta da aplicação
EXPOSE 3000

# Verifica a saúde da aplicação
HEALTHCHECK --interval=30s --timeout=3s --start-period=30s --retries=3 CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/v1/health || exit 1

# Define o ponto de entrada
CMD ["node", "dist/main"] 