import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Task } from '../../core/domain/task.entity';
import { TaskRepository } from '../../core/ports/repositories/task-repository.interface';
import { TaskStatus } from '../../core/domain/task-status.enum';
import { PrismaClient } from '@prisma/client';

// Define o tipo PrismaTask como o tipo retornado pelo prisma para tarefas
type PrismaTask = Awaited<ReturnType<PrismaClient['task']['findUnique']>>;

@Injectable()
export class PrismaTaskRepository implements TaskRepository {
  constructor(private prisma: PrismaService) {}

  async findAll(): Promise<Task[]> {
    const tasks = await this.prisma.task.findMany({
      include: { user: true },
    });
    return tasks.map((task) => this.mapPrismaTaskToDomain(task));
  }

  async findById(id: string): Promise<Task | null> {
    const task = await this.prisma.task.findUnique({
      where: { id },
      include: { user: true },
    });

    if (!task) return null;

    return this.mapPrismaTaskToDomain(task);
  }

  async findByUserId(userId: string): Promise<Task[]> {
    const tasks = await this.prisma.task.findMany({
      where: { userId },
      include: { user: true },
    });

    return tasks.map((task) => this.mapPrismaTaskToDomain(task));
  }

  async create(task: Task): Promise<Task> {
    const createdTask = await this.prisma.task.create({
      data: {
        id: task.id,
        title: task.title,
        description: task.description,
        status: task.status,
        userId: task.userId,
      },
      include: { user: true },
    });

    return this.mapPrismaTaskToDomain(createdTask);
  }

  async update(task: Task): Promise<Task> {
    const updatedTask = await this.prisma.task.update({
      where: { id: task.id },
      data: {
        title: task.title,
        description: task.description,
        status: task.status,
      },
      include: { user: true },
    });

    return this.mapPrismaTaskToDomain(updatedTask);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.task.delete({
      where: { id },
    });
  }

  private mapPrismaTaskToDomain(
    prismaTask: PrismaTask & { user?: unknown },
  ): Task {
    return new Task(
      prismaTask.id,
      prismaTask.userId,
      prismaTask.title,
      prismaTask.description ?? '',
      prismaTask.status as TaskStatus,
      prismaTask.createdAt,
      prismaTask.updatedAt,
    );
  }
}
