RBTree=function(v){var p=function(g){g=p.m[g];if(g.mod)return g.mod.exports;var t=g.mod={exports:{}};g(t,t.exports);return t.exports};p.m={};p.m["./treebase"]=function(g,p){function d(){}function h(a){this._tree=a;this._ancestors=[];this._cursor=null}d.prototype.clear=function(){this._root=null;this.size=0};d.prototype.find=function(a){for(var b=this._root;null!==b;){var c=this._comparator(a,b.data);if(0===c)return b.data;b=b.get_child(0<c)}return null};d.prototype.findIter=function(a){for(var b=
this._root,c=this.iterator();null!==b;){var d=this._comparator(a,b.data);if(0===d)return c._cursor=b,c;c._ancestors.push(b);b=b.get_child(0<d)}return null};d.prototype.lowerBound=function(a){for(var b=this._root,c=this.iterator(),d=this._comparator;null!==b;){var q=d(a,b.data);if(0===q)return c._cursor=b,c;c._ancestors.push(b);b=b.get_child(0<q)}for(q=c._ancestors.length-1;0<=q;--q)if(b=c._ancestors[q],0>d(a,b.data))return c._cursor=b,c._ancestors.length=q,c;c._ancestors.length=0;return c};d.prototype.upperBound=
function(a){for(var b=this.lowerBound(a),c=this._comparator;null!==b.data()&&0===c(b.data(),a);)b.next();return b};d.prototype.min=function(){var a=this._root;if(null===a)return null;for(;null!==a.left;)a=a.left;return a.data};d.prototype.max=function(){var a=this._root;if(null===a)return null;for(;null!==a.right;)a=a.right;return a.data};d.prototype.iterator=function(){return new h(this)};d.prototype.each=function(a){for(var b=this.iterator(),c;null!==(c=b.next());)a(c)};d.prototype.reach=function(a){for(var b=
this.iterator(),c;null!==(c=b.prev());)a(c)};h.prototype.data=function(){return null!==this._cursor?this._cursor.data:null};h.prototype.next=function(){if(null===this._cursor){var a=this._tree._root;null!==a&&this._minNode(a)}else if(null===this._cursor.right){do if(a=this._cursor,this._ancestors.length)this._cursor=this._ancestors.pop();else{this._cursor=null;break}while(this._cursor.right===a)}else this._ancestors.push(this._cursor),this._minNode(this._cursor.right);return null!==this._cursor?this._cursor.data:
null};h.prototype.prev=function(){if(null===this._cursor){var a=this._tree._root;null!==a&&this._maxNode(a)}else if(null===this._cursor.left){do if(a=this._cursor,this._ancestors.length)this._cursor=this._ancestors.pop();else{this._cursor=null;break}while(this._cursor.left===a)}else this._ancestors.push(this._cursor),this._maxNode(this._cursor.left);return null!==this._cursor?this._cursor.data:null};h.prototype._minNode=function(a){for(;null!==a.left;)this._ancestors.push(a),a=a.left;this._cursor=
a};h.prototype._maxNode=function(a){for(;null!==a.right;)this._ancestors.push(a),a=a.right;this._cursor=a};g.exports=d};p.m.__main__=function(g,t){function d(a){this.data=a;this.right=this.left=null;this.red=!0}function h(a){this._root=null;this._comparator=a;this.size=0}function a(a){return null!==a&&a.red}function b(a,b){var c=a.get_child(!b);a.set_child(!b,c.get_child(b));c.set_child(b,a);a.red=!0;c.red=!1;return c}function c(a,c){a.set_child(!c,b(a.get_child(!c),!c));return b(a,c)}var u=p("./treebase");
d.prototype.get_child=function(a){return a?this.right:this.left};d.prototype.set_child=function(a,b){a?this.right=b:this.left=b};h.prototype=new u;h.prototype.insert=function(q){var h=!1;if(null===this._root)this._root=new d(q),h=!0,this.size++;else{var f=new d(void 0),l=0,n=0,r=null,m=f,k=null,e=this._root;for(m.right=this._root;;){null===e?(e=new d(q),k.set_child(l,e),h=!0,this.size++):a(e.left)&&a(e.right)&&(e.red=!0,e.left.red=!1,e.right.red=!1);if(a(e)&&a(k)){var g=m.right===r;e===k.get_child(n)?
m.set_child(g,b(r,!n)):m.set_child(g,c(r,!n))}g=this._comparator(e.data,q);if(0===g)break;n=l;l=0>g;null!==r&&(m=r);r=k;k=e;e=e.get_child(l)}this._root=f.right}this._root.red=!1;return h};h.prototype.remove=function(g){if(null===this._root)return!1;var h=new d(void 0),f=h;f.right=this._root;for(var l=null,n=null,r=null,m=1;null!==f.get_child(m);){var k=m,n=l,l=f,f=f.get_child(m),e=this._comparator(g,f.data),m=0<e;0===e&&(r=f);if(!a(f)&&!a(f.get_child(m)))if(a(f.get_child(!m)))n=b(f,m),l.set_child(k,
n),l=n;else if(!a(f.get_child(!m))&&(e=l.get_child(!k),null!==e))if(a(e.get_child(!k))||a(e.get_child(k))){var p=n.right===l;a(e.get_child(k))?n.set_child(p,c(l,k)):a(e.get_child(!k))&&n.set_child(p,b(l,k));k=n.get_child(p);k.red=!0;f.red=!0;k.left.red=!1;k.right.red=!1}else l.red=!1,e.red=!0,f.red=!0}null!==r&&(r.data=f.data,l.set_child(l.right===f,f.get_child(null===f.left)),this.size--);this._root=h.right;null!==this._root&&(this._root.red=!1);return null!==r};g.exports=h};return p("__main__")}(window);
