import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import {
  CostCenterRepositoryPort,
  COST_CENTER_REPOSITORY,
} from '@/core/ports/repositories/cost-center-repository.port';
import { CostCenter } from '@/core/domain/cost-center/entities/cost-center.entity';

@Injectable()
export class FindCostCenterByUuidUseCase {
  constructor(
    @Inject(COST_CENTER_REPOSITORY)
    private readonly costCenterRepository: CostCenterRepositoryPort,
  ) {}

  async execute(uuid: string): Promise<CostCenter> {
    const costCenter = await this.costCenterRepository.findByUuid(uuid);

    if (!costCenter) {
      throw new NotFoundException(
        `Centro de custo com UUID ${uuid} não encontrado`,
      );
    }

    return costCenter;
  }
}
