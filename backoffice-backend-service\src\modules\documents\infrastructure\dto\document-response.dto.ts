import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DocumentStatus } from '../../domain/enums/document-status.enum';
import { EntityType } from '../../domain/enums/entity-type.enum';
import { Document } from '../../domain/entities/document.entity';

export class DocumentVersionDto {
  @ApiProperty()
  versionId: number;

  @ApiProperty()
  filePath: string;

  @ApiProperty()
  uploadedBy: string;

  @ApiPropertyOptional()
  expirationDate?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiPropertyOptional()
  downloadUrl?: string;

  @ApiPropertyOptional()
  fileName?: string;
}

export class DocumentResponseDto {
  @ApiProperty()
  uuid: string;

  @ApiProperty({ enum: EntityType })
  entityType: EntityType;

  @ApiProperty()
  entityUuid: string;

  @ApiProperty()
  currentVersion: number;

  @ApiProperty({ enum: DocumentStatus })
  status: DocumentStatus;

  @ApiProperty()
  uploadedBy: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiPropertyOptional()
  responsible?: string;

  @ApiPropertyOptional()
  department?: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiPropertyOptional({ type: [DocumentVersionDto] })
  versions?: DocumentVersionDto[];

  @ApiPropertyOptional()
  fileName?: string;

  @ApiPropertyOptional()
  downloadUrl?: string;

  static fromEntity(document: Document, downloadUrls?: { [versionId: number]: { url: string; fileName: string } }): DocumentResponseDto {
    const currentVersion = document.versions?.find(v => v.versionId === document.currentVersion);
    const currentDownloadInfo = currentVersion && downloadUrls?.[currentVersion.versionId];

    return {
      uuid: document.uuid,
      entityType: document.entityType,
      entityUuid: document.entityUuid,
      currentVersion: document.currentVersion,
      status: document.status,
      uploadedBy: document.uploadedBy,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt,
      responsible: document.responsible,
      department: document.department,
      description: document.description,
      fileName: currentDownloadInfo?.fileName || document.fileName || currentVersion?.filePath.split('/').pop(),
      downloadUrl: currentDownloadInfo?.url || document.downloadUrl,
      versions: document.versions?.map((version) => {
        const versionDownloadInfo = downloadUrls?.[version.versionId];
        return {
          versionId: version.versionId,
          filePath: version.filePath,
          uploadedBy: version.uploadedBy,
          expirationDate: version.expirationDate,
          createdAt: version.createdAt,
          downloadUrl: versionDownloadInfo?.url,
          fileName: versionDownloadInfo?.fileName || version.filePath.split('/').pop(),
        };
      }),
    };
  }
}
