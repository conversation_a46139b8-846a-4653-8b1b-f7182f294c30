import { AggregateRoot } from '../base/aggregate-root';
import { TaskStatus } from './task-status.enum';
import { TaskCreatedEvent } from './events/task-created.event';
import { TaskCompletedEvent } from './events/task-completed.event';
import { TaskCanceledEvent } from './events/task-canceled.event';
import { TaskTitleUpdatedEvent } from './events/task-title-updated.event';
import { TaskDescriptionUpdatedEvent } from './events/task-description-updated.event';
import { TaskStartedEvent } from './events/task-started.event';

export class Task extends AggregateRoot {
  readonly id: string;
  readonly userId: string;
  private _title: string;
  private _description: string;
  private _status: TaskStatus;
  readonly createdAt: Date;
  private _updatedAt: Date;

  constructor(
    id: string,
    userId: string,
    title: string,
    description: string,
    status: TaskStatus = TaskStatus.PENDING,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
  ) {
    super();
    this.id = id;
    this.userId = userId;
    this._title = title;
    this._description = description;
    this._status = status;
    this.createdAt = createdAt;
    this._updatedAt = updatedAt;

    // Adiciona evento de criação da task
    this.addEvent(new TaskCreatedEvent(this));
  }

  // Getters
  get title(): string {
    return this._title;
  }

  get description(): string {
    return this._description;
  }

  get status(): TaskStatus {
    return this._status;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  // Business logic methods
  updateTitle(title: string): void {
    this._title = title;
    this._updatedAt = new Date();

    // Adiciona evento de atualização do título
    this.addEvent(new TaskTitleUpdatedEvent(this, title));
  }

  updateDescription(description: string): void {
    this._description = description;
    this._updatedAt = new Date();

    // Adiciona evento de atualização da descrição
    this.addEvent(new TaskDescriptionUpdatedEvent(this, description));
  }

  start(): void {
    if (this._status !== TaskStatus.PENDING) {
      throw new Error('Só é possível iniciar uma tarefa que esteja pendente');
    }

    this._status = TaskStatus.IN_PROGRESS;
    this._updatedAt = new Date();

    // Adiciona evento de início da tarefa
    this.addEvent(new TaskStartedEvent(this));
  }

  complete(): void {
    if (this._status !== TaskStatus.IN_PROGRESS) {
      throw new Error(
        'Só é possível completar uma tarefa que esteja em progresso',
      );
    }

    this._status = TaskStatus.COMPLETED;
    this._updatedAt = new Date();

    // Adiciona evento de conclusão da tarefa
    this.addEvent(new TaskCompletedEvent(this));
  }

  cancel(): void {
    if (this._status === TaskStatus.COMPLETED) {
      throw new Error('Não é possível cancelar uma tarefa já concluída');
    }

    this._status = TaskStatus.CANCELED;
    this._updatedAt = new Date();

    // Adiciona evento de cancelamento da tarefa
    this.addEvent(new TaskCanceledEvent(this));
  }

  toJSON() {
    return {
      id: this.id,
      userId: this.userId,
      title: this._title,
      description: this._description,
      status: this._status,
      createdAt: this.createdAt,
      updatedAt: this._updatedAt,
    };
  }
}
