import { User } from '../../../domain/user.entity';
import { UserRepository } from '../../../ports/repositories/user-repository.interface';
import { v4 as uuidv4 } from 'uuid';
import { Role } from '../../../domain/role.enum';
import { PasswordHasher } from '../../../shared/password-hasher';

export interface CreateUserInput {
  name: string;
  email: string;
  password: string;
  role?: Role;
}

export class CreateUserUseCase {
  constructor(private userRepository: UserRepository) {}

  async execute(input: CreateUserInput): Promise<User> {
    const { name, email, password, role = Role.USER } = input;

    // Verificar se já existe um usuário com o mesmo email
    const existingUser = await this.userRepository
      .findByEmail(email)
      .catch(() => null);

    if (existingUser) {
      throw new Error(`Usuário com email ${email} já existe`);
    }

    const hashedPassword = await PasswordHasher.hash(password);

    // Criar a entidade de domínio
    const user = new User(uuidv4(), email, name, hashedPassword, role);

    // Persistir no repositório
    return this.userRepository.create(user);
  }
}
