name: backoffice-backend-service-prd
# replicaCount: 1
image:
  repository: 321711906762.dkr.ecr.us-east-2.amazonaws.com/backoffice-backend-service
  tag: latest
  pullPolicy: Always
  containerPort: 3000
environment:
  NODE_ENV: production
  JWT_SECRET: 'seu_jwt_secret'
  JWT_EXPIRATION: 3600
  RABBITMQ_HOST: b-c7502863-f899-4654-87d5-cb3affcb9355.mq.us-east-2.on.aws
  RABBITMQ_PORT: 5671
  RABBITMQ_USERNAME: backoffice-mq
  KEYCLOAK_BASE_URL: 'https://backoffice-keycloak.petrus-software.com'
  KEYCLOAK_REALM: 'cactus-backoffice'
  KEYCLOAK_CLIENT_ID: 'backoffice'
  # KEYCLOAK_CLIENT_ID: backend-dev-client
  # KEYCLOAK_CLIENT_SECRET: myclientsecret
  KEYCLOAK_ADMIN_USERNAME: petrus
  FRONTEND_URL: 'https://backoffice-frontend.petrus-software.com'
  # AWS
  AWS_REGION: us-east-2
  AWS_S3_BUCKET: bucket-backoffice-backend-prd

resources:
  requests:
    cpu: 0
    memory: 0
  limits:
    cpu: 0.5
    memory: 200Mi
livenessProbe:
  httpGet:
    path: {}
  initialDelaySeconds: 10
  failureThreshold: 3
  periodSeconds: 10
readinessProbe:
  tcpSocket: null
  initialDelaySeconds: 10
  periodSeconds: 10
  failureThreshold: 3
service:
  type: ClusterIP
  targetPort: 3000
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
  hosts:
    - name: backoffice-backend-service.petrus-software.com
      path: /

######################################################################
env:
  secret:
    DATABASE_URL: prd/backoffice-identify-service/DATABASE_URL
    RABBITMQ_PASSWORD: prd/backoffice-identify-service/RABBITMQ_PASSWORD
    KEYCLOAK_CLIENT_SECRET: prd/backoffice-identify-service/KEYCLOAK_CLIENT_SECRET
    AWS_ACCESS_KEY_ID: prd/backoffice-identify-service/AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: prd/backoffice-identify-service/AWS_SECRET_ACCESS_KEY
    KEYCLOAK_ADMIN_PASSWORD: prd/backoffice-identify-service/KEYCLOAK_ADMIN_PASSWORD
secretStoreRef:
  name: aws-auth-css
  kind: ClusterSecretStore

# secretStoreRef:
#   name: aws-auth-css
#   kind: ClusterSecretStore

hpa:
  enabled: false
  # minReplicas: {}
  # maxReplicas: {}
  # averageCpuUtilization: {}
  # averageMemUtilization: {}
# nodeSelector:
#   project: #ex: assaiclientes / marketplace
