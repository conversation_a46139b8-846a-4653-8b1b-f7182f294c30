import { Injectable } from '@nestjs/common';
import { v4 as uuid } from 'uuid';
import { Task } from 'src/core/domain/task.entity';
import { TaskStatus } from 'src/core/domain/task-status.enum';
import { TaskRepositoryPort } from 'src/core/domain/ports/task-repository.port';
import { CreateTaskDto } from './create-task.dto';
import { EventPublisherService } from 'src/infrastructure/events/event-publisher.service';

@Injectable()
export class CreateTaskUseCase {
  constructor(
    private readonly taskRepository: TaskRepositoryPort,
    private readonly eventPublisher: EventPublisherService,
  ) {}

  async execute(dto: CreateTaskDto): Promise<Task> {
    const id = uuid();
    const task = new Task(
      id,
      dto.userId,
      dto.title,
      dto.description || '',
      TaskStatus.PENDING,
      new Date(),
      new Date(),
    );

    await this.taskRepository.save(task);

    // Publica os eventos de domínio
    await this.eventPublisher.publishAll(task.domainEvents);

    // Limpa os eventos publicados
    task.clearEvents();

    return task;
  }
}
