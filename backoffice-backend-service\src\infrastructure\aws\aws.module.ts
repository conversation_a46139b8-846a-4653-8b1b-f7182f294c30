import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SQSService } from './sqs/sqs.service';
import { SNSService } from './sns/sns.service';
import { SESService } from './ses/ses.service';
import { S3StorageProvider } from './s3/s3-storage.provider';

@Module({
  imports: [ConfigModule],
  providers: [SQSService, SNSService, SESService, S3StorageProvider],
  exports: [SQSService, SNSService, SESService, S3StorageProvider],
})
export class AwsModule {}
