import { Module } from '@nestjs/common';
import { ContactsService } from './contacts.service';
import { ContactsController } from './contacts.controller';
import { PrismaCustomerContactRepository } from '@/infrastructure/repositories/prisma-customer-contact-repository';
import { CreateCustomerContactUseCase } from '@/core/application/use-cases/customer/create-customer-contact.use-case';
import { FindCustomerByUuidUseCase } from '../application/use-cases/find-customer-by-uuid.use-case';
import { CustomerRepository } from '../infrastructure/repositories/customer.repository';
import { PrismaServiceRepository } from '@/infrastructure/repositories/prisma-service.repository';
import { PrismaModule } from '@/infrastructure/prisma/prisma.module';
import { ListCustomerContactsUseCase } from '@/core/application/use-cases/customer/list-customer-contacts.use-case';
import { UsersModule } from '@/modules/users/users.module';
import { DeleteCustomerContactUseCase } from '@/core/application/use-cases/customer/delete-customer-contact.use-case';
import { UpdateCustomerContactUseCase } from '@/core/application/use-cases/customer/update-customer-contact.use-case';

@Module({
  imports: [PrismaModule, UsersModule],
  controllers: [ContactsController],
  providers: [ContactsService,
    CreateCustomerContactUseCase,
    ListCustomerContactsUseCase,
    FindCustomerByUuidUseCase,
    UpdateCustomerContactUseCase,
    DeleteCustomerContactUseCase,
    {
      provide: 'CustomerContactRepository',
      useClass: PrismaCustomerContactRepository,
    },
    {
      provide: 'ICustomerRepository',
      useClass: CustomerRepository,
    },
    {
      provide: 'SERVICE_REPOSITORY',
      useClass: PrismaServiceRepository,
    }
  ],
})
export class ContactsModule { }
