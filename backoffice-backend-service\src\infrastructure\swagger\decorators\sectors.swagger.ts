import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { CreateSectorDto } from '../../../modules/finance/sectors/dto/create-sector.dto';
import { UpdateSectorDto } from '../../../modules/finance/sectors/dto/update-sector.dto';
import { ListSectorsQueryDto } from '../../../modules/finance/sectors/dto/list-sectors-query.dto';

/**
 * Decorador para a documentação do endpoint de criação de setor
 */
export function ApiCreateSector() {
  return applyDecorators(
    ApiOperation({ summary: 'Create sector' }),
    ApiBody({
      type: CreateSectorDto,
      description: 'Sector data',
      examples: {
        example1: {
          value: {
            name: 'Administrativo',
            description:
              'Setor responsável pelas atividades administrativas da empresa',
            createdBy: '11111111-**************-************',
          },
          summary: 'Create a new sector',
        },
      },
    }),
    ApiResponse({
      status: 201,
      description: 'Setor criado com sucesso.',
    }),
    ApiResponse({
      status: 400,
      description: 'Dados inválidos fornecidos.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado.',
    }),
    ApiResponse({
      status: 403,
      description: 'Sem permissão.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de atualização de setor
 */
export function ApiUpdateSector() {
  return applyDecorators(
    ApiOperation({ summary: 'Update sector' }),
    ApiParam({
      name: 'uuid',
      description: 'Sector UUID',
      example: '11111111-**************-************',
    }),
    ApiBody({
      type: UpdateSectorDto,
      description: 'Sector update data',
      examples: {
        example1: {
          value: {
            name: 'Administrativo Atualizado',
            description: 'Descrição atualizada do setor administrativo',
          },
          summary: 'Update an existing sector',
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Setor atualizado com sucesso.',
    }),
    ApiResponse({
      status: 400,
      description: 'Dados inválidos fornecidos.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado.',
    }),
    ApiResponse({
      status: 403,
      description: 'Sem permissão.',
    }),
    ApiResponse({
      status: 404,
      description: 'Setor não encontrado.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de listagem de setores
 */
export function ApiListSectors() {
  return applyDecorators(
    ApiOperation({ summary: 'List sectors' }),
    ApiBody({
      type: ListSectorsQueryDto,
      required: false,
      description: 'Query parameters for filtering sectors',
    }),
    ApiResponse({
      status: 200,
      description: 'Lista de setores retornada com sucesso.',
    }),
    ApiResponse({
      status: 400,
      description: 'Parâmetros de consulta inválidos.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado.',
    }),
    ApiResponse({
      status: 403,
      description: 'Sem permissão.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de busca de setor por UUID
 */
export function ApiGetSector() {
  return applyDecorators(
    ApiOperation({ summary: 'Get sector by UUID' }),
    ApiParam({
      name: 'uuid',
      description: 'Sector UUID',
      example: '11111111-**************-************',
    }),
    ApiResponse({
      status: 200,
      description: 'Setor encontrado com sucesso.',
    }),
    ApiResponse({
      status: 400,
      description: 'UUID inválido.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado.',
    }),
    ApiResponse({
      status: 403,
      description: 'Sem permissão.',
    }),
    ApiResponse({
      status: 404,
      description: 'Setor não encontrado.',
    }),
  );
}

/**
 * Decorador para a documentação do endpoint de exclusão de setor
 */
export function ApiDeleteSector() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete sector' }),
    ApiParam({
      name: 'uuid',
      description: 'Sector UUID',
      example: '11111111-**************-************',
    }),
    ApiResponse({
      status: 204,
      description: 'Setor excluído com sucesso.',
    }),
    ApiResponse({
      status: 400,
      description: 'UUID inválido.',
    }),
    ApiResponse({
      status: 401,
      description: 'Não autorizado.',
    }),
    ApiResponse({
      status: 403,
      description: 'Sem permissão.',
    }),
    ApiResponse({
      status: 404,
      description: 'Setor não encontrado.',
    }),
  );
}
