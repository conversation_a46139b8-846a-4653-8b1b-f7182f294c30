import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { CreateEmployeeUseCase } from '@/core/application/use-cases/employee/create-employee.use-case';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { EmployeeResponseDto } from './dto/employee-response.dto';
import { Employee, PersonalDocument } from '@/core/domain/entities/employee.entity';
import { ListEmployeeQueryDto } from './dto/list-employee-query.dto';
import { PaginatedEmployeeResponseDto } from './dto/paginated-employee-response.dto';
import { ListEmployeeUseCase } from '@/core/application/use-cases/employee/list-employee.use-case';
import { ListEmployeeByUuidUseCase } from '@/core/application/use-cases/employee/list-employee-by-uuid.use-case';
import { DeleteEmployeeUseCase } from '@/core/application/use-cases/employee/delete-employee.use-case';
import { UpdateEmployeeUseCase } from '@/core/application/use-cases/employee/update-employee.use-case';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { calculateWorkJourney } from '@/core/domain/utils/work-journey.util';
import { CreateEmployeeArchiveUseCase } from '@/core/application/use-cases/employee/create-employee-archive.use-case';
import { ArchiveWithUrl, FindEmployeeArchiveByUuidUseCase } from '@/core/application/use-cases/employee/find-employee-archive-by-uuid.use-case';
import { FindOneEmployeeArchiveUseCase } from '@/core/application/use-cases/employee/find-one-employee-archive.use-case';
import { DeleteEmployeeArchiveUseCase } from '@/core/application/use-cases/employee/delete-employee-archive.use-case';
import { EmployeeArchiveResponseDto } from './dto/employee-archive-response.dto';
import { Archive } from '@prisma/client';
import { EmployeeArchiveDeleteResponseDto } from './dto/employee-archive-delete-response.dto';
import { EmployeeArchivesResponseDto } from './dto/employee-archives-response.dto';
import { PersonalDocumentWithUrl, UploadEmployeePersonalDocumentUseCase } from '@/core/application/use-cases/employee/upload-employee-personal-document.use-case';
import { PersonalDocumentResponseDto } from './dto/employee-personal-document-response.dto';
import { PersonalDocumentsResponseDto } from './dto/employee-personal-documents-response.dto';
import { ListPersonalDocumentsByEmployeeUuidUseCase } from '@/core/application/use-cases/employee/list-personal-documents-by-employee-uuid.use-case';
import { DeleteEmployeePersonalDocumentUseCase } from '@/core/application/use-cases/employee/delete-employee-personal-document.use-case';

@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    private readonly createEmployeeUseCase: CreateEmployeeUseCase,
    private readonly listEmployeeUseCase: ListEmployeeUseCase,
    private readonly listEmployeeByUuidUseCase: ListEmployeeByUuidUseCase,
    private readonly updateEmployeeUseCase: UpdateEmployeeUseCase,
    private readonly deleteEmployeeUseCase: DeleteEmployeeUseCase,
    private readonly createEmployeeArchiveUseCase: CreateEmployeeArchiveUseCase,
    private readonly findEmployeeArchiveByUuidUseCase: FindEmployeeArchiveByUuidUseCase,
    private readonly findOneEmployeeArchiveUseCase: FindOneEmployeeArchiveUseCase,
    private readonly deleteEmployeeArchiveUseCase: DeleteEmployeeArchiveUseCase,
    private readonly uploadEmployeePersonalDocumentUseCase: UploadEmployeePersonalDocumentUseCase,
    private readonly listPersonalDocumentsByEmployeeUuidUseCase: ListPersonalDocumentsByEmployeeUuidUseCase,
    private readonly deleteEmployeePersonalDocumentUseCase: DeleteEmployeePersonalDocumentUseCase,
  ) { }

  private mapToResponseDto(employee: Employee): EmployeeResponseDto {
    const response = {
      uuid: employee.uuid,
      name: employee.name,
      email: employee.email,
      position: employee.position,
      department: employee.department,
      hireDate: employee.hireDate.toISOString(),
      address: employee.address,
      personalDocuments: employee.personalDocuments,
      dependents: employee.dependents,
      status: employee.status,
      workSchedule: employee.workSchedule,
      shift: employee.shift,
      grossSalary: employee.grossSalary,
      mealAllowance: employee.mealAllowance,
      transportAllowance: employee.transportAllowance,
      healthPlan: employee.healthPlan,
      contractType: employee.contractType,
      seniority: employee.seniority,
      phone: employee.phone,
      birthDate: employee.birthDate?.toISOString(),
      workHours: employee.workHours,
      overtimeBank: employee.overtimeBank,
      vacations: employee.vacations,
      createdBy: employee.createdBy,
      updatedBy: employee.updatedBy,
      createdAt: employee.createdAt.toISOString(),
      updatedAt: employee.updatedAt.toISOString(),
      userId: employee.userId,
    } as EmployeeResponseDto;

    if (employee.workSchedule) {
      response.workJourney = calculateWorkJourney(employee.workSchedule);
    }

    return response;
  }

  private mapToArchiveResponseDto(archive: ArchiveWithUrl): EmployeeArchiveResponseDto {
    return {
      id: archive.id,
      employeeUuid: archive.employeeUuid,
      fileName: archive.fileName,
      downloadUrl: archive.downloadUrl,
    } as EmployeeArchiveResponseDto;
  }

  private mapToPersonalDocumentResponseDto(personalDocument: PersonalDocumentWithUrl): PersonalDocumentResponseDto {
    return {
      type: personalDocument.type,
      number: personalDocument.number,
      issueDate: personalDocument.issueDate,
      fileName: personalDocument.fileName,
      downloadUrl: personalDocument.downloadUrl,
    } as PersonalDocumentResponseDto;
  }

  private mapToArchiveDeleteResponseDto(archive: Archive): EmployeeArchiveDeleteResponseDto {
    return {
      id: archive.id,
      deletedAt: archive.deletedAt?.toISOString(),
    } as EmployeeArchiveDeleteResponseDto;
  }

  private mapToArchivesResponseDto(archives: ArchiveWithUrl[]): EmployeeArchivesResponseDto {
    return {
      archives: archives.map((archive) => this.mapToArchiveResponseDto(archive)),
    } as EmployeeArchivesResponseDto;
  }

  private mapToPersonalDocumentsResponseDto(personalDocuments: PersonalDocumentWithUrl[]): PersonalDocumentsResponseDto {
    return {
      personalDocuments: personalDocuments.map((personalDocument) => this.mapToPersonalDocumentResponseDto(personalDocument)),
    } as PersonalDocumentsResponseDto;
  }

  async create(
    createEmployeeDto: CreateEmployeeDto,
  ): Promise<EmployeeResponseDto> {
    const employee =
      await this.createEmployeeUseCase.execute(createEmployeeDto);
    return this.mapToResponseDto(employee);
  }

  async findAll(
    query: ListEmployeeQueryDto,
  ): Promise<PaginatedEmployeeResponseDto> {
    this.logger.log('findAll called with query: ' + JSON.stringify(query));
    try {
      const result = await this.listEmployeeUseCase.execute({
        limit: query.limit ?? 10,
        offset: query.offset ?? 0,
        name: query.name,
        email: query.email,
      });
      this.logger.log('listEmployeeUseCase result: ' + JSON.stringify(result));
      const response = {
        items: result.items.map((employee) => this.mapToResponseDto(employee)),
        total: result.total,
        limit: query.limit ?? 10,
        offset: query.offset ?? 0,
      };
      this.logger.log('Returning response: ' + JSON.stringify(response));
      return response;
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error('Error in findAll:', error.stack || error.message);
        console.error(error);
      } else {
        this.logger.error('Error in findAll:', JSON.stringify(error));
        console.error(error);
      }
      throw error;
    }
  }

  async findByUuid(uuid: string): Promise<EmployeeResponseDto> {
    const employee = await this.listEmployeeByUuidUseCase.execute(uuid);
    if (!employee) {
      throw new NotFoundException('Colaborador não encontrado');
    }
    return this.mapToResponseDto(employee);
  }

  async update(
    uuid: string,
    updateEmployeeDto: UpdateEmployeeDto,
    userId: string,
  ): Promise<EmployeeResponseDto> {
    const employee = await this.updateEmployeeUseCase.execute(
      uuid,
      updateEmployeeDto,
      userId,
    );
    return this.mapToResponseDto(employee);
  }

  async delete(uuid: string): Promise<void> {
    await this.deleteEmployeeUseCase.execute({ uuid });
  }

  async createArchive(employeeUuid: string, file: Express.Multer.File, userId: string): Promise<EmployeeArchiveResponseDto> {
    const archive = await this.createEmployeeArchiveUseCase.execute(employeeUuid, file, userId);
    return this.mapToArchiveResponseDto(archive);
  }

  async uploadPersonalDocument(uuid: string, number: string, file: Express.Multer.File): Promise<PersonalDocumentResponseDto> {
    const personalDocument = await this.uploadEmployeePersonalDocumentUseCase.execute(uuid, number, file);
    return this.mapToPersonalDocumentResponseDto(personalDocument);
  }

  async listPersonalDocuments(uuid: string): Promise<PersonalDocumentsResponseDto> {
    const personalDocuments = await this.listPersonalDocumentsByEmployeeUuidUseCase.execute(uuid);
    return this.mapToPersonalDocumentsResponseDto(personalDocuments);
  }

  async deletePersonalDocument(uuid: string, number: string): Promise<void> {
    await this.deleteEmployeePersonalDocumentUseCase.execute(uuid, number);
  }

  async findArchives(employeeUuid: string): Promise<EmployeeArchivesResponseDto> {
    const archives = await this.findEmployeeArchiveByUuidUseCase.execute(employeeUuid);
    return this.mapToArchivesResponseDto(archives);
  }

  async findArchive(id: string): Promise<EmployeeArchiveResponseDto> {
    const archive = await this.findOneEmployeeArchiveUseCase.execute(id);
    return this.mapToArchiveResponseDto(archive);
  }

  async deleteArchive(id: string): Promise<EmployeeArchiveDeleteResponseDto> {
    await this.deleteEmployeeArchiveUseCase.execute(id);
    return {
      id,
      deletedAt: new Date().toISOString(),
    } as EmployeeArchiveDeleteResponseDto;
  }
}
