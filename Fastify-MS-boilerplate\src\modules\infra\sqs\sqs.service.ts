import { SendMessageCommand, ReceiveMessageCommand, DeleteMessageCommand, GetQueueUrlCommand } from '@aws-sdk/client-sqs';
import { createSqsClient } from './client/sqs-client';
import { MissingResourceException } from '@/shared/errors/custom-exceptions';

const sqsClient = createSqsClient();

/**
 * Busca a URL da fila SQS a partir do nome da fila.
 * Lança MissingResourceException se a fila não for encontrada.
 * @param queueName Nome da fila SQS
 * @returns URL da fila SQS
 */
async function getQueueUrl(queueName: string): Promise<string> {
  const command = new GetQueueUrlCommand({ QueueName: queueName });
  const response = await sqsClient.send(command);
  if (!response.QueueUrl) {
    throw new MissingResourceException(`Queue URL not found for queue name: ${queueName}`);
  }
  return response.QueueUrl;
}

/**
 * Envia uma mensagem para a fila SQS especificada.
 * @param queueName Nome da fila SQS
 * @param messageBody Corpo da mensagem a ser enviada
 * @param options Opções adicionais do SQS (opcional)
 * @returns Resposta do AWS SQS
 */
export async function sendMessage(queueName: string, messageBody: string, options: Record<string, any> = {}) {
  const queueUrl = await getQueueUrl(queueName);
  const command = new SendMessageCommand({
    QueueUrl: queueUrl,
    MessageBody: messageBody,
    ...options,
  });
  return sqsClient.send(command);
}

/**
 * Recebe mensagens da fila SQS especificada.
 * @param queueName Nome da fila SQS
 * @param options Opções adicionais do SQS (opcional)
 * @returns Resposta do AWS SQS contendo as mensagens
 */
export async function receiveMessages(queueName: string, options: Record<string, any> = {}) {
  const queueUrl = await getQueueUrl(queueName);
  const command = new ReceiveMessageCommand({
    QueueUrl: queueUrl,
    ...options,
  });
  return sqsClient.send(command);
}

/**
 * Deleta uma mensagem da fila SQS especificada, usando o receipt handle.
 * @param queueName Nome da fila SQS
 * @param receiptHandle Receipt handle da mensagem a ser deletada
 * @returns Resposta do AWS SQS
 */
export async function deleteMessage(queueName: string, receiptHandle: string) {
  const queueUrl = await getQueueUrl(queueName);
  const command = new DeleteMessageCommand({
    QueueUrl: queueUrl,
    ReceiptHandle: receiptHandle,
  });
  return sqsClient.send(command);
} 