import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  constructor(private configService: ConfigService) {
    super({
      datasources: {
        db: {
          url: configService.get<string>('DATABASE_URL'),
        },
      },
      log:
        configService.get('NODE_ENV') === 'development'
          ? ['query', 'info', 'warn', 'error']
          : ['error'],
    });
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }

  async cleanDatabase(): Promise<unknown> {
    if (this.configService.get('NODE_ENV') === 'production') {
      return Promise.resolve();
    }

    // Usado para testes
    const models = Reflect.ownKeys(this).filter((key) => {
      return (
        typeof key === 'string' &&
        !key.startsWith('_') &&
        !['$connect', '$disconnect', '$on', '$transaction', '$use'].includes(
          key,
        )
      );
    });

    try {
      return Promise.all(
        models.map((modelKey) => {
          const model = this[modelKey as keyof this] as {
            deleteMany: () => Promise<unknown>;
          };
          return model.deleteMany();
        }),
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      throw new Error(`Erro ao limpar o banco de dados: ${errorMessage}`);
    }
  }
}
