import { DomainEvent } from './domain-event';
import { Task } from '../task.entity';

export class TaskCompletedEvent extends DomainEvent {
  private readonly task: Task;

  constructor(
    task: Task,
    metadata?: {
      correlationId?: string;
      userId?: string;
    },
  ) {
    super('task.completed', {
      correlationId: metadata?.correlationId,
      userId: metadata?.userId || task.userId,
    });

    this.task = task;
  }

  getData(): object {
    return {
      taskId: this.task.id,
      userId: this.task.userId,
      title: this.task.title,
      completedAt: this.task.updatedAt.toISOString(),
    };
  }
}
