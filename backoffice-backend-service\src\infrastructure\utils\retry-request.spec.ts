import { UnauthorizedException } from '@nestjs/common';
import { AxiosError, AxiosHeaders, AxiosResponse } from 'axios';
import { Test, TestingModule } from '@nestjs/testing';
import { RequestUtilsService } from './request.utils.service';
import { ConfigService } from '@nestjs/config';

// Mock para axios
//jest.mock('axios');
//const axiosMock = axios as jest.Mocked<typeof axios>;

describe('RequestUtilsService', () => {
  let requestUtils: RequestUtilsService;
  let _configService: ConfigService;

  beforeEach(async () => {
    jest.setTimeout(60000);
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RequestUtilsService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              switch (key) {
                case 'MAX_RETRIES':
                  return 3;
                case 'INITIAL_RETRY_DELAY_MS':
                  return 100;
                default:
                  return null;
              }
            }),
          },
        },
      ],
    }).compile();
    requestUtils = module.get<RequestUtilsService>(RequestUtilsService);
    _configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('executeWithRetry', () => {
    it('should succeed on the third attempt after two retryable failures', async () => {
      // Arrange
      const mockRequest = jest.fn();
      const mockResponse: AxiosResponse = {
        data: {
          example: 'data',
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {
          headers: new AxiosHeaders(),
        },
      };

      // Simula falha de rede (sem resposta) nas duas primeiras tentativas
      const networkError = new AxiosError(
        'Network Error',
        'ERR_NETWORK',
        {
          headers: new AxiosHeaders(),
        },
        null,
        {
          data: null,
          status: 0,
          statusText: '',
          headers: {},
          config: {
            headers: new AxiosHeaders(),
          },
        },
      );
      mockRequest
        .mockRejectedValueOnce(networkError) // 1ª tentativa: falha
        .mockRejectedValueOnce(networkError) // 2ª tentativa: falha
        .mockResolvedValueOnce(mockResponse); // 3ª tentativa: sucesso

      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');

      // Act
      const result = await requestUtils.executeWithRetry(
        mockRequest,
        jest.fn(),
      );

      // Assert
      expect(mockRequest).toHaveBeenCalledTimes(3);
      expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 100); // Delay após 1ª falha
      expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 200); // Delay após 2ª falha
      expect(result).toEqual(mockResponse.data);
      setTimeoutSpy.mockRestore();
    });

    it('should throw exception for non-retryable error and call exceptionfn', async () => {
      // Arrange
      const mockRequest = jest.fn();
      const nonRetryableError = new AxiosError(
        'Unauthorized',
        'ERR_BAD_REQUEST',
        {
          headers: new AxiosHeaders(),
        },
        null,
        {
          status: 401,
          statusText: 'Unauthorized',
          headers: {},
          config: {
            headers: new AxiosHeaders(),
          },
          data: {},
        },
      );
      mockRequest.mockRejectedValueOnce(nonRetryableError);

      const exceptionFn = jest.fn((_error) => {
        throw new UnauthorizedException('Erro não recuperável');
      });
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');

      // Act & Assert
      await expect(
        requestUtils.executeWithRetry(mockRequest, exceptionFn),
      ).rejects.toThrow(UnauthorizedException);
      expect(mockRequest).toHaveBeenCalledTimes(1);
      expect(exceptionFn).toHaveBeenCalledWith(nonRetryableError);
      expect(setTimeoutSpy).not.toHaveBeenCalled();
      setTimeoutSpy.mockRestore();
    });

    it('should throw UnauthorizedException after max retries on retryable errors', async () => {
      // Arrange
      const mockRequest = jest.fn();
      const serverError = new AxiosError(
        'Internal Server Error',
        'ERR_BAD_RESPONSE',
        {
          headers: new AxiosHeaders(),
        },
        null,
        {
          status: 500,
          statusText: 'Internal Server Error',
          headers: {},
          config: {
            headers: new AxiosHeaders(),
          },
          data: {},
        },
      );
      mockRequest.mockRejectedValue(serverError); // Falha em todas as tentativas

      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');

      // Act & Assert
      await expect(
        requestUtils.executeWithRetry(mockRequest, jest.fn()),
      ).rejects.toThrow('Falha após tentativas máximas');
      expect(mockRequest).toHaveBeenCalledTimes(3);
      expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 100);
      expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 200);
      setTimeoutSpy.mockRestore();
    });
  });

  describe('isRetryableError', () => {
    it('should return true for network errors (no response)', () => {
      const networkError = new AxiosError(
        'Network Error',
        'ERR_NETWORK',
        {
          headers: new AxiosHeaders(),
        },
        null,
        {
          data: null,
          status: 0,
          statusText: '',
          headers: {},
          config: {
            headers: new AxiosHeaders(),
          },
        },
      );
      expect(requestUtils.isRetryableError(networkError)).toBe(true);
    });

    it('should return true for 5xx errors', () => {
      const serverError = new AxiosError(
        'Internal Server Error',
        'ERR_BAD_RESPONSE',
        {
          headers: new AxiosHeaders(),
        },
        null,
        {
          status: 500,
          statusText: 'Internal Server Error',
          headers: {},
          config: {
            headers: new AxiosHeaders(),
          },
          data: {},
        },
      );
      expect(requestUtils.isRetryableError(serverError)).toBe(true);
    });

    it('should return false for 4xx errors', () => {
      const clientError = new AxiosError(
        'Unauthorized',
        'ERR_BAD_REQUEST',
        {
          headers: new AxiosHeaders(),
        },
        null,
        {
          status: 401,
          statusText: 'Unauthorized',
          headers: {},
          config: {
            headers: new AxiosHeaders(),
          },
          data: {},
        },
      );
      expect(requestUtils.isRetryableError(clientError)).toBe(false);
    });

    it('should return false for non-Axios errors', () => {
      const genericError = new Error('Generic error');
      expect(requestUtils.isRetryableError(genericError)).toBe(false);
    });
  });
});
