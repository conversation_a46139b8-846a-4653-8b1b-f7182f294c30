/*
  Warnings:

  - The primary key for the `customer_payment_preferences` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `billingPreference` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - You are about to drop the column `customerUuid` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - You are about to drop the column `departmentId` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - You are about to drop the column `paymentCondition` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - You are about to drop the column `paymentMethodId` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - You are about to alter the column `responsible` on the `customer_payment_preferences` table. The data in that column could be lost. The data in that column will be cast from `VarChar(255)` to `VarChar(100)`.
  - Added the required column `billing_preference` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cost_center_id` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.
  - Added the required column `customer_id` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.
  - Added the required column `department` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.
  - Added the required column `payment_condition` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.
  - Added the required column `payment_method_id` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.
  - Added the required column `sector_id` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "core"."customer_payment_preferences" DROP CONSTRAINT "customer_payment_preferences_customerUuid_fkey";

-- DropForeignKey
ALTER TABLE "core"."customer_payment_preferences" DROP CONSTRAINT "customer_payment_preferences_departmentId_fkey";

-- DropForeignKey
ALTER TABLE "core"."customer_payment_preferences" DROP CONSTRAINT "customer_payment_preferences_paymentMethodId_fkey";

-- AlterTable
ALTER TABLE "core"."customer_payment_preferences" DROP CONSTRAINT "customer_payment_preferences_pkey",
DROP COLUMN "billingPreference",
DROP COLUMN "customerUuid",
DROP COLUMN "departmentId",
DROP COLUMN "paymentCondition",
DROP COLUMN "paymentMethodId",
ADD COLUMN     "billing_preference" VARCHAR(100) NOT NULL,
ADD COLUMN     "cost_center_id" TEXT NOT NULL,
ADD COLUMN     "customer_id" TEXT NOT NULL,
ADD COLUMN     "department" VARCHAR(100) NOT NULL,
ADD COLUMN     "payment_condition" VARCHAR(100) NOT NULL,
ADD COLUMN     "payment_method_id" TEXT NOT NULL,
ADD COLUMN     "sector_id" TEXT NOT NULL,
ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "responsible" SET DATA TYPE VARCHAR(100),
ADD CONSTRAINT "customer_payment_preferences_pkey" PRIMARY KEY ("id");
DROP SEQUENCE IF EXISTS "customer_payment_preferences_id_seq";

-- CreateIndex
CREATE INDEX "customer_payment_preferences_customer_id_idx" ON "core"."customer_payment_preferences"("customer_id");

-- CreateIndex
CREATE INDEX "customer_payment_preferences_payment_method_id_idx" ON "core"."customer_payment_preferences"("payment_method_id");

-- CreateIndex
CREATE INDEX "customer_payment_preferences_cost_center_id_idx" ON "core"."customer_payment_preferences"("cost_center_id");

-- CreateIndex
CREATE INDEX "customer_payment_preferences_sector_id_idx" ON "core"."customer_payment_preferences"("sector_id");

-- AddForeignKey
ALTER TABLE "core"."customer_payment_preferences" ADD CONSTRAINT "customer_payment_preferences_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "core"."customers"("uuid") ON DELETE RESTRICT ON UPDATE CASCADE;
