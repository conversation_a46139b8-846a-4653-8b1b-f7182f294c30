import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

export class ListCostCentersDto {
  @ApiProperty({
    description: 'Number of cost centers to return',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number;

  @ApiProperty({
    description: 'Number of cost centers to skip',
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  offset?: number;

  @ApiProperty({
    description: 'Filter by description',
    example: 'Marketing',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}
