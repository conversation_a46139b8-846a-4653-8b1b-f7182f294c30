import { Test, TestingModule } from '@nestjs/testing';
import { COST_CENTER_REPOSITORY } from '@core/ports/repositories/cost-center-repository.port';
import { DeleteCostCenterUseCase } from '../delete-cost-center.use-case';
import { NotFoundException } from '@nestjs/common';
import { CostCenter } from '@/core/domain/cost-center/entities/cost-center.entity';

type MockCostCenterRepository = {
  delete: jest.Mock;
  findByUuid: jest.Mock;
};

describe('DeleteCostCenterUseCase', () => {
  let useCase: DeleteCostCenterUseCase;
  let costCenterRepository: MockCostCenterRepository;

  beforeEach(async () => {
    const mockCostCenterRepository: MockCostCenterRepository = {
      delete: jest.fn(),
      findByUuid: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeleteCostCenterUseCase,
        {
          provide: COST_CENTER_REPOSITORY,
          useValue: mockCostCenterRepository,
        },
      ],
    }).compile();

    useCase = module.get<DeleteCostCenterUseCase>(DeleteCostCenterUseCase);
    costCenterRepository = module.get(COST_CENTER_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should delete a cost center successfully', async () => {
      // Arrange
      const uuid = '11111111-**************-************';

      costCenterRepository.delete.mockResolvedValue(null);
      costCenterRepository.findByUuid.mockResolvedValue(
        new CostCenter({
          id: uuid,
          description: 'Test Cost Center',
          createdBy: 'test',
          updatedBy: 'test',
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      );

      // Act
      await useCase.execute(uuid);

      // Assert
      expect(costCenterRepository.delete).toHaveBeenCalledWith(uuid);
    });

    it('should throw an error if cost center is not found', async () => {
      // Arrange
      const uuid = '11111111-**************-************';

      costCenterRepository.findByUuid.mockResolvedValue(null);
      costCenterRepository.delete.mockResolvedValue(null);

      // Act & Assert
      await expect(useCase.execute(uuid)).rejects.toThrow(NotFoundException);
    });

    it('should throw an error if repository throws', async () => {
      // Arrange
      const uuid = '11111111-**************-************';

      const error = new Error('Repository error');
      costCenterRepository.findByUuid.mockResolvedValue(
        new CostCenter({
          id: uuid,
          description: 'Test Cost Center',
          createdBy: 'test',
          updatedBy: 'test',
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      );
      costCenterRepository.delete.mockRejectedValue(error);

      // Act & Assert
      await expect(useCase.execute(uuid)).rejects.toThrow(error);
    });
  });
});
