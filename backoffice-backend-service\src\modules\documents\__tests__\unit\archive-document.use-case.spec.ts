import { NotFoundException, BadRequestException } from '@nestjs/common';
import { ArchiveDocumentUseCase } from '../../application/use-cases/archive-documento.use-case';
import { IDocumentRepository } from '../../domain/repositories/document.repository.interface';
import { EntityType } from '../../domain/enums/entity-type.enum';
import { Document } from '../../domain/entities/document.entity';
import { DocumentStatus } from '../../domain/enums/document-status.enum';

describe('ArchiveDocumentUseCase', () => {
  let useCase: ArchiveDocumentUseCase;
  let documentRepository: jest.Mocked<IDocumentRepository>;

  const uuid = 'd290f1ee-6c54-4b01-90e6-d701748f0851';
  const archivedBy = '123e4567-e89b-12d3-a456-************';
  const now = new Date();

  beforeEach(() => {
    documentRepository = {
      findByUuid: jest.fn(),
      updateStatusToArchived: jest.fn<Promise<void>, [string, string, Date]>(),
      create: jest.fn(),
      list: jest.fn(),
    };

    useCase = new ArchiveDocumentUseCase(documentRepository);
  });

  it('deve lançar NotFoundException se o documento não for encontrado', async () => {
    documentRepository.findByUuid.mockResolvedValue(null);

    await expect(useCase.execute(uuid, archivedBy, now)).rejects.toThrow(
      NotFoundException,
    );
  });

  it('deve lançar BadRequestException se o documento já estiver arquivado', async () => {
    const archivedDocument: Document = {
      uuid,
      entityType: EntityType.CLIENT,
      entityUuid: 'entity-uuid',
      currentVersion: 1,
      status: DocumentStatus.ARCHIVED,
      uploadedBy: 'user-uuid',
      createdAt: now,
      updatedAt: now,
      updatedBy: archivedBy,
      versions: [],
    };

    documentRepository.findByUuid.mockResolvedValue(archivedDocument);

    await expect(useCase.execute(uuid, archivedBy, now)).rejects.toThrow(
      BadRequestException,
    );
  });

  it('deve chamar updateStatusToArchived quando o documento estiver ativo', async () => {
    const activeDocument: Document = {
      uuid,
      entityType: EntityType.CLIENT,
      entityUuid: 'entity-uuid',
      currentVersion: 1,
      status: DocumentStatus.ACTIVE,
      uploadedBy: 'user-uuid',
      createdAt: now,
      updatedAt: now,
      updatedBy: undefined,
      versions: [],
    };

    documentRepository.findByUuid.mockResolvedValue(activeDocument);

    await useCase.execute(uuid, archivedBy, now);

    expect(documentRepository.updateStatusToArchived.mock.calls[0]).toEqual([
      uuid,
      archivedBy,
      now,
    ]);

    expect(documentRepository.updateStatusToArchived.mock.calls.length).toBe(1);
  });
});
