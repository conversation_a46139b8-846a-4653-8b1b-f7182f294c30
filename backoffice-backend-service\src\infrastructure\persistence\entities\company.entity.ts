import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CompanyStatus } from '../../../core/domain/company.entity';

@Entity('companies')
export class CompanyEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'uuid', unique: true })
  uuid: string;

  @Column({ length: 100 })
  razaoSocial: string;

  @Column({ length: 14, unique: true })
  cnpj: string;

  @Column({ type: 'jsonb' })
  address: {
    street: string;
    city: string;
    zipCode: string;
    state: string;
  };

  @Column({ length: 15 })
  phone: string;

  @Column({ length: 255 })
  email: string;

  @Column({ length: 10 })
  status: CompanyStatus;

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @Column({ length: 50 })
  createdBy: string;

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;

  @Column({ length: 50 })
  updatedBy: string;
}
