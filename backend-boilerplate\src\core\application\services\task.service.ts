import { TaskRepository } from '../../ports/repositories/task-repository.interface';
import { CreateTaskUseCase } from '../use-cases/task/create-task.use-case';
import { UpdateTaskUseCase } from '../use-cases/task/update-task.use-case';
import { GetUserTasksUseCase } from '../use-cases/task/get-user-tasks.use-case';
import { DeleteTaskUseCase } from '../use-cases/task/delete-task.use-case';
import { Task } from '../../domain/task.entity';
import { TaskStatus } from '../../domain/task-status.enum';

export class TaskService {
  private createTaskUseCase: CreateTaskUseCase;
  private updateTaskUseCase: UpdateTaskUseCase;
  private getUserTasksUseCase: GetUserTasksUseCase;
  private deleteTaskUseCase: DeleteTaskUseCase;

  constructor(private taskRepository: TaskRepository) {
    this.createTaskUseCase = new CreateTaskUseCase(taskRepository);
    this.updateTaskUseCase = new UpdateTaskUseCase(taskRepository);
    this.getUserTasksUseCase = new GetUserTasksUseCase(taskRepository);
    this.deleteTaskUseCase = new DeleteTaskUseCase(taskRepository);
  }

  // Métodos que delegam para os casos de uso

  async findAll(): Promise<Task[]> {
    return this.taskRepository.findAll();
  }

  async findById(id: string): Promise<Task | null> {
    return this.taskRepository.findById(id);
  }

  async findByUserId(userId: string): Promise<Task[]> {
    return this.getUserTasksUseCase.execute({ userId });
  }

  async create(
    userId: string,
    title: string,
    description?: string,
  ): Promise<Task> {
    return this.createTaskUseCase.execute({ userId, title, description });
  }

  async update(
    id: string,
    title?: string,
    description?: string,
    status?: TaskStatus,
  ): Promise<Task> {
    return this.updateTaskUseCase.execute({ id, title, description, status });
  }

  async delete(id: string): Promise<void> {
    return this.deleteTaskUseCase.execute({ id });
  }
}
