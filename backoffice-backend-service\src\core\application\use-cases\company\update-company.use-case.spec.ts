import { Test, TestingModule } from '@nestjs/testing';
import { UpdateCompanyUseCase } from './update-company.use-case';
import { CompanyRepository } from '../../../ports/repositories/company-repository.interface';
import { Company, CompanyStatus } from '../../../domain/company.entity';
import { COMPANY_REPOSITORY } from '../../../ports/repositories/tokens';
import { NotFoundException, ConflictException } from '@nestjs/common';

describe('UpdateCompanyUseCase', () => {
  let useCase: UpdateCompanyUseCase;
  let companyRepository: jest.Mocked<CompanyRepository>;

  const mockCompany = new Company(
    1,
    '550e8400-e29b-41d4-a716-************',
    'Empresa Teste',
    '12345678000195',
    {
      street: 'Rua Teste',
      city: 'São Paulo',
      zipCode: '01234-567',
      state: 'SP',
    },
    '(11) 98765-4321',
    '<EMAIL>',
    CompanyStatus.ACTIVE,
    'ADMIN',
  );

  beforeEach(async () => {
    companyRepository = {
      findByUuid: jest.fn(),
      findByCnpj: jest.fn(),
      update: jest.fn(),
    } as unknown as jest.Mocked<CompanyRepository>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateCompanyUseCase,
        {
          provide: COMPANY_REPOSITORY,
          useValue: companyRepository,
        },
      ],
    }).compile();

    useCase = module.get<UpdateCompanyUseCase>(UpdateCompanyUseCase);
  });

  it('deve atualizar parcialmente uma empresa', async () => {
    companyRepository.findByUuid.mockResolvedValue(mockCompany);
    companyRepository.update.mockResolvedValue(mockCompany);

    const result = await useCase.execute({
      uuid: mockCompany.uuid,
      phone: '(11) 99999-0000',
      status: CompanyStatus.INACTIVE,
      updatedBy: 'ADMIN',
    });

    expect(result.company).toBe(mockCompany);
    expect(companyRepository.findByUuid.mock.calls[0][0]).toBe(
      mockCompany.uuid,
    );
    expect(companyRepository.update.mock.calls.length).toBeGreaterThan(0);
  });

  it('não deve atualizar empresa inexistente', async () => {
    companyRepository.findByUuid.mockResolvedValue(null);

    await expect(
      useCase.execute({
        uuid: '550e8400-e29b-41d4-a716-************',
        phone: '(11) 99999-0000',
        updatedBy: 'ADMIN',
      }),
    ).rejects.toThrow(NotFoundException);
  });

  it('não deve atualizar empresa com CNPJ duplicado', async () => {
    companyRepository.findByUuid.mockResolvedValue(mockCompany);
    companyRepository.findByCnpj.mockResolvedValue(
      new Company(
        2,
        '550e8400-e29b-41d4-a716-************',
        'Outra Empresa',
        '98765432000195',
        {
          street: 'Rua Outra',
          city: 'São Paulo',
          zipCode: '01234-567',
          state: 'SP',
        },
        '(11) 98765-4321',
        '<EMAIL>',
        CompanyStatus.ACTIVE,
        'ADMIN',
      ),
    );

    await expect(
      useCase.execute({
        uuid: mockCompany.uuid,
        cnpj: '98765432000195',
        updatedBy: 'ADMIN',
      }),
    ).rejects.toThrow(ConflictException);
  });
});
