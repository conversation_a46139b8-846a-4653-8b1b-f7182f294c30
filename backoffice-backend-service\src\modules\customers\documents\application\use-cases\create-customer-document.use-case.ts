import { Injectable, Inject } from '@nestjs/common';
import { ICustomerDocumentRepository } from '../../domain/repositories/customer-document.repository.interface';
import { CustomerDocument } from '../../domain/entities/customer-document.entity';
import { CUSTOMER_DOCUMENT_REPOSITORY } from '../../domain/constants/tokens';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { v4 as uuidv4 } from 'uuid';
import { CreateCustomerDocumentsDto } from '../../domain/dtos/customer-document.dto';

export interface CreateCustomerDocumentDto {
  customerUuid: string;
  name: string;
  file: Express.Multer.File;
}

@Injectable()
export class CreateCustomerDocumentUseCase {
  constructor(
    @Inject(CUSTOMER_DOCUMENT_REPOSITORY)
    private readonly customerDocumentRepository: ICustomerDocumentRepository,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  async execute(data: CreateCustomerDocumentDto): Promise<CustomerDocument> {
    // Gerar UUID único para o documento
    const documentUuid = uuidv4();
    
    // Definir o caminho no S3 - seguindo padrão similar aos contratos
    const fileName = data.file.originalname;
    const key = `customer/${data.customerUuid}/documents/${documentUuid}/${fileName}`;
    
    // Upload do arquivo para o S3
    await this.storageProvider.upload(data.file.buffer, data.file.mimetype, key);

    // Criar o documento no banco com a chave do S3
    const document = await this.customerDocumentRepository.create({
      customerUuid: data.customerUuid,
      name: data.name,
      url: key, // Armazenar a chave S3 em vez do base64
      fileName: fileName,
    });

    return document;
  }

  async executeMultiple(
    customerUuid: string,
    files: Express.Multer.File[],
    userId: string,
    createDocumentsDto: CreateCustomerDocumentsDto,
  ): Promise<CustomerDocument[]> {
    const results: CustomerDocument[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const metadata = createDocumentsDto.documents[i];

      const documentUuid = uuidv4();
      const fileName = file.originalname;
      const key = `customer/${customerUuid}/documents/${documentUuid}/${fileName}`;
      
      // Upload do arquivo para o S3
      await this.storageProvider.upload(file.buffer, file.mimetype, key);

      // Criar o documento no banco com metadados completos
      const document = await this.customerDocumentRepository.createWithMetadata({
        customerUuid,
        name: fileName, // Usar o nome original do arquivo
        url: key,
        fileName: fileName,
        responsible: metadata.responsible,
        department: metadata.department,
        description: metadata.description,
        expirationDate: metadata.expirationDate,
        uploadedBy: userId,
      });

      results.push(document);
    }

    return results;
  }
}
