import { ApiProperty } from '@nestjs/swagger';

export class PaymentMethodResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the payment method',
    example: '123e4567-e89b-12d3-a456-************',
  })
  uuid: string;

  @ApiProperty({
    description: 'Label of the payment method',
    example: 'Cartão de Crédito',
  })
  label: string;

  @ApiProperty({
    description: 'Description of the payment method',
    example: 'Pagamento realizado através de cartão de crédito',
  })
  description: string;

  @ApiProperty({ example: '2025-04-30T23:00:00Z' })
  createdAt: Date;

  @ApiProperty({ example: 'admin-user' })
  createdBy: string;

  @ApiProperty({ example: '2025-04-30T23:00:00Z' })
  updatedAt: Date;

  @ApiProperty({ example: 'admin-user' })
  updatedBy: string;
}
