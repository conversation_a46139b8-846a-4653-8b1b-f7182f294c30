/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-argument */

/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ConfigService } from '@nestjs/config';
import * as CircuitBreaker from 'opossum';

export function Breakable() {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const configService = this.configService as ConfigService; // Assumes ConfigService is injected in the class

      // Fetch circuit breaker settings from config
      const cbConfig = {
        // timeout: configService.get<number>('KEYCLOAK_TIMEOUT', 10000),
        errorThresholdPercentage: configService.get<number>(
          'KEYCLOAK_ERROR_THRESHOLD_PERCENTAGE',
          10000,
        ),
        resetTimeout: configService.get<number>(
          'KEYCLOAK_RESET_TIMEOUT',
          10000,
        ),
        rollingCountMaxErrors: configService.get<number>(
          'KEYCLOAK_ROLLING_COUNT_MAX_ERRORS',
          3,
        ),
      };

      const breaker = new CircuitBreaker(originalMethod.apply(this, args), {
        // timeout: cbConfig.timeout,
        errorThresholdPercentage: cbConfig.errorThresholdPercentage,
        resetTimeout: cbConfig.resetTimeout,
      });

      breaker.on('open', () => console.debug('Circuit breaker opened'));
      breaker.on('halfOpen', (request) =>
        console.debug('Circuit breaker half-open', request),
      );
      breaker.on('close', () => console.debug('Circuit breaker closed'));
      breaker.on('timeout', () => console.debug('Circuit breaker timeout'));
      breaker.on('reject', () => console.debug('Circuit breaker rejected'));
      breaker.on('success', () => console.debug('Circuit breaker success'));
      breaker.on('failure', () => console.debug('Circuit breaker failure'));
      breaker.on('fallback', () => console.debug('Circuit breaker fallback'));

      // Execute the method through the circuit breaker
      try {
        return await breaker.fire();
      } catch (err) {
        if (breaker.opened) {
          throw err;
        }
      }
    };
  };
}
