import { Module } from '@nestjs/common';
import { SectorsController } from './sectors.controller';
import { SectorsService } from './sectors.service';
import { CreateSectorUseCase } from '@/core/application/use-cases/sector/create-sector.use-case';
import { PrismaSectorRepository } from '@/infrastructure/repositories/prisma-sector.repository';
import { PrismaEventPublisherService } from '@/infrastructure/events/prisma-event-publisher.service';
import { PrismaModule } from '@/infrastructure/prisma/prisma.module';
import { UpdateSectorUseCase } from '@/core/application/use-cases/sector/update-sector.use-case';
import { DeleteSectorUseCase } from '@/core/application/use-cases/sector/delete-sector.use-case';
import { ListSectorsUseCase } from '@/core/application/use-cases/sector/list-sectors.use-case';

export const SECTOR_REPOSITORY = 'SECTOR_REPOSITORY';
export const EVENT_PUBLISHER = 'EVENT_PUBLISHER';

@Module({
  imports: [PrismaModule],
  controllers: [SectorsController],
  providers: [
    CreateSectorUseCase,
    UpdateSectorUseCase,
    DeleteSectorUseCase,
    ListSectorsUseCase,
    SectorsService,
    {
      provide: SECTOR_REPOSITORY,
      useClass: PrismaSectorRepository,
    },
    {
      provide: EVENT_PUBLISHER,
      useClass: PrismaEventPublisherService,
    },
  ],
  exports: [
    SECTOR_REPOSITORY,
    EVENT_PUBLISHER,
    SectorsService,
    CreateSectorUseCase,
    UpdateSectorUseCase,
    DeleteSectorUseCase,
    ListSectorsUseCase,
  ],
})
export class SectorsModule {}
