import { drizzle } from 'drizzle-orm/node-postgres';
import { eq } from 'drizzle-orm';
import { Pool } from 'pg';
import { hash } from 'bcryptjs';
import { env } from '../config/env';
import { users } from '../../modules/users/entites/user.entity';

const pool = new Pool({
  connectionString: `postgresql://${env.DB_USER}:${env.DB_PASSWORD}@${env.DB_HOST}:${env.DB_PORT}/${env.DB_NAME}`,
});

const db = drizzle(pool);

// Função para criar usuários de exemplo
async function seedUsers() {
  console.log('🌱 Seeding users...');
  
  try {
    // Verificar se já existem usuários
    const existingUsers = await db.select().from(users);
    if (existingUsers.length > 0) {
      console.log('👤 Users already exist, skipping seed');
      return;
    }

    // Criar usuários de exemplo
    const usersToSeed = [
      {
        email: '<EMAIL>',
        username: 'admin',
        password: await hash('admin123', env.BCRYPT_ROUNDS),
        name: 'Administrator',
        role: 'admin' as const,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        username: 'user',
        password: await hash('user123', env.BCRYPT_ROUNDS),
        name: 'Regular User',
        role: 'user' as const,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        username: 'demo',
        password: await hash('demo123', env.BCRYPT_ROUNDS),
        name: 'Demo User',
        role: 'user' as const,
        isActive: true,
      },
    ];

    // Inserir usuários no banco
    await db.insert(users).values(usersToSeed);
    
    console.log('✅ Users seeded successfully!');
    console.log('📝 Created users:');
    console.log('  - <EMAIL> / admin123 (admin)');
    console.log('  - <EMAIL> / user123 (user)');
    console.log('  - <EMAIL> / demo123 (user)');
    
  } catch (error) {
    console.error('❌ Failed to seed users:', error);
    throw error;
  }
}

// Função principal do seeder
async function main() {
  console.log('🚀 Starting database seeding...');
  
  try {
    await seedUsers();
    console.log('✅ Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Função para criar usuários adicionais aleatórios
async function seedRandomUsers() {
  console.log('🎲 Seeding random users...');
  
  try {
    // Verificar quantos usuários já existem
    const existingUsers = await db.select().from(users);
    if (existingUsers.length >= 10) {
      console.log('👥 Already have enough users, skipping random seed');
      return;
    }

    // Gerar usuários aleatórios
    const randomUsers = [
      {
        email: '<EMAIL>',
        username: 'johndoe',
        password: await hash('password123', env.BCRYPT_ROUNDS),
        name: 'John Doe',
        role: 'user' as const,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        username: 'janesmith',
        password: await hash('password123', env.BCRYPT_ROUNDS),
        name: 'Jane Smith',
        role: 'user' as const,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        username: 'mikeadmin',
        password: await hash('admin456', env.BCRYPT_ROUNDS),
        name: 'Mike Admin',
        role: 'admin' as const,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        username: 'sarahinactive',
        password: await hash('password123', env.BCRYPT_ROUNDS),
        name: 'Sarah Inactive',
        role: 'user' as const,
        isActive: false,
      },
      {
        email: '<EMAIL>',
        username: 'testuser',
        password: await hash('test123', env.BCRYPT_ROUNDS),
        name: 'Test User',
        role: 'user' as const,
        isActive: true,
      },
    ];

    // Inserir apenas usuários que ainda não existem
    for (const user of randomUsers) {
      const existingUser = await db.select().from(users).where(eq(users.email, user.email));
      if (existingUser.length === 0) {
        await db.insert(users).values(user);
        console.log(`✅ Created user: ${user.email}`);
      }
    }
    
    console.log('✅ Random users seeded successfully!');
    
  } catch (error) {
    console.error('❌ Failed to seed random users:', error);
    throw error;
  }
}

// Executar apenas se o arquivo for chamado diretamente
if (require.main === module) {
  main();
}

// Função para criar dados específicos para desenvolvimento
async function seedDevelopmentData() {
  console.log('🔧 Seeding development data...');
  
  try {
    // Verificar se estamos em ambiente de desenvolvimento
    if (env.NODE_ENV !== 'development') {
      console.log('🚫 Not in development environment, skipping dev data seed');
      return;
    }

    // Criar usuários específicos para desenvolvimento
    const devUsers = [
      {
        email: '<EMAIL>',
        username: 'developer',
        password: await hash('dev123', env.BCRYPT_ROUNDS),
        name: 'Developer User',
        role: 'admin' as const,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        username: 'tester',
        password: await hash('test123', env.BCRYPT_ROUNDS),
        name: 'Tester User',
        role: 'user' as const,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        username: 'qa',
        password: await hash('qa123', env.BCRYPT_ROUNDS),
        name: 'QA User',
        role: 'user' as const,
        isActive: true,
      },
    ];

    // Inserir apenas usuários que ainda não existem
    for (const user of devUsers) {
      const existingUser = await db.select().from(users).where(eq(users.email, user.email));
      if (existingUser.length === 0) {
        await db.insert(users).values(user);
        console.log(`🔧 Created dev user: ${user.email} / ${user.username}`);
      }
    }
    
    console.log('✅ Development data seeded successfully!');
    
  } catch (error) {
    console.error('❌ Failed to seed development data:', error);
    throw error;
  }
}

// Função para limpar dados de teste (útil para resetar o ambiente)
async function clearTestData() {
  console.log('🧹 Clearing test data...');
  
  try {
    // Deletar usuários de teste específicos
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    for (const email of testEmails) {
      const deleted = await db.delete(users).where(eq(users.email, email));
      if (deleted) {
        console.log(`🗑️ Deleted user: ${email}`);
      }
    }
    
    console.log('✅ Test data cleared successfully!');
    
  } catch (error) {
    console.error('❌ Failed to clear test data:', error);
    throw error;
  }
}

export { seedUsers, seedRandomUsers, seedDevelopmentData, clearTestData }; 