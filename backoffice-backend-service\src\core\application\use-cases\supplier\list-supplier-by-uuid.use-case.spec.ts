import { Test, TestingModule } from '@nestjs/testing';
import { ListSupplierByUuidUseCase } from './list-supplier-by-uuid.use-case';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { Contact } from '../../../domain/supplier/value-objects/contact.value-object';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { NotFoundException } from '@nestjs/common';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '../../../domain/supplier/enums/supplier-classification.enum';

describe('ListSupplierByUuidUseCase', () => {
  let useCase: ListSupplierByUuidUseCase;
  let repository: jest.Mocked<SupplierRepositoryPort>;

  const mockSupplier = new Supplier(
    'test-id',
    'Test Supplier',
    '12345678901234',
    'Test Trade',
    new Address('Test Street', null, null, null, 'Test City', '12345-678', 'SP'),
    '<EMAIL>',
    SupplierClassification.CORE,
    SupplierType.GAME,
    SupplierStatus.ACTIVE,
    'user-id',
    'user-id',
    new Date(),
    new Date()
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SupplierRepositoryPort> = {
      findById: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByDocument: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findByUserId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ListSupplierByUuidUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<ListSupplierByUuidUseCase>(ListSupplierByUuidUseCase);
    repository =
      module.get<jest.Mocked<SupplierRepositoryPort>>(SUPPLIER_REPOSITORY);
  });

  describe('execute', () => {
    it('should return supplier when found by UUID', async () => {
      const findById = jest.spyOn(repository, 'findById');
      findById.mockResolvedValue(mockSupplier);

      const result = await useCase.execute('test-id');

      expect(result).toBeDefined();
      expect(result).toEqual(mockSupplier);
      expect(findById).toHaveBeenCalledWith('test-id');
    });

    it('should throw ConflictException when supplier does not exist', async () => {
      const findById = jest.spyOn(repository, 'findById');
      findById.mockResolvedValue(null);

      await expect(useCase.execute('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
      expect(findById).toHaveBeenCalledWith('non-existent-id');
    });
  });
});
