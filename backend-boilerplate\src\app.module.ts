import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { ConsoleSpanExporter } from '@opentelemetry/sdk-trace-node';
import { PrometheusExporter } from '@opentelemetry/exporter-prometheus';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { APP_FILTER } from '@nestjs/core';

// Controladores e serviços
import { createConfig } from './config.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { TasksModule } from './modules/tasks/tasks.module';
import { AllExceptionsFilter } from './infrastructure/exceptions/all-exceptions.filter';
import { AwsModule } from './infrastructure/aws/aws.module';
import { EventsModule } from './infrastructure/events/events.module';

@Module({
  imports: [
    // Configuração do módulo
    createConfig(),
    // Scheduler para tarefas agendadas
    ScheduleModule.forRoot(),
    AuthModule,
    UsersModule,
    TasksModule,
    AwsModule,
    EventsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    // Configuração do OpenTelemetry
    const sdk = new NodeSDK({
      traceExporter: new ConsoleSpanExporter(),
      metricReader: new PrometheusExporter({
        port: this.configService.get('OTEL_EXPORTER_PROMETHEUS_PORT') || 8081,
      }),
      instrumentations: [getNodeAutoInstrumentations()],
      serviceName:
        this.configService.get('OTEL_SERVICE_NAME') ||
        'petrus-nest-boilerplate',
    });

    // Iniciar o SDK
    sdk.start();

    // Garantir que o SDK seja encerrado corretamente quando a aplicação for encerrada
    process.on('SIGTERM', () => {
      sdk
        .shutdown()
        .then(() => console.log('SDK encerrado'))
        .catch((error) => console.log('Erro ao encerrar o SDK', error))
        .finally(() => process.exit(0));
    });
  }
}
