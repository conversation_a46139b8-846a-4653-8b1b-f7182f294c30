import {
  Body,
  Controller,
  Patch,
  Delete,
  HttpCode,
  Param,
  Post,
  UseGuards,
  Get,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { Api<PERSON>earerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../../core/domain/role.enum';
import { CreateCompanyUseCase } from '../../core/application/use-cases/company/create-company.use-case';
import { UpdateCompanyUseCase } from '../../core/application/use-cases/company/update-company.use-case';
import { DeleteCompanyUseCase } from '../../core/application/use-cases/company/delete-company.use-case';
import { User } from '../auth/decorators/user.decorator';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { CompanyService } from '../finance/services/company.service';
import { ListCompaniesDto } from '../finance/dto/list-companies.dto';
import { PaginatedResponseDto } from '../finance/dto/paginated-response.dto';
import { Company } from '@prisma/client';
import {
  ApiCreateCompany,
  ApiDeleteCompany,
  ApiGetCompany,
  ApiListCompanies,
  ApiUpdateCompany,
} from '../../infrastructure/swagger/decorators';

// Função auxiliar para validar UUID
function isValidUUID(uuid: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

@ApiTags('Companies')
@Controller('core/companies')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CompanyController {
  constructor(
    private readonly createCompanyUseCase: CreateCompanyUseCase,
    private readonly updateCompanyUseCase: UpdateCompanyUseCase,
    private readonly deleteCompanyUseCase: DeleteCompanyUseCase,
    private readonly companyService: CompanyService,
  ) {}

  @Post()
  @Roles(Role.ADMIN)
  @ApiCreateCompany()
  async createCompany(
    @Body() input: CreateCompanyDto,
    @User('preferred_username') username: string,
  ) {
    const result = await this.createCompanyUseCase.execute({
      ...input,
      createdBy: username,
    });
    return result.company;
  }

  @Get(':uuid')
  @Roles(Role.USER, Role.ADMIN)
  @ApiGetCompany()
  async getCompanyByUuid(@Param('uuid') uuid: string): Promise<Company> {
    // Validação manual do UUID
    if (!isValidUUID(uuid)) {
      throw new BadRequestException('uuid must be a UUID');
    }

    return this.companyService.getCompanyByUuid(uuid);
  }

  @Get()
  @Roles(Role.USER, Role.ADMIN)
  @ApiListCompanies()
  async listCompanies(
    @Query() filters: ListCompaniesDto,
  ): Promise<PaginatedResponseDto<Company>> {
    return this.companyService.listCompanies(filters);
  }

  @Patch(':uuid')
  @Roles(Role.ADMIN)
  @ApiUpdateCompany()
  async updateCompany(
    @Param('uuid') uuid: string,
    @Body() input: UpdateCompanyDto,
    @User('preferred_username') username: string,
  ) {
    const result = await this.updateCompanyUseCase.execute({
      uuid,
      ...input,
      updatedBy: username,
    });
    return result.company.toJSON();
  }

  @Delete(':uuid')
  @HttpCode(204)
  @Roles(Role.ADMIN)
  @ApiDeleteCompany()
  async deleteCompany(@Param('uuid') uuid: string) {
    await this.deleteCompanyUseCase.execute({ uuid });
  }
}
