import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Api<PERSON><PERSON>erAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../../core/domain/role.enum';
import { User } from '../auth/decorators/user.decorator';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { CreatePaymentMethodUseCase } from '../../core/application/use-cases/payment-method/create-payment-method-use-case';
import { ListPaymentMethodUseCase } from '../../core/application/use-cases/payment-method/list-payment-methods-use-case';
import { ListPaymentMethodDto } from './dto/list-payment-method.dto';
import { PaymentMethodResponseDto } from './dto/payment-mehtod.response.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { UpdatePaymentMethodUseCase } from '../../core/application/use-cases/payment-method/update-payment-method-use-case';
import { DeletePaymentMethodUseCase } from '../../core/application/use-cases/payment-method/delete-payment-methods-use-case';
import {
  ApiCreatePaymentMethod,
  ApiDeletePaymentMethod,
  ApiGetPaymentMethod,
  ApiListPaymentMethods,
  ApiUpdatePaymentMethod,
} from '../../infrastructure/swagger/decorators';

@ApiTags('Payment Methods')
@Controller('finance/payment-methods')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class PaymentMethodController {
  constructor(
    private readonly createPaymentMethodUseCase: CreatePaymentMethodUseCase,
    private readonly listPaymentMethodUseCase: ListPaymentMethodUseCase,
    private readonly updatePaymentMethodUseCase: UpdatePaymentMethodUseCase,
    private readonly deletePaymentMethodUseCase: DeletePaymentMethodUseCase,
  ) {}

  @Post()
  @Roles(Role.FINANCE_ADMIN)
  @ApiCreatePaymentMethod()
  async createPaymentMethod(
    @Body() input: CreatePaymentMethodDto,
    @User('preferred_username') username: string,
  ) {
    const result = await this.createPaymentMethodUseCase.execute({
      ...input,
      createdBy: username,
    });
    return result;
  }

  @Get(':uuid')
  @Roles(Role.FINANCE_ADMIN, Role.FINANCE_USER)
  @ApiGetPaymentMethod()
  async listPaymentMethodByUuid(
    @Param('uuid') uuid: string,
  ): Promise<PaymentMethodResponseDto> {
    const result = await this.listPaymentMethodUseCase.execute({
      limit: 10,
      offset: 0,
      id: uuid,
    });

    return result.items[0];
  }

  @Get()
  @Roles(Role.FINANCE_ADMIN, Role.FINANCE_USER)
  @ApiListPaymentMethods()
  async listPaymentMethods(@Query() query: ListPaymentMethodDto) {
    const result = await this.listPaymentMethodUseCase.execute({
      limit: query.limit || 10,
      offset: query.offset || 0,
      label: query.label,
    });

    return result;
  }

  @Patch(':uuid')
  @Roles(Role.FINANCE_ADMIN, Role.FINANCE_USER)
  @ApiUpdatePaymentMethod()
  async updatePaymentMethodPatch(
    @Param('uuid') uuid: string,
    @Body() body: UpdatePaymentMethodDto,
    @User('preferred_username') username: string,
  ): Promise<PaymentMethodResponseDto> {
    return this.updatePaymentMethodUseCase.execute({
      uuid: uuid,
      paymentMethod: {
        label: body.label,
        description: body.description,
      },
      updatedBy: username,
    });
  }

  @Delete(':uuid')
  @Roles(Role.FINANCE_ADMIN, Role.FINANCE_USER)
  @ApiDeletePaymentMethod()
  async deletePaymentMethod(@Param('uuid') uuid: string): Promise<void> {
    return this.deletePaymentMethodUseCase.execute({
      uuid: uuid,
    });
  }
}
