import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, ArrayMinSize, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class DocumentMetadataDto {
  @ApiProperty({
    description: 'Nome do responsável pelo documento',
    example: '<PERSON>'
  })
  @IsString()
  responsible: string;

  @ApiProperty({
    description: 'Departamento responsável pelo documento',
    example: 'Financeiro'
  })
  @IsString()
  department: string;

  @ApiPropertyOptional({
    description: 'Descrição adicional do documento',
    example: 'Contrato de prestação de serviços'
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Data de expiração do documento (YYYY-MM-DD)',
    example: '2024-12-31'
  })
  @IsString()
  @IsOptional()
  expirationDate?: string;
}

export class UploadSupplierDocumentsDto {
  @ApiProperty({ 
    type: 'string', 
    format: 'binary', 
    isArray: true,
    description: 'Arquivos a serem enviados'
  })
  documents: any[];

  @ApiProperty({
    description: 'Metadados dos documentos (JSON string)',
    example: '[{"responsible": "João Silva", "department": "Financeiro", "description": "Contrato principal", "expirationDate": "2024-12-31"}]'
  })
  @IsString()
  documentsMetadata: string;
} 