// Export main service functions
export {
  publishMessage,
  publishJsonMessage,
  publishBatch,
  createTopic,
  deleteTopic,
  subscribe,
  unsubscribe,
  listTopics,
  listSubscriptions,
} from './sns.service';

// Export types
export type {
  PublishMessageParams,
  PublishJsonMessageParams,
  PublishBatchParams,
  SubscribeParams,
  CreateTopicParams,
  SnsNotification,
  SnsSubscriptionConfirmation,
  SnsMessage,
} from './types/sns.types';

// Export utilities
export * as snsUtils from './utils/sns.utils';

// Export client creation function (for advanced usage)
export { createSnsClient } from './client/sns-client';
