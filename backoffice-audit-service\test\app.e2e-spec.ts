// Set environment variables before any imports
process.env.RABBITMQ_URI = 'amqp://localhost:5672';
process.env.AUDIT_EXCHANGE_NAME = 'test.exchange';
process.env.AUDIT_ROUTING_KEY = 'test.routing.key';
process.env.AUDIT_QUEUE_NAME = 'test.queue';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { AppModule } from './../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import { AuditService } from '../src/audit/service/audit.service';

// Mock external dependencies
jest.mock('@golevelup/nestjs-rabbitmq', () => ({
  RabbitMQModule: {
    forRoot: jest.fn().mockReturnValue({
      module: class MockRabbitMQModule {},
      providers: [],
      exports: [],
    }),
  },
  RabbitSubscribe: jest.fn().mockImplementation(() => () => {}),
}));

describe('Audit API (e2e)', () => {
  let app: INestApplication<App>;

  beforeEach(async () => {
    // Mock PrismaService to avoid database connection
    const mockPrismaService = {
      $connect: jest.fn().mockResolvedValue(undefined),
      $disconnect: jest.fn().mockResolvedValue(undefined),
      onModuleInit: jest.fn().mockResolvedValue(undefined),
      onModuleDestroy: jest.fn().mockResolvedValue(undefined),
    };

    // Mock AuditService to return test data
    const mockAuditService = {
      findAll: jest.fn().mockResolvedValue({
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      }),
      create: jest.fn().mockResolvedValue(undefined),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(PrismaService)
      .useValue(mockPrismaService)
      .overrideProvider(AuditService)
      .useValue(mockAuditService)
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
  });

  it('/audit/logs (GET)', () => {
    return request(app.getHttpServer()).get('/audit/logs').expect(200);
  });

  it('/audit/logs (GET) with query parameters', () => {
    return request(app.getHttpServer())
      .get('/audit/logs?page=1&limit=10')
      .expect(200);
  });
});
