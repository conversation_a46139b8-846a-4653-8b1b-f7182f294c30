import { Test } from '@nestjs/testing';
import { Auth<PERSON>ontroller } from '../../auth.controller';
import { AuthService } from '../../auth.service';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../../infrastructure/utils/request.utils.service';
import {
  AUTH_TEST_CONFIG,
  mockConfigService,
  mockRequestUtilsService,
  createTestUser,
  resetAllMocks,
  TestUser,
} from './auth-test.config';
import { JwtService } from '@nestjs/jwt';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
import { EventPublisherService } from '../../../../infrastructure/events/event-publisher.service';
import { EventsTestModule } from '../../../../infrastructure/events/test/events-test.module';
import { UsersService } from '../../../users/users.service';
import { CustomerRepository } from '../../../customers/infrastructure/repositories/customer.repository';
import { EmployeeService } from '../../../finance/employee/employee.service';

// Interface for token response for type checking - used for type safety
interface _TokenResponse {
  access_token: string;
  refresh_token?: string;
  user?: {
    id: string;
    email: string;
    name: string;
  };
  message?: string;
}

describe('Auth Login Integration Tests', () => {
  let controller: AuthController;
  let _authService: AuthService;
  let keycloakService: KeycloakService;
  let testUser: TestUser;

  const mockUserRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByEmail: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    updateUserKeycloakId: jest.fn(),
  };

  const mockUsersService = {
    findOne: jest.fn(),
    create: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn().mockImplementation(() => 'mock-jwt-token'),
    decode: jest.fn(),
  };

  const mockKeycloakIdentityProvider = {
    registerUser: jest.fn(),
    assignUserRoles: jest.fn(),
    authenticate: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    getUserInfo: jest.fn(),
    keycloakAdminUtils: {
      getAdminAuthHeaders: jest.fn().mockResolvedValue({
        Authorization: 'Bearer mock-token',
        'Content-Type': 'application/json',
      }),
    },
  };

  const mockEventPublisher = {
    publish: jest.fn(),
  };

  const mockPasswordResetTokenRepository = {
    create: jest.fn(),
    findByToken: jest.fn(),
    markAsUsed: jest.fn(),
    deleteExpiredTokens: jest.fn(),
  };

  const mockEmailService = {
    sendPasswordResetEmail: jest.fn(),
  };

  beforeEach(async () => {
    // Reset all mocks before each test
    resetAllMocks();

    // Create a new test user for each test
    testUser = createTestUser();

    const moduleRef = await Test.createTestingModule({
      imports: [EventsTestModule],
      controllers: [AuthController],
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: 'PasswordResetTokenRepository',
          useValue: mockPasswordResetTokenRepository,
        },
        {
          provide: 'EmailService',
          useValue: mockEmailService,
        },
        {
          provide: KeycloakService,
          useFactory: () => ({
            token: jest.fn().mockResolvedValue({
              access_token: AUTH_TEST_CONFIG.mock.accessToken,
              refresh_token: AUTH_TEST_CONFIG.mock.refreshToken,
              expires_in: 300,
            }),
            refreshToken: jest.fn().mockResolvedValue({
              access_token: AUTH_TEST_CONFIG.mock.accessToken,
            }),
            logout: jest.fn().mockResolvedValue(undefined),
            validateToken: jest.fn().mockResolvedValue(true),
            getUserInfo: jest.fn().mockResolvedValue({
              sub: testUser.keycloakId,
              preferred_username: testUser.email,
              email: testUser.email,
              name: testUser.name,
            }),
            getBaseUrl: jest
              .fn()
              .mockReturnValue(AUTH_TEST_CONFIG.keycloak.baseUrl),
            getRealm: jest
              .fn()
              .mockReturnValue(AUTH_TEST_CONFIG.keycloak.realm),
          }),
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: mockKeycloakIdentityProvider,
        },
        {
          provide: EventPublisherService,
          useValue: mockEventPublisher,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RequestUtilsService,
          useValue: mockRequestUtilsService,
        },
        {
          provide: EmployeeService,
          useValue: {},
        },
        {
          provide: CustomerRepository,
          useValue: {},
        },
      ],
    }).compile();

    controller = moduleRef.get<AuthController>(AuthController);
    _authService = moduleRef.get<AuthService>(AuthService);
    keycloakService = moduleRef.get<KeycloakService>(KeycloakService);
  });

  describe('token (login)', () => {
    it('should authenticate with Keycloak and get access token', async () => {
      // Mock successful login
      mockUserRepository.findByEmail.mockResolvedValue({
        id: 'user-123',
        email: testUser.email,
        name: testUser.name,
        role: 'USER',
        keycloakId: testUser.keycloakId,
      });

      const loginDto = {
        username: testUser.email,
        password: testUser.password,
      };

      const result = (await controller.token(loginDto)) as _TokenResponse;

      expect(result).toHaveProperty('access_token');
      const tokenSpy = jest.spyOn(keycloakService, 'token');
      expect(tokenSpy).toHaveBeenCalledWith(testUser.email, testUser.password);

      testUser.accessToken = result.access_token;
      testUser.refreshToken = result.refresh_token || null;
    });

    it('should reject login with incorrect credentials', async () => {
      // Mock Keycloak error
      jest
        .spyOn(keycloakService, 'token')
        .mockRejectedValueOnce(new Error('Invalid credentials'));

      const loginDto = {
        username: testUser.email,
        password: 'WrongPassword123!',
      };

      await expect(controller.token(loginDto)).rejects.toThrow(
        'Usuário ou senha inválidos',
      );
    });

    it('should reject login with non-existent user', async () => {
      const loginDto = {
        username: '<EMAIL>',
        password: 'Password123!',
      };

      jest
        .spyOn(keycloakService, 'token')
        .mockRejectedValueOnce(new Error('Usuário ou senha inválidos'));

      await expect(controller.token(loginDto)).rejects.toThrow(
        'Usuário ou senha inválidos',
      );
    });
  });
});
