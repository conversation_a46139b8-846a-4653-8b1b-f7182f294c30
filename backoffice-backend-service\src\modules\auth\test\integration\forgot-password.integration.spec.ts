import { Test } from '@nestjs/testing';
import { AuthController } from '../../auth.controller';
import { AuthService } from '../../auth.service';
import { JwtService } from '@nestjs/jwt';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
import { EventPublisherService } from '../../../../infrastructure/events/event-publisher.service';
import { UsersService } from '../../../users/users.service';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../../infrastructure/utils/request.utils.service';
import {
  mockConfigService,
  mockRequestUtilsService,
  createTestUser,
  resetAllMocks,
  TestUser,
} from './auth-test.config';
import { ForgotPasswordDto } from '../../dto/forgot-password.dto';
import { EventsTestModule } from '../../../../infrastructure/events/test/events-test.module';
import { EmployeeService } from '../../../finance/employee/employee.service';
import { CustomerRepository } from '../../../customers/infrastructure/repositories/customer.repository';

describe('Auth Forgot Password Integration Tests', () => {
  let controller: AuthController;
  let _authService: AuthService;
  let testUser: TestUser;

  const mockUserRepository = {
    findByEmail: jest.fn(),
    create: jest.fn(),
    updateUserKeycloakId: jest.fn(),
  };

  const mockPasswordResetTokenRepository = {
    create: jest.fn(),
    findByToken: jest.fn(),
    markAsUsed: jest.fn(),
    deleteExpiredTokens: jest.fn(),
    deleteByToken: jest.fn(),
  };

  const mockEmailService = {
    sendPasswordResetEmail: jest.fn(),
  };

  const mockUsersService = {
    findOne: jest.fn(),
    create: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn().mockImplementation(() => 'mock-jwt-token'),
    decode: jest.fn(),
  };

  const mockKeycloakService = {
    token: jest.fn(),
    refreshToken: jest.fn(),
    validateToken: jest.fn(),
    getUserInfo: jest.fn(),
    logout: jest.fn(),
  };

  const mockKeycloakIdentityProvider = {
    registerUser: jest.fn(),
    assignUserRoles: jest.fn(),
    authenticate: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    getUserInfo: jest.fn(),
    keycloakAdminUtils: {
      getAdminAuthHeaders: jest.fn().mockResolvedValue({
        Authorization: 'Bearer mock-token',
        'Content-Type': 'application/json',
      }),
    },
  };

  const mockEventPublisher = {
    publish: jest.fn(),
  };

  beforeEach(async () => {
    resetAllMocks();
    testUser = createTestUser();

    const moduleRef = await Test.createTestingModule({
      imports: [EventsTestModule],
      controllers: [AuthController],
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: 'PasswordResetTokenRepository',
          useValue: mockPasswordResetTokenRepository,
        },
        {
          provide: 'EmailService',
          useValue: mockEmailService,
        },
        {
          provide: KeycloakService,
          useValue: mockKeycloakService,
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: mockKeycloakIdentityProvider,
        },
        {
          provide: EventPublisherService,
          useValue: mockEventPublisher,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RequestUtilsService,
          useValue: mockRequestUtilsService,
        },
        {
          provide: EmployeeService,
          useValue: {},
        },
        {
          provide: CustomerRepository,
          useValue: {},
        },
      ],
    }).compile();

    controller = moduleRef.get<AuthController>(AuthController);
    _authService = moduleRef.get<AuthService>(AuthService);
  });

  describe('POST /auth/forgot-password', () => {
    it('should return 204 and save token if user exists', async () => {
      const dto: ForgotPasswordDto = { email: testUser.email };
      mockUserRepository.findByEmail.mockResolvedValue({
        id: 'user-123',
        email: testUser.email,
      });
      mockPasswordResetTokenRepository.create.mockResolvedValue({
        id: 'token-123',
        token: '123456',
      });

      await expect(controller.forgotPassword(dto)).resolves.toBeUndefined();
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(
        testUser.email,
      );
      expect(mockPasswordResetTokenRepository.create).toHaveBeenCalled();
      expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalled();
    });

    it('should return 204 if user does not exist (no email sent)', async () => {
      const dto: ForgotPasswordDto = { email: '<EMAIL>' };
      mockUserRepository.findByEmail.mockResolvedValue(null);

      await expect(controller.forgotPassword(dto)).resolves.toBeUndefined();
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(
        '<EMAIL>',
      );
      expect(mockPasswordResetTokenRepository.create).not.toHaveBeenCalled();
      expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalled();
    });

    it('should throw 500 on internal error', async () => {
      const dto: ForgotPasswordDto = { email: testUser.email };
      mockUserRepository.findByEmail.mockRejectedValue(new Error('DB error'));

      try {
        await controller.forgotPassword(dto);
        fail('Should have thrown');
      } catch (error: unknown) {
        const err = error as {
          status?: number;
          getStatus?: () => number;
          message?: string;
        };
        const status = err.status ?? err.getStatus?.() ?? 500;
        const message = err.message ?? 'Internal Server Error';
        expect(`${status} ${message}`).toMatch(/(500|DB error)/);
      }
    });
  });
});
