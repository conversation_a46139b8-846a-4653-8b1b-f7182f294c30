import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CustomerPaymentPreferenceDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  department: string;

  @ApiProperty()
  responsible: string;

  @ApiProperty()
  paymentMethodId: string;

  @ApiProperty()
  paymentCondition: string;

  @ApiProperty()
  billingPreference: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class CreateCustomerPaymentPreferenceDto {
  @IsNotEmpty({ message: 'Department is required' })
  @IsString({ message: 'Department must be a string' })
  department: string;

  @IsNotEmpty({ message: 'Responsible is required' })
  @IsString({ message: 'Responsible must be a string' })
  responsible: string;

  @IsNotEmpty({ message: 'Payment method ID is required' })
  @IsString({ message: 'Payment method ID must be a string' })
  paymentMethodId: string;

  @IsNotEmpty({ message: 'Payment condition is required' })
  @IsString({ message: 'Payment condition must be a string' })
  paymentCondition: string;

  @IsNotEmpty({ message: 'Billing preference is required' })
  @IsString({ message: 'Billing preference must be a string' })
  billingPreference: string;
}

export class UpdateCustomerPaymentPreferenceDto {
  @IsNotEmpty({ message: 'Department is required' })
  @IsString({ message: 'Department must be a string' })
  department: string;

  @IsNotEmpty({ message: 'Responsible is required' })
  @IsString({ message: 'Responsible must be a string' })
  responsible: string;

  @IsNotEmpty({ message: 'Payment method ID is required' })
  @IsString({ message: 'Payment method ID must be a string' })
  paymentMethodId: string;

  @IsNotEmpty({ message: 'Payment condition is required' })
  @IsString({ message: 'Payment condition must be a string' })
  paymentCondition: string;

  @IsNotEmpty({ message: 'Billing preference is required' })
  @IsString({ message: 'Billing preference must be a string' })
  billingPreference: string;
}
