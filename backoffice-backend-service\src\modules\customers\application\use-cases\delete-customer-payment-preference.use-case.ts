import { Injectable, Inject } from '@nestjs/common';
import { ICustomerPaymentPreferenceRepository } from '../../domain/repositories/customer-payment-preference.repository.interface';

@Injectable()
export class DeleteCustomerPaymentPreferenceUseCase {
  constructor(
    @Inject('ICustomerPaymentPreferenceRepository')
    private readonly repository: ICustomerPaymentPreferenceRepository,
  ) {}

  async execute(id: string): Promise<void> {
    await this.repository.delete(id);
  }
} 