import { Task } from '../../../domain/task.entity';
import { TaskRepository } from '../../../ports/repositories/task-repository.interface';
import { v4 as uuidv4 } from 'uuid';
import { TaskStatus } from '../../../domain/task-status.enum';

export interface CreateTaskInput {
  userId: string;
  title: string;
  description?: string;
}

export class CreateTaskUseCase {
  constructor(private taskRepository: TaskRepository) {}

  async execute(input: CreateTaskInput): Promise<Task> {
    const { userId, title, description = '' } = input;

    // Criar a entidade de domínio
    const task = new Task(
      uuidv4(),
      userId,
      title,
      description,
      TaskStatus.PENDING,
    );

    // Persistir no repositório
    return this.taskRepository.create(task);
  }
}
