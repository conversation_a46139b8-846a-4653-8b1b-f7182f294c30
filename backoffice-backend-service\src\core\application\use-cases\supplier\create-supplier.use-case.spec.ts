import { Test, TestingModule } from '@nestjs/testing';
import { CreateSupplierUseCase } from './create-supplier.use-case';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { CreateSupplierDto } from '../../../../modules/finance/dto/create-supplier.dto';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { ConflictException } from '@nestjs/common';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '@prisma/client';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { AuthService } from '../../../../modules/auth/auth.service';
import { UsersService } from '../../../../modules/users/users.service';
import { NotFoundException } from '@nestjs/common';

describe('CreateSupplierUseCase', () => {
  let useCase: CreateSupplierUseCase;
  let repository: jest.Mocked<SupplierRepositoryPort>;

  const mockSupplier = new Supplier(
    'test-id',
    'Test Supplier',
    '12345678901234',
    'Test Trade',
    new Address(
      'Test Street',
      null,
      null,
      null,
      'Test City',
      '12345-678',
      'TS',
    ),
    '<EMAIL>',
    SupplierClassification.CORE,
    SupplierType.GAME,
    SupplierStatus.PENDING,
    'user-id',
    'test-user',
    new Date('2024-01-01T00:00:00Z'),
    new Date('2024-01-01T00:00:00Z'),
    'test-user',
  );

  beforeEach(async () => {
    const mockRepository = {
      create: jest.fn(),
      findByDocument: jest.fn(),
      findById: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    const mockAuthService = {
      forgotPassword: jest.fn().mockResolvedValue(undefined),
    };
    const mockUsersService = {
      create: jest.fn().mockResolvedValue({ id: 'user-id', email: '<EMAIL>' }),
      findByEmail: jest.fn().mockRejectedValue(new NotFoundException()),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateSupplierUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    useCase = module.get<CreateSupplierUseCase>(CreateSupplierUseCase);
    repository = module.get(SUPPLIER_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    const createSupplierDto: CreateSupplierDto = {
      name: 'Test Supplier',
      document: '12345678901234',
      tradeName: 'Test Trade',
      address: {
        street: 'Test Street',
        city: 'Test City',
        zipCode: '12345-678',
        state: 'TS',
      },
      type: SupplierType.GAME,
      email: '<EMAIL>',
    };

    const userId = 'test-user';

    it('should create a new supplier successfully', async () => {
      repository.findByDocument.mockResolvedValue(null);
      repository.create.mockResolvedValue(mockSupplier);

      const findBydocument = jest.spyOn(repository, 'findByDocument');
      const create = jest.spyOn(repository, 'create');

      const result = await useCase.execute(createSupplierDto, userId);

      expect(findBydocument).toHaveBeenCalledWith(createSupplierDto.document);
      expect(create).toHaveBeenCalled();
      expect(result).toEqual(mockSupplier);
    });

    it('should throw ConflictException if document already exists', async () => {
      repository.findByDocument.mockResolvedValue(mockSupplier);

      const findBydocument = jest.spyOn(repository, 'findByDocument');
      const create = jest.spyOn(repository, 'create');

      await expect(useCase.execute(createSupplierDto, userId)).rejects.toThrow(
        ConflictException,
      );
      expect(findBydocument).toHaveBeenCalledWith(createSupplierDto.document);
      expect(create).not.toHaveBeenCalled();
    });

    it('should throw if findByEmail throws an unexpected error', async () => {
      const unexpectedError = new Error('Unexpected');
      repository.findByDocument.mockResolvedValue(null);

      const mockAuthService = { forgotPassword: jest.fn() };
      const mockUsersService = {
        create: jest.fn(),
        findByEmail: jest.fn().mockRejectedValue(unexpectedError),
      };
      const module = await Test.createTestingModule({
        providers: [
          CreateSupplierUseCase,
          {
            provide: SUPPLIER_REPOSITORY,
            useValue: repository,
          },
          {
            provide: AuthService,
            useValue: mockAuthService,
          },
          {
            provide: UsersService,
            useValue: mockUsersService,
          },
        ],
      }).compile();
      const useCaseWithError = module.get<CreateSupplierUseCase>(CreateSupplierUseCase);
      await expect(
        useCaseWithError.execute(createSupplierDto, userId)
      ).rejects.toThrow('Unexpected');
    });
  });
});
