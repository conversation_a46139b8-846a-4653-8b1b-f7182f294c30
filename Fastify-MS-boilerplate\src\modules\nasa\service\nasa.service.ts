import 'dotenv/config';
import { Queue } from 'bullmq';
import { Resend } from 'resend';
import axios from 'axios';

/**
 * Fila BullMQ responsável por processar o envio de e-mails do APOD da NASA.
 */
const apodEmailQueue = new Queue('nasa', {
  connection: {
    host: process.env.REDIS_HOST,
    port: Number(process.env.REDIS_PORT),
    // password: process.env.REDIS_PASSWORD, // se necessário
  },
});

/**
 * Adiciona um novo job à fila para enviar o e-mail do APOD da NASA.
 * @param recipientEmail E-mail do destinatário que receberá o APOD.
 */
export async function enqueueApodEmailJob(recipientEmail: string) {
  await apodEmailQueue.add('send-apod-email', { email: recipientEmail });
}

/**
 * Busca a imagem do dia (APOD) da NASA e envia por e-mail ao destinatário.
 * Esta função deve ser chamada pelo worker ao processar o job.
 * @param recipientEmail E-mail do destinatário.
 */
export async function sendApodEmailToRecipient(recipientEmail: string) {
  const apodResponse = await axios.get('https://api.nasa.gov/planetary/apod', {
    params: { api_key: process.env.NASA_APOD_API_KEY },
  });
  const { title, url, explanation } = apodResponse.data;

  const resend = new Resend(process.env.RESEND_API_KEY);
  await resend.emails.send({
    from: '<EMAIL>',
    to: recipientEmail,
    subject: `Imagem do dia da NASA: ${title}`,
    html: `<h1>${title}</h1><img src="${url}" alt="${title}" /><p>${explanation}</p>`,
  });
} 