// Set environment variables before any imports
process.env.RABBITMQ_URI = 'amqp://localhost:5672';
process.env.AUDIT_EXCHANGE_NAME = 'test.exchange';
process.env.AUDIT_ROUTING_KEY = 'test.routing.key';
process.env.AUDIT_QUEUE_NAME = 'test.queue';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from '../app.module';
import { PrismaService } from '../prisma/prisma.service';
import { AuditService } from '../audit/service/audit.service';

// Mock external dependencies
jest.mock('@golevelup/nestjs-rabbitmq', () => ({
  RabbitMQModule: {
    forRoot: jest.fn().mockReturnValue({
      module: class MockRabbitMQModule {},
      providers: [],
      exports: [],
    }),
  },
  RabbitSubscribe: jest.fn().mockImplementation(() => () => {}),
}));

describe('Main Application Bootstrap (Integration)', () => {
  let app: INestApplication;
  let module: TestingModule;
  let mockPrismaService: any;

  beforeEach(async () => {
    // Mock PrismaService to avoid database connection
    mockPrismaService = {
      $connect: jest.fn().mockResolvedValue(undefined),
      $disconnect: jest.fn().mockResolvedValue(undefined),
      onModuleInit: jest.fn().mockResolvedValue(undefined),
      onModuleDestroy: jest.fn().mockResolvedValue(undefined),
    };

    module = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(PrismaService)
      .useValue(mockPrismaService)
      .compile();

    app = module.createNestApplication();
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
    if (module) {
      await module.close();
    }
  });

  it('should create the application successfully', () => {
    expect(app).toBeDefined();
  });

  it('should start the application without errors', async () => {
    await expect(app.init()).resolves.not.toThrow();
  });

  it('should have all required modules loaded', () => {
    expect(app.get(PrismaService)).toBeDefined();
    expect(app.get(AuditService)).toBeDefined();
  });

  it('should have versioning enabled after init', async () => {
    await app.init();

    // Check if versioning is configured (we can't easily test this directly,
    // but we can ensure the app initializes with the configuration)
    expect(app).toBeDefined();
  });

  it('should configure Swagger documentation', async () => {
    await app.init();

    // The actual Swagger setup happens in main.ts, but we can verify
    // the app structure supports it
    expect(app).toBeDefined();
  });
});
