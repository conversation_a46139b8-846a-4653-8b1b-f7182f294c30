import { DomainEvent } from './domain-event';
import { Task } from '../task.entity';

export class TaskCanceledEvent extends DomainEvent {
  private readonly task: Task;

  constructor(
    task: Task,
    metadata?: {
      correlationId?: string;
      userId?: string;
    },
  ) {
    super('task.canceled', {
      correlationId: metadata?.correlationId,
      userId: metadata?.userId || task.userId,
    });

    this.task = task;
  }

  getData(): object {
    return {
      taskId: this.task.id,
      userId: this.task.userId,
      title: this.task.title,
      canceledAt: this.task.updatedAt.toISOString(),
    };
  }
}
