import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { CustomerContract } from '../../domain/entities/customer-contract.entity';
import { ICustomerContractRepository } from '../../domain/repositories/customer-contract.repository.interface';
import { EntityType, ContractType, ContractStatus } from '@prisma/client';

@Injectable()
export class CustomerContractRepository implements ICustomerContractRepository {
  constructor(private readonly prisma: PrismaService) { }

  async create(contract: { customerUuid: string; filePath?: string; fileName?: string; isSigned: boolean }): Promise<CustomerContract> {
    const contractRecord = await this.prisma.contract.create({
      data: {
        entityType: EntityType.CLIENT,
        entityUuid: contract.customerUuid,
        contractType: ContractType.CERT_PLATFORM,
        createdBy: 'system',
        updatedBy: 'system',
      },
    });

    if (contract.filePath) {
      await this.prisma.contractVersion.create({
        data: {
          versionId: 1,
          uploadedBy: 'system',
          filePath: contract.filePath,
          signed: contract.isSigned,
          contractId: contractRecord.id,
        },
      });
    }

    // Extrair fileName do filePath se não foi fornecido
    const fileName = contract.fileName || this.extractFileNameFromPath(contract.filePath);

    return {
      uuid: contractRecord.id,
      customerUuid: contract.customerUuid,
      name: fileName || `Contrato ${contractRecord.id}`,
      url: contract.filePath || '',
      isSigned: contract.isSigned,
      fileName: fileName,
      status: ContractStatus.PENDING,
      createdAt: contractRecord.createdAt,
      updatedAt: contractRecord.updatedAt,
    };
  }

  async createWithMetadata(contract: {
    customerUuid: string;
    filePath?: string;
    fileName?: string;
    isSigned: boolean;
    contractIdentifier: string;
    contractType?: ContractType;
    expirationDate?: string;
    uploadedBy: string;
    status: ContractStatus;
  }): Promise<CustomerContract> {
    const contractRecord = await this.prisma.contract.create({
      data: {
        entityType: EntityType.CLIENT,
        entityUuid: contract.customerUuid,
        contractType: contract.contractType || ContractType.CERT_PLATFORM,
        createdBy: contract.uploadedBy,
        updatedBy: contract.uploadedBy,
        status: contract.status,
      },
    });

    if (contract.filePath) {
      await this.prisma.contractVersion.create({
        data: {
          versionId: 1,
          uploadedBy: contract.uploadedBy,
          filePath: contract.filePath,
          signed: contract.isSigned,
          expirationDate: contract.expirationDate ? new Date(contract.expirationDate) : null,
          contractId: contractRecord.id,
        },
      });
    }

    // Extrair fileName do filePath se não foi fornecido
    const fileName = contract.fileName || this.extractFileNameFromPath(contract.filePath);

    return {
      uuid: contractRecord.id,
      customerUuid: contract.customerUuid,
      name: contract.contractIdentifier,
      url: contract.filePath || '',
      isSigned: contract.isSigned,
      fileName: fileName,
      status: contract.status,
      contractIdentifier: contract.contractIdentifier,
      contractType: contract.contractType,
      expirationDate: contract.expirationDate,
      uploadedBy: contract.uploadedBy,
      createdAt: contractRecord.createdAt,
      updatedAt: contractRecord.updatedAt,
    };
  }

  private extractFileNameFromPath(filePath?: string): string | undefined {
    if (!filePath) return undefined;
    
    // O padrão é: customer/{customerUuid}/{contractUuid}/v{version}/{fileName}
    const parts = filePath.split('/');
    return parts.length > 0 ? parts[parts.length - 1] : undefined;
  }

  async findByUuid(customerUuid: string, contractUuid: string): Promise<CustomerContract | null> {
    return this.prisma.contract.findFirst({
      where: { id: contractUuid, entityUuid: customerUuid },
    }) as unknown as CustomerContract;
  }

  async findByContractUuid(contractUuid: string): Promise<CustomerContract | null> {
    const contract = await this.prisma.contract.findFirst({
      where: { id: contractUuid },
      include: {
        versions: {
          orderBy: { versionId: 'desc' },
          take: 1,
        },
      },
    });

    if (!contract) return null;

    const latestVersion = contract.versions[0];
    const fileName = this.extractFileNameFromPath(latestVersion?.filePath);

    return {
      uuid: contract.id,
      customerUuid: contract.entityUuid,
      name: fileName || `Contrato ${contract.id}`,
      url: latestVersion?.filePath || '',
      isSigned: latestVersion?.signed || false,
      fileName: fileName,
      status: contract.status,
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
    };
  }

  async findAllByCustomer(customerUuid: string): Promise<CustomerContract[]> {
    const contracts = await this.prisma.contract.findMany({
      where: { entityUuid: customerUuid },
      include: {
        versions: {
          orderBy: { versionId: 'desc' },
          take: 1,
        },
      },
    });

    return contracts.map(contract => {
      const latestVersion = contract.versions[0];
      const fileName = this.extractFileNameFromPath(latestVersion?.filePath);

      return {
        uuid: contract.id,
        customerUuid: contract.entityUuid,
        name: fileName || `Contrato ${contract.id}`,
        url: latestVersion?.filePath || '',
        isSigned: latestVersion?.signed || false,
        fileName: fileName,
        status: contract.status,
        createdAt: contract.createdAt,
        updatedAt: contract.updatedAt,
      };
    });
  }

  async update(_customerUuid: string, contractUuid: string, _data: Partial<CustomerContract>): Promise<CustomerContract> {
    return this.prisma.contract.update({
      where: { id: contractUuid },
      data: {
        updatedBy: 'system',
      },
    }) as unknown as CustomerContract;
  }

  async delete(_customerUuid: string, contractUuid: string): Promise<void> {
    await this.prisma.contractVersion.deleteMany({
      where: { contractId: contractUuid },
    });
    await this.prisma.contract.delete({
      where: { id: contractUuid },
    });
  }
} 