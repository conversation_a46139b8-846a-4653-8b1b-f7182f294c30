import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as amqp from 'amqplib';

@Injectable()
export class RabbitMQService implements OnModuleInit, OnModuleDestroy {
  private connection: any = null;
  private channel: any = null;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async connect() {
    try {
      const host = this.configService.get<string>('RABBITMQ_HOST', 'localhost');
      const url = `amqp://guest:guest@${host}:5672`;

      this.connection = await amqp.connect(url);
      if (this.connection) {
        this.channel = await this.connection.createChannel();
        console.log('Conectado ao RabbitMQ com sucesso');
      }
    } catch (error) {
      this.logError('Erro ao conectar ao RabbitMQ', error);
      throw new Error('Falha ao conectar ao RabbitMQ');
    }
  }

  private async disconnect() {
    try {
      if (this.channel) {
        await this.channel.close();
      }
      if (this.connection) {
        await this.connection.close();
      }
      console.log('Desconectado do RabbitMQ com sucesso');
    } catch (error) {
      this.logError('Erro ao desconectar do RabbitMQ', error);
    }
  }

  /**
   * Método auxiliar para logar erros de forma segura
   * @param message Mensagem de contexto do erro
   * @param error Objeto de erro
   */
  private logError(message: string, error: unknown): void {
    let errorDetails: string;

    if (error instanceof Error) {
      errorDetails = error.message;
      if (error.stack) {
        console.debug(error.stack);
      }
    } else if (error !== null && error !== undefined) {
      try {
        if (typeof error === 'object') {
          errorDetails = JSON.stringify(error) || 'objeto vazio';
        } else {
          errorDetails = String(error);
        }
      } catch {
        errorDetails = 'erro não serializável';
      }
    } else {
      errorDetails = 'erro desconhecido';
    }

    console.error(message + ': ' + errorDetails);
  }

  /**
   * Publica uma mensagem em uma exchange
   * @param exchange - Nome da exchange
   * @param routingKey - Chave de roteamento
   * @param message - Mensagem para publicar (será convertida para JSON)
   * @param options - Opções adicionais
   */
  async publish(
    exchange: string,
    routingKey: string,
    message: Record<string, unknown>,
    options?: amqp.Options.Publish,
  ) {
    try {
      if (!this.channel) {
        await this.connect();
      }

      if (!this.channel) {
        throw new Error('Não foi possível estabelecer conexão com o RabbitMQ');
      }

      // Garantir que a exchange existe
      await this.channel.assertExchange(exchange, 'topic', { durable: true });

      // Publicar a mensagem
      this.channel.publish(
        exchange,
        routingKey,
        Buffer.from(JSON.stringify(message)),
        { persistent: true, ...options },
      );

      console.log(
        `Mensagem publicada na exchange ${exchange} com routing key ${routingKey}`,
      );
    } catch (error) {
      console.error('Erro ao publicar mensagem no RabbitMQ:', error);
      throw error;
    }
  }

  /**
   * Consome mensagens de uma fila
   * @param queue - Nome da fila
   * @param callback - Função de callback para processar as mensagens
   * @param options - Opções adicionais
   */
  async consume(
    queue: string,
    callback: (message: Record<string, unknown>) => Promise<void> | void,
    options?: amqp.Options.Consume,
  ) {
    try {
      if (!this.channel) {
        await this.connect();
      }

      if (!this.channel) {
        throw new Error('Não foi possível estabelecer conexão com o RabbitMQ');
      }

      // Garantir que a fila existe
      await this.channel.assertQueue(queue, { durable: true });

      // Consumir mensagens
      const consumeCallback = (msg: amqp.ConsumeMessage | null) => {
        if (msg) {
          void (async () => {
            try {
              const content = JSON.parse(msg.content.toString()) as Record<
                string,
                unknown
              >;
              await Promise.resolve(callback(content));
              if (this.channel) {
                this.channel.ack(msg);
              }
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : 'erro desconhecido';
              console.error(
                `Erro ao processar mensagem da fila ${queue}: ${errorMessage}`,
              );
              // Rejeitar a mensagem com requeue
              if (this.channel) {
                this.channel.nack(msg, false, true);
              }
            }
          })();
        }
      };

      await this.channel.consume(queue, consumeCallback, { ...options });

      console.log(`Consumidor registrado para a fila ${queue}`);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'erro desconhecido';
      console.error(`Erro ao consumir mensagens do RabbitMQ: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Vincula uma fila a uma exchange com uma routing key
   * @param queue - Nome da fila
   * @param exchange - Nome da exchange
   * @param pattern - Padrão da routing key
   */
  async bindQueue(queue: string, exchange: string, pattern: string) {
    try {
      if (!this.channel) {
        await this.connect();
      }

      if (!this.channel) {
        throw new Error('Não foi possível estabelecer conexão com o RabbitMQ');
      }

      // Garantir que a exchange e a fila existem
      await this.channel.assertExchange(exchange, 'topic', { durable: true });
      await this.channel.assertQueue(queue, { durable: true });

      // Vincular a fila à exchange
      await this.channel.bindQueue(queue, exchange, pattern);

      console.log(
        `Fila ${queue} vinculada à exchange ${exchange} com pattern ${pattern}`,
      );
    } catch (error) {
      console.error('Erro ao vincular fila à exchange:', error);
      throw error;
    }
  }
}
