import { PasswordHasher } from '../../../shared/password-hasher';
import { UserRepository } from '../../../ports/repositories/user-repository.interface';

export interface AuthenticateUserInput {
  email: string;
  password: string;
}

export interface AuthenticateUserOutput {
  id: string;
  email: string;
  name: string;
  role: string;
}

export class AuthenticateUserUseCase {
  constructor(private userRepository: UserRepository) {}

  async execute(
    input: AuthenticateUserInput,
  ): Promise<AuthenticateUserOutput | null> {
    const { email, password } = input;

    // Buscar o usuário pelo email
    const user = await this.userRepository.findByEmail(email).catch(() => null);

    if (!user) {
      return null;
    }

    // Verificar se a senha está correta
    const isPasswordValid = await PasswordHasher.compare(
      password,
      user.password,
    );

    if (!isPasswordValid) {
      return null;
    }

    // Retornar os dados do usuário autenticado
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    };
  }
}
