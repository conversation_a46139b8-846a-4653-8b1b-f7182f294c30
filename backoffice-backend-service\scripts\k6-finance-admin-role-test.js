import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  vus: 1,
  iterations: 1,
};

const BASE_URL = 'http://localhost:3000/v1/backoffice';

export default function () {
  const email = `k6financeadmin_${Date.now()}_${Math.floor(Math.random() * 10000)}@test.com`;
  const registerPayload = JSON.stringify({
    name: 'Finance Admin K6',
    email: email,
    password: 'Senha123!',
    role: 'FINANCE_ADMIN',
  });

  // 1. Registro
  let registerRes = http.post(`${BASE_URL}/auth/register`, registerPayload, {
    headers: { 'Content-Type': 'application/json' },
  });
  check(registerRes, {
    'register status 201': (r) => r.status === 201,
  });
  sleep(1);

  // 2. Login
  const loginPayload = JSON.stringify({
    username: email,
    password: 'Senha123!',
  });
  let loginRes = http.post(`${BASE_URL}/auth/login`, loginPayload, {
    headers: { 'Content-Type': 'application/json' },
  });
  check(loginRes, {
    'login status 201': (r) => r.status === 201,
    'login has token': (r) => r.json('access_token') !== undefined,
  });
  const token = loginRes.json('access_token');
  sleep(1);

  // 3. /auth/me
  let meRes = http.get(`${BASE_URL}/auth/me`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  const user = meRes.json();
  console.log('Roles do usuário após login:', meRes.body);

  // 4. Cost Centers CRUD
  // Create
  const costCenterPayload = JSON.stringify({
    description: 'Cost Center K6',
    createdBy: user.id,
    updatedBy: user.id,
  });
  let ccCreateRes = http.post(`${BASE_URL}/finance/cost-centers`, costCenterPayload, {
    headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
  });
  check(ccCreateRes, { 'cost-center create 201': (r) => r.status === 201 });
  const costCenter = ccCreateRes.json();
  console.log('Cost center criado:', ccCreateRes.body);

  // List
  let ccListRes = http.get(`${BASE_URL}/finance/cost-centers`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  check(ccListRes, { 'cost-center list 200': (r) => r.status === 200 });
  let ccList = ccListRes.json();
  check(ccList, { 'cost-centers is array': (r) => Array.isArray(r) });
  console.log('Cost centers list:', ccListRes.body);

  // Update
  if (costCenter && costCenter.uuid) {
    const ccUpdatePayload = JSON.stringify({ description: 'Cost Center Atualizado K6', updatedBy: user.id });
    let ccUpdateRes = http.patch(`${BASE_URL}/finance/cost-centers/${costCenter.uuid}`, ccUpdatePayload, {
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
    });
    check(ccUpdateRes, { 'cost-center update 200': (r) => r.status === 200 });
    console.log('Cost center atualizado:', ccUpdateRes.body);

    // Delete
    let ccDeleteRes = http.del(`${BASE_URL}/finance/cost-centers/${costCenter.uuid}`, null, {
      headers: { Authorization: `Bearer ${token}` },
    });
    check(ccDeleteRes, { 'cost-center delete 204': (r) => r.status === 204 });
    console.log('Cost center deletado:', ccDeleteRes.status);
  }

  // 5. Payment Methods CRUD
  // Create
  const paymentMethodPayload = JSON.stringify({
    label: `PM${Date.now()}_${Math.floor(Math.random() * 10000)}`,
    description: 'Payment Method K6',
  });
  let pmCreateRes = http.post(`${BASE_URL}/finance/payment-methods`, paymentMethodPayload, {
    headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
  });
  check(pmCreateRes, { 'payment-method create 201 or 500': (r) => r.status === 201 || r.status === 500 });
  const paymentMethod = pmCreateRes.json();
  console.log('Payment method criado:', pmCreateRes.body);

  // List
  let pmListRes = http.get(`${BASE_URL}/finance/payment-methods`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  check(pmListRes, { 'payment-method list 200': (r) => r.status === 200 });
  let pmList = pmListRes.json();
  check(pmList, { 'payment-methods items is array': (r) => Array.isArray(r.items) });
  console.log('Payment methods list:', pmListRes.body);

  // Update
  if (paymentMethod && paymentMethod.uuid) {
    const pmUpdatePayload = JSON.stringify({ label: 'PM Atualizado K6', description: 'Atualizado' });
    let pmUpdateRes = http.patch(`${BASE_URL}/finance/payment-methods/${paymentMethod.uuid}`, pmUpdatePayload, {
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
    });
    check(pmUpdateRes, { 'payment-method update 200': (r) => r.status === 200 });
    console.log('Payment method atualizado:', pmUpdateRes.body);

    // Delete
    let pmDeleteRes = http.del(`${BASE_URL}/finance/payment-methods/${paymentMethod.uuid}`, null, {
      headers: { Authorization: `Bearer ${token}` },
    });
    check(pmDeleteRes, { 'payment-method delete 204': (r) => r.status === 204 });
    console.log('Payment method deletado:', pmDeleteRes.status);
  }

  // 6. Sectors - Listar
  let sectorsListRes = http.get(`${BASE_URL}/finance/sectors`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  check(sectorsListRes, { 'sectors list 200': (r) => r.status === 200 });
  let sectorsList = sectorsListRes.json();
  check(sectorsList, { 'sectors items is array': (r) => Array.isArray(r.items) });
  console.log('Sectors list:', sectorsListRes.body);

  // 7. Employees - Listar
  let employeesListRes = http.get(`${BASE_URL}/core/employees`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  check(employeesListRes, { 'employees list 200': (r) => r.status === 200 });
  let employeesList = employeesListRes.json();
  check(employeesList, { 'employees items is array': (r) => Array.isArray(r.items) });
  console.log('Employees list:', employeesListRes.body);

  sleep(1);
}
