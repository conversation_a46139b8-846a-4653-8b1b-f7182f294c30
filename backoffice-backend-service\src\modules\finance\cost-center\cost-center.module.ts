import { Module } from '@nestjs/common';
import { PrismaModule } from '../../../infrastructure/prisma/prisma.module';
import { CostCenterController } from './controllers/cost-center.controller';
import { CostCenterService } from './services/cost-center.service';
import { CreateCostCenterUseCase } from '@/core/application/use-cases/cost-center/create-cost-center.use-case';
import { COST_CENTER_REPOSITORY } from '@/core/ports/repositories/cost-center-repository.port';
import { PrismaCostCenterRepository } from '@/infrastructure/repositories/prisma-cost-center.repository';
import { DeleteCostCenterUseCase } from '@/core/application/use-cases/cost-center/delete-cost-center.use-case';
import { UpdateCostCenterUseCase } from '@/core/application/use-cases/cost-center/update-cost-center.use-case';
import { ListCostCentersUseCase } from '@/core/application/use-cases/cost-center/list-cost-centers.use-case';

@Module({
  imports: [PrismaModule],
  controllers: [CostCenterController],
  providers: [
    CostCenterService,
    CreateCostCenterUseCase,
    DeleteCostCenterUseCase,
    UpdateCostCenterUseCase,
    ListCostCentersUseCase,
    {
      provide: COST_CENTER_REPOSITORY,
      useClass: PrismaCostCenterRepository,
    },
  ],
  exports: [CostCenterService],
})
export class CostCenterModule {}
