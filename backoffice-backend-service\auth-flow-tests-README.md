# Authentication Flow Testing Guide

## Overview

This document explains how to test the authentication flow in our backend service, which uses Keycloak for identity management. It covers common issues, particularly token expiration, and provides solutions for both testing and production environments.

## Authentication Flow

The complete authentication flow consists of three main steps:

1. **Registration**: Create a new user in both Keycloak and our database
2. **Login**: Authenticate the user and obtain access and refresh tokens
3. **Logout**: Invalidate the tokens when the user logs out

## Common Issues

### Token Expiration

By default, Keycloak configures access tokens with a very short lifespan (60 seconds), which can cause test failures if logout operations are executed after this period. This is especially problematic in E2E tests where multiple operations are performed sequentially.

When a token expires, you'll typically see errors like:
- `Token de atualização inválido` (Invalid refresh token)
- `401 Unauthorized` responses when trying to access protected endpoints

## Solutions

### For Testing

1. **Combined Tests**: Keep login and logout operations in the same test to ensure they execute quickly before token expiration.

2. **Error Handling**: Implement robust error handling in tests to gracefully handle token expiration:

```typescript
try {
  await keycloakService.logout(refreshToken);
  console.log('Logout successful via KeycloakService');
} catch (error) {
  console.error('Error during logout via KeycloakService:', error);
  
  // Try alternative logout method
  try {
    await request(app.getHttpServer())
      .post('/auth/logout')
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ refresh_token: refreshToken })
      .expect(204);
    
    console.log('Logout successful via API HTTP');
  } catch (httpError) {
    console.warn('Logout via API HTTP also failed, but this is expected in test environments');
  }
}
```

### For Production

To avoid token expiration issues in production, configure Keycloak with longer token lifespans:

1. Access the Keycloak admin console: http://localhost:8080/admin
2. Navigate to **Realm Settings** > **Tokens** tab
3. Increase the following settings:
   - **Access Token Lifespan**: Set to at least 1 hour (3600 seconds)
   - **Client Session Idle**: Set to at least 8 hours
   - **Client Session Max**: Set to at least 24 hours
4. Click **Save**

![Keycloak Token Settings](https://keycloak.org/docs/latest/server_admin/keycloak-images/realm-tokens.png)

## Testing with Postman

For manual testing, we've created a Postman collection that covers the complete authentication flow:

1. Import the `auth-flow-collection.postman_collection.json` file into Postman
2. Set up the environment variables using `auth-flow-environment.postman_environment.json`
3. Run the collection in sequence:
   - Register User
   - Login
   - Access Protected Resource
   - Logout
   - Verify Logout (should return 401)

## Running E2E Tests

To run the authentication flow E2E test:

```bash
npm run test src/modules/auth/test/e2e/auth-flow.e2e-spec.ts
```

This test verifies the complete flow from registration to logout, ensuring that our authentication system works correctly.

## Troubleshooting

If you encounter issues with token validation or expiration:

1. Check that Keycloak is running and accessible
2. Verify that token lifespans are configured appropriately
3. Ensure that logout operations are executed promptly after login
4. Check for any network issues that might delay requests

For persistent issues, consider implementing a token refresh mechanism that automatically refreshes expired tokens before making requests. 