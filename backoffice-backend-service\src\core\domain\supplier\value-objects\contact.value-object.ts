export class Contact {
  private readonly _email: string;
  private readonly _phone: string;

  constructor(email: string, phone: string) {
    this._email = email;
    this._phone = phone;
    this.validate();
  }

  private validate(): void {
    if (!this._email) throw new Error('Email is required');
    if (!this._phone) throw new Error('Phone is required');

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this._email)) {
      throw new Error('Invalid email format');
    }

    const phoneRegex = /^\(\d{2}\) \d{4,5}-\d{4}$/;
    if (!phoneRegex.test(this._phone)) {
      throw new Error(
        'Invalid phone format. Use: (XX) XXXX-XXXX or (XX) XXXXX-XXXX',
      );
    }
  }

  get email(): string {
    return this._email;
  }

  get phone(): string {
    return this._phone;
  }

  toJSON(): Record<string, string> {
    return {
      email: this._email,
      phone: this._phone,
    };
  }
}
