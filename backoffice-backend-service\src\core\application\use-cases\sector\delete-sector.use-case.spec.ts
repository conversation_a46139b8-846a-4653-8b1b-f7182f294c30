import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { SectorRepositoryPort } from '@core/ports/repositories/sector-repository.port';
import { SECTOR_REPOSITORY } from '@/core/constants/injection-tokens';
import { Sector } from '@/core/domain/entities/sector.entity';
import { DeleteSectorUseCase } from './delete-sector.use-case';

describe('DeleteSectorUseCase', () => {
  let useCase: DeleteSectorUseCase;
  let repository: jest.Mocked<SectorRepositoryPort>;

  const mockSector = Sector.create(
    1,
    'test-uuid',
    'test-code',
    'test-description',
    'test-created-by',
    'test-updated-by',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SectorRepositoryPort> = {
      findById: jest.fn(),
      update: jest.fn(),
      findByUuid: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
      findByCode: jest.fn(),
      list: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeleteSectorUseCase,
        {
          provide: SECTOR_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<DeleteSectorUseCase>(DeleteSectorUseCase);
    repository =
      module.get<jest.Mocked<SectorRepositoryPort>>(SECTOR_REPOSITORY);
  });

  describe('execute', () => {
    it('should delete sector when found by UUID', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');
      const deleteFn = jest.spyOn(repository, 'delete');

      findByUuid.mockResolvedValue(mockSector);

      await useCase.execute({ uuid: 'test-uuid' });

      expect(findByUuid).toHaveBeenCalledWith('test-uuid');
      expect(deleteFn).toHaveBeenCalledWith('test-uuid');
    });

    it('should throw SectorNotFoundError when sector does not exist', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');

      findByUuid.mockResolvedValue(null);

      await expect(
        useCase.execute({ uuid: 'non-existent-uuid' }),
      ).rejects.toThrow(NotFoundException);
      expect(findByUuid).toHaveBeenCalledWith('non-existent-uuid');
    });
  });
});
