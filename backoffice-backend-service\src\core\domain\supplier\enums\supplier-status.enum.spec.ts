import { SupplierStatus } from './supplier-status.enum';

describe('SupplierStatus Enum', () => {
  it('should have the correct values', () => {
    expect(SupplierStatus.ACTIVE).toBe('ACTIVE');
    expect(SupplierStatus.INACTIVE).toBe('INACTIVE');
    expect(SupplierStatus.PENDING).toBe('PENDING');
  });

  it('should have exactly three status options', () => {
    const statusValues = Object.values(SupplierStatus);
    expect(statusValues).toHaveLength(3);
    expect(statusValues).toContain('ACTIVE');
    expect(statusValues).toContain('INACTIVE');
    expect(statusValues).toContain('PENDING');
  });

  it('should have the correct keys', () => {
    const statusKeys = Object.keys(SupplierStatus);
    expect(statusKeys).toHaveLength(3);
    expect(statusKeys).toContain('ACTIVE');
    expect(statusKeys).toContain('INACTIVE');
    expect(statusKeys).toContain('PENDING');
  });

  it('should not allow invalid status values', () => {
    const validStatuses = Object.values(SupplierStatus);
    const invalidStatus = 'pending' as SupplierStatus;

    expect(validStatuses).not.toContain(invalidStatus);
  });
});
