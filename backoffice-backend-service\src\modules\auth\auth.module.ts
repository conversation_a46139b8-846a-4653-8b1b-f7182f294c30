import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { PrismaModule } from '../../infrastructure/prisma/prisma.module';
import { UsersModule } from '../users/users.module';
import { PrismaUserRepository } from '../../infrastructure/repositories/prisma-user.repository';
import { KeycloakModule } from '../../infrastructure/keycloak/keycloak.module';
import { DomainEventsModule } from '../../infrastructure/events/domain-events.module';
import { PrismaPasswordResetTokenRepository } from './repositories/password-reset-token.repository';
import { EmailModule } from '../../infrastructure/email/email.module';
import { EmployeeModule } from '../finance/employee/employee.module';
import { EmployeeService } from '../finance/employee/employee.service';
import { CustomersModule } from '../customers/customers.module';
import { CustomerRepository } from '../customers/infrastructure/repositories/customer.repository';
import { SupplierAuthModule } from './supplier-auth/supplier-auth.module';
import { PrismaSupplierRepository } from '@/infrastructure/repositories/prisma-supplier.repository';
import { PrismaEmployeeRepository } from '@/infrastructure/repositories/prisma-employee.repository';
import { PrismaUsersOtpRepository } from './repositories/users-otp.respository';

@Module({
  imports: [
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: `${configService.get<number>('JWT_EXPIRATION')}s`,
        },
      }),
    }),
    PrismaModule,
    UsersModule,
    KeycloakModule,
    DomainEventsModule,
    ConfigModule,
    EmailModule,
    EmployeeModule,
    CustomersModule,
    SupplierAuthModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    LocalStrategy,
    {
      provide: 'UserRepository',
      useClass: PrismaUserRepository,
    },
    {
      provide: 'PasswordResetTokenRepository',
      useClass: PrismaPasswordResetTokenRepository,
    },
    {
      provide: 'UsersOtpRepository',
      useClass: PrismaUsersOtpRepository,
    },
    EmployeeService,
    CustomerRepository,
    PrismaSupplierRepository,
    PrismaEmployeeRepository,
  ],
  exports: [AuthService, PrismaEmployeeRepository],
})
export class AuthModule { }
