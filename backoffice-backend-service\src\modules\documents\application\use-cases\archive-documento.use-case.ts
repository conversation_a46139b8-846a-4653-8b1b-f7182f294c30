import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { IDocumentRepository } from '../../domain/repositories/document.repository.interface';
import { DocumentStatus } from '../../domain/enums/document-status.enum';

@Injectable()
export class ArchiveDocumentUseCase {
  constructor(
    @Inject('IDocumentRepository')
    private readonly documentRepository: IDocumentRepository,
  ) {}

  async execute(
    uuid: string,
    archivedBy: string,
    updatedAt: Date,
  ): Promise<void> {
    const document = await this.documentRepository.findByUuid(uuid);

    if (!document) {
      throw new NotFoundException('Documento não encontrado');
    }

    if (document.status === DocumentStatus.ARCHIVED) {
      throw new BadRequestException('Document com status archived');
    }

    await this.documentRepository.updateStatusToArchived(
      uuid,
      archivedBy,
      updatedAt,
    );
  }
}
