/* eslint-disable @typescript-eslint/unbound-method */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { Test, TestingModule } from '@nestjs/testing';
import { AuditPublisherService } from './audit-publisher.service';
import { RabbitMQService } from '@/infrastructure/messaging/rabbitmq/rabbitmq.service';
import { AuditEvent, AuditEventType } from './audit.interface';

describe('AuditPublisherService', () => {
  let service: AuditPublisherService;
  let rabbitMQService: jest.Mocked<RabbitMQService>;

  const mockRabbitMQService = {
    bindQueue: jest.fn(),
    publish: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuditPublisherService,
        {
          provide: RabbitMQService,
          useValue: mockRabbitMQService,
        },
      ],
    }).compile();

    service = module.get<AuditPublisherService>(AuditPublisherService);
    rabbitMQService = module.get<RabbitMQService>(
      RabbitMQService,
    ) as jest.Mocked<RabbitMQService>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should setup queues and bindings on initialization', () => {
    // Service is already initialized in beforeEach
    expect(rabbitMQService.bindQueue).toHaveBeenCalledWith(
      'audit.log.queue',
      'audit.events',
      'audit.action',
    );
  });

  it('should publish audit event successfully', async () => {
    const auditEvent: AuditEvent = {
      eventType: AuditEventType.AUDIT_LOG,
      timestamp: '2023-01-01T00:00:00Z',
      username: '<EMAIL>',
      method: 'POST',
      endpoint: '/api/test',
      body: { test: 'data' },
      ip: '127.0.0.1',
      userAgent: 'test-agent',
    };

    (rabbitMQService.publish as jest.Mock).mockResolvedValue(undefined);

    await service.publish(auditEvent);

    expect(rabbitMQService.publish).toHaveBeenCalledWith(
      'audit.events',
      'audit.action',
      auditEvent,
      {
        persistent: true,
        contentType: 'application/json',
      },
    );
  });

  it('should handle publish errors', async () => {
    const auditEvent: AuditEvent = {
      eventType: AuditEventType.AUDIT_LOG,
      timestamp: '2023-01-01T00:00:00Z',
      username: '<EMAIL>',
      method: 'POST',
      endpoint: '/api/test',
    };

    const error = new Error('RabbitMQ connection failed');
    (rabbitMQService.publish as jest.Mock).mockRejectedValue(error);

    await expect(service.publish(auditEvent)).rejects.toThrow(
      'RabbitMQ connection failed',
    );
  });

  it('should publish event with minimal required fields', async () => {
    const auditEvent: AuditEvent = {
      eventType: AuditEventType.AUDIT_LOG,
      timestamp: '2023-01-01T00:00:00Z',
      username: '<EMAIL>',
      method: 'GET',
      endpoint: '/api/minimal',
    };

    (rabbitMQService.publish as jest.Mock).mockResolvedValue(undefined);

    await service.publish(auditEvent);

    expect(rabbitMQService.publish).toHaveBeenCalledWith(
      'audit.events',
      'audit.action',
      auditEvent,
      {
        persistent: true,
        contentType: 'application/json',
      },
    );
  });

  it('should preserve all event properties when publishing', async () => {
    const auditEvent: AuditEvent = {
      eventType: AuditEventType.AUDIT_LOG,
      timestamp: '2023-01-01T00:00:00Z',
      username: '<EMAIL>',
      method: 'PUT',
      endpoint: '/api/update',
      headers: { 'Content-Type': 'application/json' },
      body: { id: 1, name: 'updated' },
      ip: '***********',
      userAgent: 'Mozilla/5.0',
    };

    rabbitMQService.publish.mockResolvedValue(undefined);

    await service.publish(auditEvent);

    const publishedEvent = (rabbitMQService.publish as jest.Mock).mock
      .calls[0][2];
    expect(publishedEvent).toEqual(auditEvent);
  });
});
