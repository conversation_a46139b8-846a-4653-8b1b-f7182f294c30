import { SupplierStatus, SupplierType } from '@prisma/client';
import { Supplier } from '../../domain/supplier/entities/supplier.entity';

export interface SupplierRepositoryPort {
  create(supplier: Supplier): Promise<Supplier>;
  findByUserId(userId: string): Promise<Supplier | null>;
  findByDocument(document: string): Promise<Supplier | null>;
  findById(id: string): Promise<Supplier | null>;
  findAll(): Promise<Supplier[]>;
  update(supplier: Supplier): Promise<Supplier>;
  delete(id: string): Promise<void>;
  findWithPagination(params: {
    limit: number;
    offset: number;
    cnpj?: string;
    name?: string;
    type?: SupplierType;
    status?: SupplierStatus;
  }): Promise<{ items: Supplier[]; total: number }>;
}
