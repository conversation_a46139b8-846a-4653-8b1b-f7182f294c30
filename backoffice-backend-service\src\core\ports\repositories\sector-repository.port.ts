import { Sector } from '../../domain/entities/sector.entity';

export interface ListSectorsCriteria {
  limit: number;
  offset: number;
  code?: string;
  description?: string;
}

export interface ListSectorsResult {
  items: Sector[];
  total: number;
}

export interface SectorRepositoryPort {
  findByCode(code: string): Promise<Sector | null>;
  findByUuid(uuid: string): Promise<Sector | null>;
  findById(id: number): Promise<Sector | null>;
  save(sector: Sector): Promise<Sector>;
  update(sector: Sector): Promise<Sector>;
  delete(uuid: string): Promise<void>;
  list(criteria: ListSectorsCriteria): Promise<ListSectorsResult>;
}
