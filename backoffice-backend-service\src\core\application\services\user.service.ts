import { UserRepository } from '../../ports/repositories/user-repository.interface';
import { CreateUserUseCase } from '../use-cases/user/create-user.use-case';
import { UpdateUserUseCase } from '../use-cases/user/update-user.use-case';
import { User } from '../../domain/user.entity';
import { Role } from '../../domain/role.enum';
import { KeycloakIdentityProviderService } from '@/infrastructure/keycloak/keycloak-identity-provider.service';

export class UserService {
  private createUserUseCase: CreateUserUseCase;
  private updateUserUseCase: UpdateUserUseCase;

  constructor(
    private userRepository: UserRepository,
    private keycloakIdentityProvider: KeycloakIdentityProviderService,
  ) {
    this.createUserUseCase = new CreateUserUseCase(
      userRepository,
      keycloakIdentityProvider,
    );
    this.updateUserUseCase = new UpdateUserUseCase(userRepository);
  }

  // Métodos que delegam para os casos de uso

  async findAll(): Promise<User[]> {
    return this.userRepository.findAll();
  }

  async findById(id: string): Promise<User | null> {
    return this.userRepository.findById(id);
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findByEmail(email);
  }

  async findByKeycloakId(keycloakId: string): Promise<User | null> {
    return this.userRepository.findByKeycloakId(keycloakId);
  }

  async create(
    name: string,
    email: string,
    password: string,
    role?: Role,
  ): Promise<User> {
    return this.createUserUseCase.execute({ name, email, password, role });
  }

  async update(id: string, name?: string, password?: string): Promise<User> {
    return this.updateUserUseCase.execute({ id, name, password });
  }

  async delete(id: string): Promise<void> {
    // Verificar se o usuário existe
    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new Error(`Usuário com ID ${id} não encontrado`);
    }

    return this.userRepository.delete(id);
  }
}
