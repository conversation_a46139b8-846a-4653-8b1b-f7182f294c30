import { ListPaymentMethodUseCase } from './list-payment-methods-use-case';
import { PaymentMethodRepository } from '../../../ports/repositories/payment-method-repository.interface';
import { PaymentMethod } from '../../../domain/payment-method';

describe('ListPaymentMethodsUseCase', () => {
  let useCase: ListPaymentMethodUseCase;
  let PaymentMethodRepository: jest.Mocked<PaymentMethodRepository>;

  const mockPaymentMethod = new PaymentMethod(
    1,
    '550e8400-e29b-41d4-a716-************',
    'Test Payment Method',
    'Test Description',
    'Test User',
    new Date(),
    new Date(),
    'Test User',
  );

  beforeEach(() => {
    const mockRepository: jest.Mocked<PaymentMethodRepository> = {
      findWithPagination: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByLabel: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findByUuid: jest.fn(),
    };
    PaymentMethodRepository = mockRepository;
    useCase = new ListPaymentMethodUseCase(PaymentMethodRepository);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should return empty paginated PaymentMethods with id filter', async () => {
      const mockResult = {
        items: [],
        total: 0,
      };

      const findWithPagination = jest.spyOn(
        PaymentMethodRepository,
        'findWithPagination',
      );

      PaymentMethodRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        id: '550e8400-e29b-41d4-a716-************',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        id: '550e8400-e29b-41d4-a716-************',
      });
    });
    it('should return paginated PaymentMethods without filters', async () => {
      const mockResult = {
        items: [mockPaymentMethod],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        PaymentMethodRepository,
        'findWithPagination',
      );

      PaymentMethodRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
      });
    });

    it('should return paginated PaymentMethods with Label filter', async () => {
      const mockResult = {
        items: [mockPaymentMethod],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        PaymentMethodRepository,
        'findWithPagination',
      );

      PaymentMethodRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        label: 'credit card',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        label: 'credit card',
      });
    });

    it('should return paginated PaymentMethods with id filter', async () => {
      const mockResult = {
        items: [mockPaymentMethod],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        PaymentMethodRepository,
        'findWithPagination',
      );

      PaymentMethodRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        id: '550e8400-e29b-41d4-a716-************',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        id: '550e8400-e29b-41d4-a716-************',
      });
    });
  });
});
