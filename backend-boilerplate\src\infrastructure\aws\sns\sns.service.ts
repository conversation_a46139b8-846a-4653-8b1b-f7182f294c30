import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  SNSClient,
  PublishCommand,
  PublishBatchCommand,
  PublishBatchRequestEntry,
  SubscribeCommand,
  ListSubscriptionsByTopicCommand,
  CreateTopicCommand,
  ListSubscriptionsCommand,
  ListTopicsCommand,
  MessageAttributeValue,
  Topic,
  Subscription,
} from '@aws-sdk/client-sns';

// Interface para mensagens em lote
export interface SNSBatchMessage {
  id?: string;
  message: Record<string, unknown>;
  subject?: string;
}

// Interface para dados de usuário
export interface UserData {
  id: string;
  email: string;
  name: string;
  role: string;
  [key: string]: unknown;
}

// Interface para evento de usuário criado
export interface UserCreatedEvent extends Record<string, unknown> {
  eventType: string;
  data: UserData;
  timestamp: string;
}

@Injectable()
export class SNSService {
  private readonly snsClient: SNSClient;

  constructor(private readonly configService: ConfigService) {
    this.snsClient = new SNSClient({
      region: this.configService.get<string>('AWS_REGION', 'us-east-1'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID', ''),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
          '',
        ),
      },
    });
  }

  /**
   * Publica uma mensagem em um tópico SNS
   * @param topicArn ARN do tópico SNS
   * @param message Mensagem a ser publicada
   * @param subject Assunto da mensagem (opcional)
   */
  async publish<T extends Record<string, unknown>>(
    topicArn: string,
    message: T,
    subject?: string,
  ): Promise<string> {
    try {
      const command = new PublishCommand({
        TopicArn: topicArn,
        Message: JSON.stringify(message),
        Subject: subject,
      });

      const response = await this.snsClient.send(command);
      console.log(`Mensagem publicada no SNS com ID: ${response.MessageId}`);

      return response.MessageId || '';
    } catch (error) {
      console.error('Erro ao publicar mensagem no SNS:', error);
      throw error;
    }
  }

  /**
   * Publica várias mensagens em um tópico SNS de uma vez
   * @param topicArn ARN do tópico SNS
   * @param messages Array de mensagens a serem publicadas
   */
  async publishBatch(
    topicArn: string,
    messages: SNSBatchMessage[],
  ): Promise<{ successful: string[]; failed: string[] }> {
    try {
      const entries: PublishBatchRequestEntry[] = messages.map(
        (msg, index) => ({
          Id: msg.id || `msg-${index}`,
          Message: JSON.stringify(msg.message),
          Subject: msg.subject,
        }),
      );

      const command = new PublishBatchCommand({
        TopicArn: topicArn,
        PublishBatchRequestEntries: entries,
      });

      const response = await this.snsClient.send(command);

      if (response.Successful && response.Failed) {
        const successful = response.Successful.map((s) => s.Id || '');
        const failed = response.Failed.map((f) => f.Id || '');

        console.log(
          `Mensagens publicadas em lote no SNS ${topicArn}. Sucesso: ${successful.length}, Falhas: ${failed.length}`,
        );

        return { successful, failed };
      }

      return { successful: [], failed: [] };
    } catch (error) {
      console.error('Erro ao publicar mensagens em lote no SNS:', error);
      throw error;
    }
  }

  /**
   * Cria um novo tópico SNS
   * @param name Nome do tópico a ser criado
   * @param attributes Atributos do tópico (opcional)
   */
  async createTopic(
    name: string,
    attributes: Record<string, string> = {},
  ): Promise<string> {
    try {
      const command = new CreateTopicCommand({
        Name: name,
        Attributes: attributes,
      });

      const response = await this.snsClient.send(command);
      console.log(`Tópico SNS ${name} criado com ARN: ${response.TopicArn}`);

      return response.TopicArn || '';
    } catch (error) {
      console.error(`Erro ao criar tópico SNS ${name}:`, error);
      throw error;
    }
  }

  /**
   * Inscreve um endpoint (SQS, Lambda, HTTP, etc.) em um tópico SNS
   * @param topicArn ARN do tópico SNS
   * @param protocol Protocolo da inscrição (sqs, lambda, http, https, application, email, etc.)
   * @param endpoint Endpoint para receber as notificações
   * @param attributes Atributos da inscrição (opcional)
   */
  async subscribe(
    topicArn: string,
    protocol: string,
    endpoint: string,
    attributes: Record<string, string> = {},
  ): Promise<string> {
    try {
      const command = new SubscribeCommand({
        TopicArn: topicArn,
        Protocol: protocol,
        Endpoint: endpoint,
        Attributes: attributes,
      });

      const response = await this.snsClient.send(command);
      console.log(
        `Assinatura criada para tópico ${topicArn} com ARN: ${response.SubscriptionArn}`,
      );

      return response.SubscriptionArn || '';
    } catch (error) {
      console.error(`Erro ao assinar tópico SNS ${topicArn}:`, error);
      throw error;
    }
  }

  /**
   * Lista as inscrições em um tópico SNS
   * @param topicArn ARN do tópico SNS
   */
  async listSubscriptions(topicArn?: string): Promise<Subscription[]> {
    try {
      let command;

      if (topicArn) {
        command = new ListSubscriptionsByTopicCommand({
          TopicArn: topicArn,
        });
      } else {
        command = new ListSubscriptionsCommand({});
      }

      const response = await this.snsClient.send(command);

      if ('Subscriptions' in response && response.Subscriptions) {
        return response.Subscriptions;
      }

      return [];
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      console.error('Erro ao listar assinaturas SNS:', errorMessage);
      throw new Error(`Falha ao listar assinaturas SNS: ${errorMessage}`);
    }
  }

  /**
   * Exemplo: Publicação de evento de usuário criado
   * @param userData Dados do usuário
   */
  async publishUserCreatedEvent(userData: UserData): Promise<string> {
    const userTopicArn =
      this.configService.get<string>('SNS_USER_TOPIC_ARN') || '';

    // Definindo explicitamente o tipo dos dados
    const eventData: UserCreatedEvent = {
      eventType: 'USER_CREATED',
      data: userData,
      timestamp: new Date().toISOString(),
    };

    return this.publish(userTopicArn, eventData, 'Novo usuário criado');
  }

  private formatMessageAttributes(
    attributes: Record<string, unknown>,
  ): Record<string, MessageAttributeValue> {
    const result: Record<string, MessageAttributeValue> = {};

    for (const [key, value] of Object.entries(attributes)) {
      let dataType = 'String';
      let stringValue = '';

      if (typeof value === 'number') {
        dataType = 'Number';
        stringValue = value.toString();
      } else if (typeof value === 'boolean') {
        stringValue = value.toString();
      } else if (value === null) {
        stringValue = 'null';
      } else if (value === undefined) {
        stringValue = '';
      } else if (typeof value === 'object') {
        try {
          const jsonString = JSON.stringify(value);
          stringValue = jsonString || '[Objeto vazio]';
        } catch {
          // Ignoramos o erro de tipagem aqui
          stringValue = '[Objeto Não Serializável]';
        }
      } else if (typeof value === 'string') {
        stringValue = value;
      } else if (typeof value === 'function') {
        stringValue = '[Função]';
      } else {
        // Para qualquer outro tipo, usamos conversão segura
        try {
          // Conversão segura para evitar o erro no-base-to-string
          if (value !== undefined && value !== null) {
            stringValue = `${value}`;
          } else {
            stringValue = '[Valor desconhecido]';
          }
        } catch {
          // Ignoramos o erro de tipagem aqui
          stringValue = '[Valor desconhecido]';
        }
      }

      result[key] = {
        DataType: dataType,
        StringValue: stringValue,
      };
    }

    return result;
  }

  async publishToUserDevices(
    userTopicArn: string,
    message: Record<string, unknown>,
    subject: string,
  ): Promise<void> {
    if (!userTopicArn) {
      console.error('ARN do tópico do usuário não fornecido');
      return;
    }

    await this.publish(userTopicArn, message, subject);
  }

  /**
   * Lista todos os tópicos SNS disponíveis
   */
  async listTopics(): Promise<Topic[]> {
    try {
      const command = new ListTopicsCommand({});
      const response = await this.snsClient.send(command);

      if (response.Topics) {
        return response.Topics;
      }

      return [];
    } catch (error) {
      console.error('Erro ao listar tópicos SNS:', error);
      throw error;
    }
  }
}
