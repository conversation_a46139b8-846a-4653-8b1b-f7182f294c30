import { EMPLOYEE_REPOSITORY, EmployeeRepositoryPort } from "@/core/ports/repositories/employee-repository.port";
import { IStorageProvider } from "@/core/ports/storage/storage-provider.port";
import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { PersonalDocumentWithUrl } from "./upload-employee-personal-document.use-case";
import { PersonalDocument } from "@/core/domain/entities/employee.entity";

@Injectable()
export class ListPersonalDocumentsByEmployeeUuidUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) { }

  async execute(uuid: string): Promise<PersonalDocumentWithUrl[]> {
    const expiresIn = 3600;
    const employee = await this.employeeRepository.findByUuid(uuid);
    if (!employee) {
      throw new NotFoundException('Funcion<PERSON>rio não encontrado.');
    }
    const personalDocuments = employee.personalDocuments as unknown as PersonalDocument[];
    return Promise.all(personalDocuments.map(async (personalDocument) => {
      // Only try to get download URL if file exists
      if (personalDocument.filePath && personalDocument.fileName) {
        try {
          const downloadUrl = await this.storageProvider.getDownloadUrl(personalDocument.filePath, personalDocument.fileName, expiresIn);
          return {
            ...personalDocument,
            downloadUrl: downloadUrl || undefined,
          } as unknown as PersonalDocumentWithUrl;
        } catch (error) {
          // If error getting URL, return document without downloadUrl
          return {
            ...personalDocument,
            downloadUrl: undefined,
          } as unknown as PersonalDocumentWithUrl;
        }
      }
      // Document exists but no file uploaded yet
      return {
        ...personalDocument,
        downloadUrl: undefined,
      } as unknown as PersonalDocumentWithUrl;
    }));
  }
}