declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        active: boolean;
        scope: string;
        client_id: string;
        username: string;
        exp: number;
        iat: number;
        sub: string;
        aud: string;
        iss: string;
        jti: string;
        azp: string;
        realm_access?: {
          roles: string[];
        };
        resource_access?: {
          [key: string]: {
            roles: string[];
          };
        };
      };
    }
  }
} 