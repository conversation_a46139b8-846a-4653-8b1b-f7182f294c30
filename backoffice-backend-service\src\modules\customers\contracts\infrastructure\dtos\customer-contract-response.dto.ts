import { ApiProperty } from '@nestjs/swagger';
import { CustomerContract } from '../../domain/entities/customer-contract.entity';
import { ContractStatus } from '@prisma/client';

export class CustomerContractResponseDto {
  @ApiProperty()
  uuid: string;

  @ApiProperty()
  customerUuid: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  isSigned: boolean;

  @ApiProperty({ description: 'URL para download do contrato', required: false })
  downloadUrl?: string | null;

  @ApiProperty({ description: 'Nome do arquivo do contrato', required: false })
  fileName?: string | null;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ description: 'Status do contrato' })
  status: ContractStatus;

  static fromEntity(contract: CustomerContract, downloadUrl?: string | null, fileName?: string | null): CustomerContractResponseDto {
    return {
      uuid: contract.uuid,
      customerUuid: contract.customerUuid,
      name: contract.name,
      isSigned: contract.isSigned,
      downloadUrl: downloadUrl || null,
      fileName: fileName || null,
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
      status: contract.status,
    };
  }
} 