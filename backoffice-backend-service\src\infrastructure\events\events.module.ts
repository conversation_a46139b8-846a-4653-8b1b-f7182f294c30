import { Module, Global } from '@nestjs/common';
import { EventEmitterService } from './event-emitter.service';
import { EventPublisherService } from './event-publisher.service';
import { AwsModule } from '../aws/aws.module';

@Global()
@Module({
  imports: [AwsModule],
  providers: [EventEmitterService, EventPublisherService],
  exports: [EventEmitterService, EventPublisherService],
})
export class EventsModule {}
