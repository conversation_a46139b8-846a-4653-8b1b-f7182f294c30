import { Test, TestingModule } from '@nestjs/testing';
import { CostCenterController } from '../controllers/cost-center.controller';
import { CostCenterService } from '../services/cost-center.service';
import { CreateCostCenterDto } from '../dto/create-cost-center.dto';
import { CostCenterResponseDto } from '../dto/cost-center-response.dto';
import { ConfigService } from '@nestjs/config';
import { UpdateCostCenterDto } from '../dto/update-cost-center.dto';

// Definindo uma interface parcial para o teste
type PartialAuthenticatedRequest = {
  user: { id: string };
};

describe('CostCenterController', () => {
  let controller: CostCenterController;
  let service: CostCenterService;

  // Definindo o tipo do mock do serviço
  const mockCostCenterService = {
    create: jest.fn(),
    delete: jest.fn(),
    update: jest.fn(),
  };

  // Mock do ConfigService para o JwtAuthGuard
  const mockConfigService = {
    get: jest.fn().mockImplementation((key: string) => {
      if (key === 'jwt.secret') return 'test-secret';
      if (key === 'jwt.expiresIn') return '1h';
      return null;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CostCenterController],
      providers: [
        {
          provide: CostCenterService,
          useValue: mockCostCenterService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    controller = module.get<CostCenterController>(CostCenterController);
    service = module.get<CostCenterService>(CostCenterService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a cost center successfully', async () => {
      // Arrange
      const createDto: CreateCostCenterDto = {
        description: 'Test Cost Center',
        createdBy: '',
        updatedBy: '',
      };

      // Criando um objeto que corresponde parcialmente à interface AuthenticatedRequest
      const req = {
        user: {
          id: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        },
      } as PartialAuthenticatedRequest;

      const expectedResponse: CostCenterResponseDto = {
        uuid: '11111111-**************-************',
        description: 'Test Cost Center',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        createdAt: '2025-05-12T00:00:00Z',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedAt: '2025-05-12T00:00:00Z',
      };

      mockCostCenterService.create.mockResolvedValue(expectedResponse);

      // Act
      // Usando uma função arrow para evitar problema de unbound method
      const controllerCreateFn = (
        ...args: Parameters<typeof controller.create>
      ) => controller.create(...args);
      const result = await controllerCreateFn(
        createDto,
        req as unknown as never,
      );

      // Assert
      expect(createDto.createdBy).toBe('aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee');
      expect(createDto.updatedBy).toBe('aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee');
      // Usando uma função arrow para evitar problema de unbound method
      const serviceCreateFn = jest.spyOn(service, 'create');
      expect(serviceCreateFn).toHaveBeenCalledWith(createDto);
      expect(result).toEqual(expectedResponse);
    });
  });

  describe('delete', () => {
    it('should delete a cost center successfully', async () => {
      // Arrange
      const uuid = '11111111-**************-************';

      // Act
      await controller.delete(uuid);

      // Assert
      const serviceDeleteFn = jest.spyOn(service, 'delete');
      expect(serviceDeleteFn).toHaveBeenCalledWith(uuid);
    });

    it('should throw an error if service throws', async () => {
      // Arrange
      const uuid = '11111111-**************-************';

      const error = new Error('Test error');
      mockCostCenterService.delete.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.delete(uuid)).rejects.toThrow(error);
    });
  });

  describe('update', () => {
    it('should update a cost center successfully', async () => {
      // Arrange
      const updateDto: UpdateCostCenterDto = {
        description: 'Updated Cost Center',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      };

      const expectedResponse: CostCenterResponseDto = {
        uuid: '11111111-**************-************',
        description: 'Updated Cost Center',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        createdAt: '2025-05-12T00:00:00Z',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedAt: '2025-05-12T00:00:00Z',
      };

      mockCostCenterService.update.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.update(
        '11111111-**************-************',
        updateDto,
      );

      // Assert
      expect(result).toEqual(expectedResponse);
    });
  });
});
