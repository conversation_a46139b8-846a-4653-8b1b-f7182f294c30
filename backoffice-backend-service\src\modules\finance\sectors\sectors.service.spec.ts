import { Test, TestingModule } from '@nestjs/testing';
import { SectorsService } from './sectors.service';
import { CreateSectorUseCase } from '../../../core/application/use-cases/sector/create-sector.use-case';
import { SECTOR_REPOSITORY } from '../../../core/constants/injection-tokens';
import { NotFoundException } from '@nestjs/common';
import { Sector } from '../../../core/domain/entities/sector.entity';
import { SectorRepositoryPort } from '../../../core/ports/repositories/sector-repository.port';
import { UpdateSectorUseCase } from '@/core/application/use-cases/sector/update-sector.use-case';
import { DeleteSectorUseCase } from '@/core/application/use-cases/sector/delete-sector.use-case';
import {
  ListSectorsUseCase,
  ListSectorsInput,
} from '../../../core/application/use-cases/sector/list-sectors.use-case';
import { ListSectorsResult } from '../../../core/ports/repositories/sector-repository.port';

describe('SectorsService', () => {
  let service: SectorsService;
  let mockExecute: jest.Mock;
  let mockUpdateExecute: jest.Mock;
  let mockDeleteExecute: jest.Mock;
  let mockSectorRepository: jest.Mocked<SectorRepositoryPort>;
  let mockListSectorsUseCase: {
    execute: jest.Mock<Promise<ListSectorsResult>, [ListSectorsInput]>;
  };

  const mockSector = Sector.create(
    1,
    'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
    'HR',
    'Human Resources',
    'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
    'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    mockExecute = jest.fn().mockResolvedValue(mockSector);
    mockUpdateExecute = jest.fn().mockResolvedValue(mockSector);
    mockDeleteExecute = jest.fn().mockResolvedValue(undefined);
    mockSectorRepository = {
      findById: jest.fn(),
      save: jest.fn(),
      findByCode: jest.fn(),
      findByUuid: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      list: jest.fn().mockResolvedValue({ items: [mockSector], total: 1 }),
    };

    mockListSectorsUseCase = {
      execute: jest
        .fn<Promise<ListSectorsResult>, [ListSectorsInput]>()
        .mockResolvedValue({ items: [mockSector], total: 1 }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SectorsService,
        {
          provide: CreateSectorUseCase,
          useValue: {
            execute: mockExecute,
          },
        },
        {
          provide: UpdateSectorUseCase,
          useValue: {
            execute: mockUpdateExecute,
          },
        },
        {
          provide: DeleteSectorUseCase,
          useValue: {
            execute: mockDeleteExecute,
          },
        },
        {
          provide: SECTOR_REPOSITORY,
          useValue: mockSectorRepository,
        },
        {
          provide: ListSectorsUseCase,
          useValue: mockListSectorsUseCase,
        },
      ],
    }).compile();

    service = module.get<SectorsService>(SectorsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSector', () => {
    it('should create a sector successfully', async () => {
      const input = {
        code: 'HR',
        description: 'Human Resources',
        createdBy: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
        updatedBy: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
      };

      const result = await service.createSector(input);

      expect(result).toEqual(mockSector);
      expect(mockExecute).toHaveBeenCalledWith(input);
    });

    it('should throw error when sector code already exists', async () => {
      const input = {
        code: 'HR',
        description: 'Human Resources',
        createdBy: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
        updatedBy: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
      };

      mockExecute.mockRejectedValue(
        new NotFoundException('Sector code already exists'),
      );

      await expect(service.createSector(input)).rejects.toThrow(
        'Sector code already exists',
      );
    });
  });

  describe('getSectorByUuid', () => {
    it('should return a sector when found', async () => {
      mockSectorRepository.findByUuid.mockResolvedValue(mockSector);

      const result = await service.getSectorByUuid(
        'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
      );

      expect(result).toBe(mockSector);
      expect(mockSectorRepository.findByUuid.mock.calls[0][0]).toBe(
        'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
      );
    });

    it('should throw NotFoundException when sector is not found', async () => {
      mockSectorRepository.findByUuid.mockResolvedValue(null);

      await expect(
        service.getSectorByUuid('b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateSector', () => {
    it('should update a sector successfully', async () => {
      const input = {
        code: 'HR',
        description: 'Human Resources',
        updatedBy: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
      };

      const result = await service.updateSector(
        'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
        input,
      );

      expect(result).toBe(mockSector);
      expect(mockUpdateExecute).toHaveBeenCalledWith(
        'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
        input,
      );
    });

    it('should throw NotFoundException when sector is not found', async () => {
      mockUpdateExecute.mockRejectedValue(
        new NotFoundException('Sector not found'),
      );

      const input = {
        code: 'HR',
        description: 'Human Resources',
        updatedBy: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
      };

      await expect(
        service.updateSector('b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456', input),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteSector', () => {
    it('should delete a sector successfully', async () => {
      mockDeleteExecute.mockResolvedValue(undefined);

      await service.deleteSector('b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456');

      expect(mockDeleteExecute).toHaveBeenCalledWith({
        uuid: 'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
      });
    });

    it('should throw NotFoundException when sector is not found', async () => {
      mockDeleteExecute.mockRejectedValue(
        new NotFoundException('Sector not found'),
      );

      await expect(
        service.deleteSector('b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('listSectors', () => {
    it('should list sectors with pagination', async () => {
      const result = await service.listSectors({
        limit: 10,
        offset: 0,
      });
      expect(result.items).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(mockListSectorsUseCase.execute).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
      });
    });

    it('should list sectors with filters', async () => {
      const result = await service.listSectors({
        limit: 10,
        offset: 0,
        code: 'HR',
        description: 'Human',
      });
      expect(result.items).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(mockListSectorsUseCase.execute).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        code: 'HR',
        description: 'Human',
      });
    });
  });
});
