# backoffice-audit-service

## Description

This project is a NestJS application for auditing backoffice operations. It uses RabbitMQ for message queuing and Prisma for database interactions.

## Technologies Used

- NestJS
- RabbitMQ
- Prisma
- PostgreSQL (via Prisma)
- Docker
- Jest (for testing)
- ESLint (for linting)
- Prettier (for code formatting)

## Installation

1.  **Clone the repository:**

    ```bash
    git clone <repository-url>
    cd backoffice-audit-service
    ```

2.  **Install dependencies:**

    ```bash
    npm install
    ```

3.  **Set up environment variables:**

    Copy the `.env.example` file to `.env` and update the values as needed.

    ```bash
    cp .env.example .env
    ```

4.  **Run Docker Compose (for RabbitMQ and PostgreSQL):**

    ```bash
    docker-compose up -d
    ```

5.  **Run Prisma Migrations:**

    ```bash
    npx prisma migrate dev
    ```

## Running the Application

-   **Development Mode (with watch):**

    ```bash
    npm run start:dev
    ```

-   **Production Mode:**

    ```bash
    npm run start:prod
    ```

## Running Tests

-   **Unit Tests:**

    ```bash
    npm run test
    ```

-   **End-to-End Tests:**

    ```bash
    npm run test:e2e
    ```

-   **Coverage Report:**

    ```bash
    npm run test:cov
    ```

## API Documentation (Swagger)

Once the application is running, the API documentation will be available at:

`http://localhost:3000/api` (or your configured port)