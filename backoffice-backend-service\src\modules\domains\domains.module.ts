import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../infrastructure/prisma/prisma.module';
import { UsersModule } from '../users/users.module';
import { DomainController } from './application/controllers/domain.controller';
import { CreateDomainUseCase } from './application/use-cases/create-domain.use-case';
import { FindDomainByUuidUseCase } from './application/use-cases/find-domain-by-uuid.use-case';
import { ListDomainsUseCase } from './application/use-cases/list-domains.use-case';
import { UpdateDomainUseCase } from './application/use-cases/update-domain.use-case';
import { DeleteDomainUseCase } from './application/use-cases/delete-domain.use-case';
import { DomainRepository } from './infrastructure/repositories/domain.repository';

@Module({
  imports: [PrismaModule, UsersModule],
  controllers: [DomainController],
  providers: [
    {
      provide: 'IDomainRepository',
      useClass: DomainRepository,
    },
    CreateDomainUseCase,
    FindDomainByUuidUseCase,
    ListDomainsUseCase,
    UpdateDomainUseCase,
    DeleteDomainUseCase,
  ],
  exports: [
    CreateDomainUseCase,
    FindDomainByUuidUseCase,
    ListDomainsUseCase,
    UpdateDomainUseCase,
    DeleteDomainUseCase,
  ],
})
export class DomainsModule {} 