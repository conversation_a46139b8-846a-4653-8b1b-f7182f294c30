import { TaskRepository } from '../../../ports/repositories/task-repository.interface';

export interface DeleteTaskInput {
  id: string;
}

export class DeleteTaskUseCase {
  constructor(private taskRepository: TaskRepository) {}

  async execute(input: DeleteTaskInput): Promise<void> {
    const { id } = input;

    // Verificar se a tarefa existe
    const task = await this.taskRepository.findById(id);

    if (!task) {
      throw new Error(`Tarefa com ID ${id} não encontrada`);
    }

    // Excluir a tarefa
    await this.taskRepository.delete(id);
  }
}
