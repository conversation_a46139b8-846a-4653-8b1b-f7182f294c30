# Configuração básica do projeto
sonar.projectKey=seu-usuario_backend-boilerplate
sonar.organization=sua-organizacao

# Caminho para as fontes
sonar.sources=src
sonar.tests=test

# Exclusões da análise de cobertura de código
sonar.coverage.exclusions=**/*.spec.ts,**/*.test.ts,**/*.e2e-spec.ts,src/main.ts,src/**/*.module.ts,src/infrastructure/config/**/*,src/infrastructure/telemetry/**/*,test/**/*,**/*.dto.ts,**/*.swagger.ts,**/*.enum.ts

# Exclusões de duplicação
sonar.cpd.exclusions=**/*.spec.ts,**/*.test.ts,**/*.e2e-spec.ts

# Caminho para relatórios de cobertura de teste
sonar.javascript.lcov.reportPaths=coverage/lcov.info 