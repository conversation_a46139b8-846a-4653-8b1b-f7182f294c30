# Fastify Boilerplate

API Server com arquitetura Package by Feature usando Fastify, Drizzle ORM e PostgreSQL.

## 🚀 Quick Start com Docker

### 1. Configure as variáveis de ambiente

```bash
# Copie o arquivo de exemplo
cp env.example .env

# Edite as variáveis conforme necessário
```

### 2. Inicie os serviços

```bash
# Suba todos os serviços (API + PostgreSQL)
docker-compose up -d
```

### 3. Acesse os serviços

- **API**: http://localhost:3000
- **Health Check**: http://localhost:3000/health
- **Swagger Docs**: http://localhost:3000/docs (apenas em desenvolvimento)

## 🛠️ Desenvolvimento Local

### Pré-requisitos

- Node.js 18+
- pnpm
- PostgreSQL (ou use Docker)

### Instalação

```bash
# Instalar dependências
pnpm install

# Configurar ambiente
cp env.example .env

# Executar migrações
pnpm run db:migrate

# Iniciar em modo desenvolvimento
pnpm run dev
```

## 📋 Scripts Disponíveis

```bash
# Desenvolvimento
pnpm run dev          # Inicia servidor com hot reload

# Build
pnpm run build        # Compila o projeto
pnpm run start        # Inicia versão compilada

# Database
pnpm run db:generate  # Gera migrações
pnpm run db:migrate   # Executa migrações
pnpm run db:seed      # Popula banco com dados de teste
pnpm run db:setup     # Executa migrações + seeder
pnpm run db:clear     # Limpa dados de teste
pnpm run db:studio    # Interface visual do banco
```

## 🌱 Database Seeder

O projeto inclui um sistema completo de seeder para popular o banco de dados com dados de teste durante o desenvolvimento.

### 📋 Usuários Criados Automaticamente

**Usuários Básicos:**
- **Admin**: `<EMAIL>` / `admin123`
- **User**: `<EMAIL>` / `user123`
- **Demo**: `<EMAIL>` / `demo123`

**Usuários Adicionais:**
- John Doe, Jane Smith, Mike Admin, Sarah (inativo), Test User

**Usuários de Desenvolvimento** (apenas em NODE_ENV=development):
- **Developer**: `<EMAIL>` / `dev123`
- **Tester**: `<EMAIL>` / `test123`
- **QA**: `<EMAIL>` / `qa123`

### 🚀 Execução Automática

O seeder é executado automaticamente quando você inicia o projeto com Docker:

```bash
docker-compose up -d
```

### 🛠️ Execução Manual

```bash
# Executar setup completo (migrações + seeder)
pnpm run db:setup

# Executar apenas seeder
pnpm run db:seed

# Limpar dados de teste
pnpm run db:clear
```

### 🔧 Funcionalidades

- **Proteção contra duplicação**: Verifica se dados já existem antes de inserir
- **Dados específicos por ambiente**: Usuários de desenvolvimento só são criados em NODE_ENV=development
- **Senhas seguras**: Todas as senhas são hashadas com bcrypt
- **Cleanup automático**: Função para limpar dados de teste quando necessário

### 📚 Documentação Completa

Para mais detalhes sobre como personalizar ou adicionar novos seeders, consulte: `src/shared/database/README.md`

## 🐳 Docker Commands

```bash
# Subir apenas o banco
docker-compose up postgres -d

# Ver logs da API
docker-compose logs -f api

# Parar todos os serviços
docker-compose down

# Limpar volumes (CUIDADO: apaga dados)
docker-compose down -v
```

## 🔧 Variáveis de Ambiente

| Variável | Descrição | Padrão |
|----------|-----------|---------|
| `NODE_ENV` | Ambiente de execução | `development` |
| `PORT` | Porta da API | `3000` |
| `DB_HOST` | Host do PostgreSQL | `localhost` |
| `DB_PORT` | Porta do PostgreSQL | `5432` |
| `DB_NAME` | Nome do banco | `boilerplate` |
| `DB_USER` | Usuário do banco | `postgres` |
| `DB_PASSWORD` | Senha do banco | `postgres123` |
| `JWT_SECRET` | Chave secreta JWT | *(obrigatório)* |
| `BCRYPT_ROUNDS` | Rounds do bcrypt | `10` |
| `CORS_ENABLED` | Habilitar CORS | `true` |
| `DEBUG` | Logs detalhados | `false` |
| `RATE_LIMIT` | Limite de requests/min | `100` |

## 📁 Estrutura do Projeto

```
src/
├── modules/           # Módulos por feature
│   ├── auth/          # Módulo de autenticação
│   └── users/         # Módulo de usuários
│       ├── controllers/
│       ├── docs/
│       ├── entities/
│       ├── repositories/
│       ├── routes/
│       ├── services/
│       ├── types/
│       └── zod/
└── shared/            # Código compartilhado
    ├── config/        # Configurações
    ├── database/      # Conexão do banco
    ├── enums/         # Enumerações
    ├── errors/        # Tratamento de erros
    ├── middleware/    # Middlewares globais
    ├── types/         # Tipos globais
    ├── utils/         # Utilitários
    └── validators/    # Validadores
```

## 🔒 Segurança

- ✅ Sanitização automática de respostas
- ✅ Validação de entrada com Zod
- ✅ Hash de senhas com bcrypt
- ✅ Tratamento global de erros
- ✅ Rate limiting (configurável)
- ✅ CORS configurável
- ✅ Middleware de autenticação JWT

## 🧪 Testando a API

### Via Swagger UI (Recomendado)
Acesse http://localhost:3000/docs para uma interface interativa completa.

### Via cURL

```bash
# Health check
curl http://localhost:3000/health

# Criar usuário
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","username":"test","password":"123456","name":"Test User"}'

# Listar usuários
curl http://localhost:3000/api/users

# Buscar usuário por ID
curl http://localhost:3000/api/users/1

# Atualizar usuário
curl -X PUT http://localhost:3000/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{"name":"Updated Name"}'

# Desativar usuário
curl -X DELETE http://localhost:3000/api/users/1
```

## 📊 Monitoramento

- **Health Check**: `GET /health`
- **Swagger Docs**: `GET /docs` (desenvolvimento)
- **Logs**: Estruturados com Pino
- **Database**: Pool de conexões com monitoramento
- **Errors**: Tratamento centralizado com stack traces

## 🔧 Technology Stack

- **Framework**: Fastify
- **ORM**: Drizzle ORM com PostgreSQL
- **Validation**: Zod
- **Authentication**: JWT com bcryptjs
- **Documentation**: Swagger/OpenAPI
- **Development**: TypeScript

## 📝 Adicionando Novos Módulos

Para adicionar um novo módulo seguindo o padrão Package by Feature:

1. **Criar estrutura**:
```bash
mkdir -p src/modules/novo-modulo/{controllers,docs,entities,repositories,routes,services,types,zod}
```

2. **Seguir o padrão existente** do módulo `users` como referência

3. **Registrar as rotas** no arquivo principal

## 🤝 Contributing

1. Fork o projeto
2. Crie uma branch (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 License

This project is licensed under the MIT License. 