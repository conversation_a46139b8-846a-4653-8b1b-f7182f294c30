import bcrypt from 'bcryptjs';
import * as userRepository from '../../users/repositories/user.repository';
import { object } from '../../../shared/utils/data-manipulation';
import jwt, { SignOptions } from 'jsonwebtoken';
import { env } from '../../../shared/config/env';
import { InvalidCredentialsException } from '@/shared/errors/custom-exceptions';

export const login = async (loginData: {
  email?: string;
  username?: string;
  password: string;
}) => {
  const { email, username, password } = loginData;

  const user = await userRepository.getByEmailOrUsername(email, username);

  if (!user) {
    // Throwing same error for both email and username to avoid information leakage
    throw new InvalidCredentialsException('Invalid email/username or password');
  }

  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    throw new InvalidCredentialsException('Invalid email/username or password');
  }

  const token = jwt.sign(
    {
      userId: user.id,
      email: user.email,
      username: user.username,
      role: user.role,
    },
    env.JWT_SECRET as string,
    { expiresIn: env.JWT_EXPIRES_IN as SignOptions['expiresIn'] }
  );

  const refreshToken = jwt.sign(
    {
      userId: user.id,
      email: user.email,
      username: user.username,
      role: user.role,
    },
    env.JWT_SECRET as string,
    { expiresIn: env.JWT_REFRESH_EXPIRES_IN as SignOptions['expiresIn'] }
  );

  const userWithoutSensitiveData = object(user).removeKeys('password');

  return { user: userWithoutSensitiveData, token, refreshToken };
};

export const refreshToken = async (refreshTokenData: {
  tokenCookie?: string;
  refreshTokenCookie?: string;
  refreshToken: string;
}) => {
  const { tokenCookie, refreshTokenCookie, refreshToken } = refreshTokenData;

  if (!tokenCookie || !refreshTokenCookie || !refreshToken) {
    throw new InvalidCredentialsException('Invalid token or refresh token');
  }

  if (refreshToken !== refreshTokenCookie) {
    throw new InvalidCredentialsException('Invalid token or refresh token');
  }

  const decoded = jwt.verify(tokenCookie, env.JWT_SECRET as string) as
    | jwt.JwtPayload
    | jwt.JsonWebTokenError;

  if (decoded instanceof jwt.JsonWebTokenError) {
    throw new InvalidCredentialsException('Invalid token');
  }

  const newToken = jwt.sign(decoded, env.JWT_SECRET as string, {
    expiresIn: env.JWT_EXPIRES_IN,
  });
  const newRefreshToken = jwt.sign(decoded, env.JWT_SECRET as string, {
    expiresIn: env.JWT_REFRESH_EXPIRES_IN,
  });

  return { token: newToken, refreshToken: newRefreshToken };
};

export const logout = async (logoutData: {
  token: string;
  refreshToken: string;
  tokenCookie?: string;
  refreshTokenCookie?: string;
}) => {
  const { token, refreshToken, tokenCookie, refreshTokenCookie } = logoutData;

  if (!token || !refreshToken || !tokenCookie || !refreshTokenCookie) {
    throw new InvalidCredentialsException('Invalid token or refresh token');
  }

  if (refreshToken !== refreshTokenCookie) {
    throw new InvalidCredentialsException('Invalid token or refresh token');
  }

  if (token !== tokenCookie) {
    throw new InvalidCredentialsException('Invalid token or refresh token');
  }

  const requestDecoded = jwt.verify(token, env.JWT_SECRET as string) as
    | jwt.JwtPayload
    | jwt.JsonWebTokenError;

  if (requestDecoded instanceof jwt.JsonWebTokenError) {
    throw new InvalidCredentialsException('Invalid token or refresh token');
  }

  const cookieDecoded = jwt.verify(tokenCookie, env.JWT_SECRET as string) as
    | jwt.JwtPayload
    | jwt.JsonWebTokenError;

  if (cookieDecoded instanceof jwt.JsonWebTokenError) {
    throw new InvalidCredentialsException('Invalid token or refresh token');
  }

  if (
    requestDecoded.userId !== cookieDecoded.userId ||
    requestDecoded.email !== cookieDecoded.email ||
    requestDecoded.username !== cookieDecoded.username ||
    requestDecoded.role !== cookieDecoded.role
  ) {
    throw new InvalidCredentialsException('Invalid token or refresh token');
  }

  // Invalidate the tokens by not returning them
  return { success: true, message: 'Logout successful' };
};
