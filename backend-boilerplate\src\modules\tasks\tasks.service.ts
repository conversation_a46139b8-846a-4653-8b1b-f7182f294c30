import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { TaskRepository } from '../../core/ports/repositories/task-repository.interface';
import { Task } from '../../core/domain/task.entity';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskService as ApplicationTaskService } from '../../core/application/services/task.service';

@Injectable()
export class TasksService {
  private applicationService: ApplicationTaskService;

  constructor(
    @Inject('TaskRepository')
    private taskRepository: TaskRepository,
  ) {
    this.applicationService = new ApplicationTaskService(taskRepository);
  }

  async findAll(): Promise<Task[]> {
    return this.applicationService.findAll();
  }

  async findById(id: string): Promise<Task> {
    const task = await this.applicationService.findById(id);

    if (!task) {
      throw new NotFoundException(`Tarefa com ID ${id} não encontrada`);
    }

    return task;
  }

  async findByUserId(userId: string): Promise<Task[]> {
    return this.applicationService.findByUserId(userId);
  }

  async create(userId: string, createTaskDto: CreateTaskDto): Promise<Task> {
    return this.applicationService.create(
      userId,
      createTaskDto.title,
      createTaskDto.description,
    );
  }

  async update(id: string, updateTaskDto: UpdateTaskDto): Promise<Task> {
    try {
      return await this.applicationService.update(
        id,
        updateTaskDto.title,
        updateTaskDto.description,
        updateTaskDto.status,
      );
    } catch (error) {
      if (error instanceof Error && error.message.includes('não encontrada')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await this.applicationService.delete(id);
    } catch (error) {
      if (error instanceof Error && error.message.includes('não encontrada')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }
}
