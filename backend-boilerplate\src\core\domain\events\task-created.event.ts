import { DomainEvent } from './domain-event';
import { Task } from '../task.entity';

export class TaskCreatedEvent extends DomainEvent {
  private readonly task: Task;

  constructor(
    task: Task,
    metadata?: {
      correlationId?: string;
      userId?: string;
    },
  ) {
    super('task.created', {
      correlationId: metadata?.correlationId,
      userId: metadata?.userId || task.userId,
    });

    this.task = task;
  }

  getData(): object {
    return {
      taskId: this.task.id,
      userId: this.task.userId,
      title: this.task.title,
      description: this.task.description,
      status: this.task.status,
      createdAt: this.task.createdAt.toISOString(),
    };
  }
}
