{"timestamp":"2025-07-28 20:42:04.441","level":"error","message":"Database health check failed","service":"fastify-api","environment":"development","error":"query.getSQL is not a function","responseTime":1,"trace_id":"6e0362f813716990f4ac7d13c015824f","span_id":"a76edd9ab54f10b9","trace_flags":"01"}
{"timestamp":"2025-07-28 20:42:04.772","level":"error","message":"Redis health check failed","service":"fastify-api","environment":"development","error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.","responseTime":329,"trace_id":"6e0362f813716990f4ac7d13c015824f","span_id":"a76edd9ab54f10b9","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:26.163","level":"error","message":"Database health check failed","service":"fastify-api","environment":"development","error":"query.getSQL is not a function","responseTime":0,"trace_id":"79ee52f2c14e1219fa7e30856ef974ab","span_id":"b499b820b4334912","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:28.341","level":"error","message":"Database health check failed","service":"fastify-api","environment":"development","error":"query.getSQL is not a function","responseTime":0,"trace_id":"b5e0d40deaa151dc3cd3c6c759b4491f","span_id":"0264de55b1216d91","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:31.599","level":"error","message":"Redis health check failed","service":"fastify-api","environment":"development","error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.","responseTime":5436,"trace_id":"79ee52f2c14e1219fa7e30856ef974ab","span_id":"b499b820b4334912","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:31.600","level":"error","message":"Redis health check failed","service":"fastify-api","environment":"development","error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.","responseTime":3259,"trace_id":"b5e0d40deaa151dc3cd3c6c759b4491f","span_id":"0264de55b1216d91","trace_flags":"01"}
{"timestamp":"2025-07-28 20:45:31.474","level":"error","message":"Database health check failed","service":"fastify-api","environment":"development","error":"query.getSQL is not a function","responseTime":0,"trace_id":"20bcb7850f5defcb720ef0bb620c223e","span_id":"286e8622ca163eb1","trace_flags":"01"}
{"timestamp":"2025-07-28 20:45:31.714","level":"error","message":"Redis health check failed","service":"fastify-api","environment":"development","error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.","responseTime":239,"trace_id":"20bcb7850f5defcb720ef0bb620c223e","span_id":"286e8622ca163eb1","trace_flags":"01"}
