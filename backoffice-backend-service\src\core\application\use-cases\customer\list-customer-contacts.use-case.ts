import { CustomerContact } from "@/core/domain/customer/entities/customer-contact.entity";
import { CustomerContactRepositoryPort } from "@/core/ports/repositories/customer-contact-repository.port";
import { Injectable, Inject, NotFoundException } from '@nestjs/common';

@Injectable()
export class ListCustomerContactsUseCase {
    constructor(
        @Inject('CustomerContactRepository')
        private readonly customerContactRepository: CustomerContactRepositoryPort,
    ) {}

    async execute(customerId: number): Promise<CustomerContact[]> {
        const contacts = await this.customerContactRepository.findByCustomerId(customerId);

        if (!contacts || contacts.length === 0) {
            throw new NotFoundException(`No contacts found for customer with ID ${customerId}`);
        }

        return contacts;
    }
}