import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Role } from '../../../core/domain/role.enum';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { Request } from 'express';

// Interface que estende Request para incluir o user
interface RequestWithUser extends Request {
  user: {
    id?: string;
    email?: string;
    role?: string;
  };
}

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const request = context.switchToHttp().getRequest<RequestWithUser>();

    if (!request.user || !request.user.role) {
      return false;
    }

    return requiredRoles.some((role) => request.user.role === role);
  }
}
