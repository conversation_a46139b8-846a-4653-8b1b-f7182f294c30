# Guia de Autenticação e Autorização

## Visão Geral

A API possui um sistema de autenticação baseado em JWT (JSON Web Token), porém **atualmente os endpoints estão configurados sem a obrigatoriedade de autenticação para permitir testes mais fáceis durante o desenvolvimento**.

## Como usar a autenticação

### Autenticação
1. Para obter um token JWT, use o endpoint `/api/v1/auth/login` com seu email e senha.
2. Este token deve ser incluído no cabeçalho das requisições como: `Authorization: Bearer SEU_TOKEN_JWT`

### Validação de token
- Para validar se seu token JWT é válido e obter os dados do usuário autenticado, use o endpoint `/api/v1/auth/validate`
- Este é o único endpoint que exige autenticação por padrão

## Implementação da autenticação nos controladores

Ao finalizar os testes e implementar a autenticação obrigatória, siga estas orientações:

### Endpoints que exigem autenticação
Adicione o decorator `@UseGuards(JwtAuthGuard)` e `@ApiBearerAuth()` nos métodos:

```typescript
@Get('meu-endpoint')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
async meuMetodo() {
  // seu código
}
```

### Endpoints que exigem autenticação e permissões específicas
Adicione os decorators `@UseGuards(JwtAuthGuard, RolesGuard)`, `@Roles(Role.ADMIN)` e `@ApiBearerAuth()`:

```typescript
@Get('endpoint-admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN)
@ApiBearerAuth()
async metodoAdmin() {
  // seu código
}
```

## Recomendações de Segurança

1. **Ambiente de Produção**: Todos os endpoints devem exigir autenticação em produção, exceto login e registro
2. **Validação de Dados**: Sempre valide os dados de entrada mesmo sem autenticação
3. **Proteção de Rotas**: Implemente a proteção de rotas no frontend também
4. **Evite Hardcoding**: Não use IDs fixos como `teste-user-id` em produção
5. **Testes de Segurança**: Realize testes de segurança antes de lançar para produção

## Locais que exigem atenção

Os seguintes controllers atualmente permitem acesso sem autenticação, mas devem ser protegidos em produção:

1. **UsersController** - Todos os endpoints, especialmente acesso/modificação de dados de usuários
2. **TasksController** - Todos os endpoints, especialmente criação/modificação de tarefas

---

Para mais informações sobre a implementação da autenticação, consulte os seguintes arquivos:
- `src/modules/auth/guards/jwt-auth.guard.ts` - Guarda de autenticação JWT
- `src/modules/auth/guards/roles.guard.ts` - Guarda de autorização baseada em roles
- `src/modules/auth/auth.service.ts` - Serviço de autenticação
- `src/modules/auth/auth.controller.ts` - Controlador com endpoints de autenticação 