import { Test, TestingModule } from '@nestjs/testing';
import { PrismaAuditRepository } from './prisma-audit.repository';
import { PrismaService } from '../../prisma/prisma.service';
import { AuditMessage } from '../domain/audit-message';

const mockPrismaService = {
  audit: {
    create: jest.fn(),
    count: jest.fn(),
    findMany: jest.fn(),
  },
};

describe('PrismaAuditRepository', () => {
  let repository: PrismaAuditRepository;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PrismaAuditRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<PrismaAuditRepository>(PrismaAuditRepository);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('create', () => {
    it('should create an audit record with correct data', async () => {
      // Arrange
      const auditMessage = new AuditMessage(
        'USER_LOGIN',
        '2025-06-26T12:00:00.000Z',
        'testuser',
        'POST',
        '/api/login',
        { email: '<EMAIL>', password: 'hidden' },
        '***********',
        'Mozilla/5.0 Test Agent',
      );

      const expectedPrismaData = {
        eventType: 'USER_LOGIN',
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'testuser',
        method: 'POST',
        endpoint: '/api/login',
        body: { email: '<EMAIL>', password: 'hidden' },
        ip: '***********',
        userAgent: 'Mozilla/5.0 Test Agent',
      };

      mockPrismaService.audit.create.mockResolvedValue({
        id: 1,
        ...expectedPrismaData,
      });

      // Act
      await repository.create(auditMessage);

      // Assert
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(prismaService.audit.create).toHaveBeenCalledWith({
        data: expectedPrismaData,
      });
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(prismaService.audit.create).toHaveBeenCalledTimes(1);
    });

    it('should handle complex body objects', async () => {
      // Arrange
      const complexBody = {
        user: {
          id: 123,
          name: 'Test User',
          metadata: {
            roles: ['admin', 'user'],
            permissions: {
              read: true,
              write: false,
            },
          },
        },
        request: {
          headers: {
            'user-agent': 'Mozilla/5.0',
            accept: 'application/json',
          },
        },
      };

      const auditMessage = new AuditMessage(
        'USER_UPDATE',
        '2025-06-26T12:00:00.000Z',
        'adminuser',
        'PUT',
        '/api/users/123',
        complexBody,
        '********',
        'Mozilla/5.0 Complex Agent',
      );

      mockPrismaService.audit.create.mockResolvedValue({
        id: 2,
        eventType: 'USER_UPDATE',
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'adminuser',
        method: 'PUT',
        endpoint: '/api/users/123',
        body: complexBody,
        ip: '********',
        userAgent: 'Mozilla/5.0 Complex Agent',
      });

      // Act
      await repository.create(auditMessage);

      // Assert
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(prismaService.audit.create).toHaveBeenCalledWith({
        data: {
          eventType: 'USER_UPDATE',
          timestamp: '2025-06-26T12:00:00.000Z',
          username: 'adminuser',
          method: 'PUT',
          endpoint: '/api/users/123',
          body: complexBody,
          ip: '********',
          userAgent: 'Mozilla/5.0 Complex Agent',
        },
      });
    });

    it('should propagate database errors', async () => {
      // Arrange
      const auditMessage = new AuditMessage(
        'USER_LOGIN',
        '2025-06-26T12:00:00.000Z',
        'testuser',
        'POST',
        '/api/login',
        { email: '<EMAIL>' },
        '***********',
        'Mozilla/5.0 Test Agent',
      );

      const errorMessage = 'Database constraint violation';
      mockPrismaService.audit.create.mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(repository.create(auditMessage)).rejects.toThrow(
        errorMessage,
      );
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(prismaService.audit.create).toHaveBeenCalledTimes(1);
    });

    it('should handle empty body objects', async () => {
      // Arrange
      const auditMessage = new AuditMessage(
        'HEALTH_CHECK',
        '2025-06-26T12:00:00.000Z',
        'system',
        'GET',
        '/health',
        {}, // Empty body
        '127.0.0.1',
        'Health Monitor/1.0',
      );

      mockPrismaService.audit.create.mockResolvedValue({
        id: 3,
        eventType: 'HEALTH_CHECK',
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'system',
        method: 'GET',
        endpoint: '/health',
        body: {},
        ip: '127.0.0.1',
        userAgent: 'Health Monitor/1.0',
      });

      // Act
      await repository.create(auditMessage);

      // Assert
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(prismaService.audit.create).toHaveBeenCalledWith({
        data: {
          eventType: 'HEALTH_CHECK',
          timestamp: '2025-06-26T12:00:00.000Z',
          username: 'system',
          method: 'GET',
          endpoint: '/health',
          body: {},
          ip: '127.0.0.1',
          userAgent: 'Health Monitor/1.0',
        },
      });
    });
  });

  describe('findAll', () => {
    const mockAuditRecord = {
      id: 1,
      eventType: 'USER_LOGIN',
      timestamp: new Date('2025-06-29T10:00:00.000Z'),
      username: 'testuser',
      method: 'POST',
      endpoint: '/api/login',
      body: { email: '<EMAIL>' },
      ip: '***********',
      userAgent: 'Mozilla/5.0',
    };

    it('should return paginated results with correct data', async () => {
      // Arrange
      const params = {
        username: 'testuser',
        timestamp: '2025-06-29',
        page: 1,
        limit: 10,
        offset: 0,
      };

      mockPrismaService.audit.count.mockResolvedValue(25);
      mockPrismaService.audit.findMany.mockResolvedValue([mockAuditRecord]);

      // Act
      const result = await repository.findAll(params);

      // Assert
      expect(result).toEqual({
        data: expect.arrayContaining([
          expect.any(AuditMessage),
        ]) as AuditMessage[],
        total: 25,
        page: 1,
        limit: 10,
        totalPages: 3,
      });
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toBeInstanceOf(AuditMessage);
      expect(result.data[0].eventType).toBe('USER_LOGIN');
      expect(result.data[0].username).toBe('testuser');
    });

    it('should filter by username when provided', async () => {
      // Arrange
      const params = {
        username: 'testuser',
        page: 1,
        limit: 10,
        offset: 0,
      };

      mockPrismaService.audit.count.mockResolvedValue(5);
      mockPrismaService.audit.findMany.mockResolvedValue([mockAuditRecord]);

      // Act
      await repository.findAll(params);

      // Assert
      expect(mockPrismaService.audit.count).toHaveBeenCalledWith({
        where: {
          username: {
            contains: 'testuser',
            mode: 'insensitive',
          },
        },
      });
      expect(mockPrismaService.audit.findMany).toHaveBeenCalledWith({
        where: {
          username: {
            contains: 'testuser',
            mode: 'insensitive',
          },
        },
        skip: 0,
        take: 10,
        orderBy: {
          timestamp: 'desc',
        },
      });
    });

    it('should filter by timestamp when provided', async () => {
      // Arrange
      const params = {
        timestamp: '2025-06-29',
        page: 1,
        limit: 10,
        offset: 0,
      };

      mockPrismaService.audit.count.mockResolvedValue(3);
      mockPrismaService.audit.findMany.mockResolvedValue([mockAuditRecord]);

      // Act
      await repository.findAll(params);

      // Assert
      const testDate = new Date('2025-06-29');
      const expectedStartOfDay = new Date(
        testDate.getFullYear(),
        testDate.getMonth(),
        testDate.getDate(),
      );
      const expectedEndOfDay = new Date(
        testDate.getFullYear(),
        testDate.getMonth(),
        testDate.getDate() + 1,
      );

      const expectedWhere = {
        where: {
          timestamp: {
            gte: expectedStartOfDay,
            lt: expectedEndOfDay,
          },
        },
      };

      expect(mockPrismaService.audit.count).toHaveBeenCalledWith(expectedWhere);
      expect(mockPrismaService.audit.findMany).toHaveBeenCalledWith({
        ...expectedWhere,
        skip: 0,
        take: 10,
        orderBy: {
          timestamp: 'desc',
        },
      });
    });

    it('should filter by both username and timestamp', async () => {
      // Arrange
      const params = {
        username: 'admin',
        timestamp: '2025-06-29',
        page: 2,
        limit: 5,
        offset: 5,
      };

      mockPrismaService.audit.count.mockResolvedValue(12);
      mockPrismaService.audit.findMany.mockResolvedValue([mockAuditRecord]);

      // Act
      await repository.findAll(params);

      // Assert
      const testDate = new Date('2025-06-29');
      const expectedStartOfDay = new Date(
        testDate.getFullYear(),
        testDate.getMonth(),
        testDate.getDate(),
      );
      const expectedEndOfDay = new Date(
        testDate.getFullYear(),
        testDate.getMonth(),
        testDate.getDate() + 1,
      );

      const expectedWhere = {
        where: {
          username: {
            contains: 'admin',
            mode: 'insensitive',
          },
          timestamp: {
            gte: expectedStartOfDay,
            lt: expectedEndOfDay,
          },
        },
      };

      expect(mockPrismaService.audit.count).toHaveBeenCalledWith(expectedWhere);
      expect(mockPrismaService.audit.findMany).toHaveBeenCalledWith({
        ...expectedWhere,
        skip: 5,
        take: 5,
        orderBy: {
          timestamp: 'desc',
        },
      });
    });

    it('should return empty results when no records found', async () => {
      // Arrange
      const params = {
        username: 'nonexistent',
        page: 1,
        limit: 10,
        offset: 0,
      };

      mockPrismaService.audit.count.mockResolvedValue(0);
      mockPrismaService.audit.findMany.mockResolvedValue([]);

      // Act
      const result = await repository.findAll(params);

      // Assert
      expect(result).toEqual({
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      });
    });

    it('should handle pagination correctly', async () => {
      // Arrange
      const params = {
        page: 3,
        limit: 15,
        offset: 30,
      };

      mockPrismaService.audit.count.mockResolvedValue(50);
      mockPrismaService.audit.findMany.mockResolvedValue([mockAuditRecord]);

      // Act
      const result = await repository.findAll(params);

      // Assert
      expect(result.totalPages).toBe(4); // Math.ceil(50 / 15)
      expect(mockPrismaService.audit.findMany).toHaveBeenCalledWith({
        where: {},
        skip: 30,
        take: 15,
        orderBy: {
          timestamp: 'desc',
        },
      });
    });

    it('should handle null ip and userAgent values', async () => {
      // Arrange
      const recordWithNulls = {
        ...mockAuditRecord,
        ip: null,
        userAgent: null,
      };

      const params = {
        page: 1,
        limit: 10,
        offset: 0,
      };

      mockPrismaService.audit.count.mockResolvedValue(1);
      mockPrismaService.audit.findMany.mockResolvedValue([recordWithNulls]);

      // Act
      const result = await repository.findAll(params);

      // Assert - null values should be converted to 'unknown' strings in the mapping
      expect(result.data).toHaveLength(1);
      expect(result.data[0].ip).toBe('unknown');
      expect(result.data[0].userAgent).toBe('unknown');
    });

    it('should propagate database errors from count operation', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        offset: 0,
      };

      mockPrismaService.audit.count.mockRejectedValue(
        new Error('Database error'),
      );

      // Act & Assert
      await expect(repository.findAll(params)).rejects.toThrow(
        'Database error',
      );
    });

    it('should propagate database errors from findMany operation', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        offset: 0,
      };

      mockPrismaService.audit.count.mockResolvedValue(5);
      mockPrismaService.audit.findMany.mockRejectedValue(
        new Error('Query failed'),
      );

      // Act & Assert
      await expect(repository.findAll(params)).rejects.toThrow('Query failed');
    });
  });
});
