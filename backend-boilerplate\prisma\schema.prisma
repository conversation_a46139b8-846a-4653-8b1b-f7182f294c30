// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modelo de Usuário
model User {
  id        String    @id @default(uuid())
  email     String    @unique
  name      String?
  password  String
  role      Role      @default(USER)
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  tasks     Task[]
  
  @@map("users")
}

// Enum para os papéis de usuário
enum Role {
  ADMIN
  USER
}

// Modelo de Tarefa
model Task {
  id          String     @id @default(uuid())
  title       String
  description String?
  status      TaskStatus @default(PENDING)
  userId      String     @map("user_id")
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("tasks")
}

// Enum para o status das tarefas
enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELED
}

// Modelo para Produtos (exemplo para DDD)
model Product {
  id          String    @id @default(uuid())
  name        String
  description String?
  price       Decimal   @db.Decimal(10, 2)
  sku         String    @unique
  stock       Int       @default(0)
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  categoryId  String?   @map("category_id")
  category    Category? @relation(fields: [categoryId], references: [id])

  @@map("products")
}

// Modelo para Categorias
model Category {
  id        String    @id @default(uuid())
  name      String
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  products  Product[]

  @@map("categories")
}
