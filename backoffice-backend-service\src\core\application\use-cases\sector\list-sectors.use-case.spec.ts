import { Test, TestingModule } from '@nestjs/testing';
import { ListSectorsUseCase } from './list-sectors.use-case';
import { SECTOR_REPOSITORY } from '@/core/constants/injection-tokens';
import { SectorRepositoryPort } from '@/core/ports/repositories/sector-repository.port';
import { Sector } from '@/core/domain/entities/sector.entity';

describe('ListSectorsUseCase', () => {
  let useCase: ListSectorsUseCase;
  let mockSectorRepository: jest.Mocked<SectorRepositoryPort>;

  const mockSector = Sector.create(
    1,
    'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
    'HR',
    'Human Resources',
    'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
    'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    mockSectorRepository = {
      findByCode: jest.fn(),
      findByUuid: jest.fn(),
      findById: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      list: jest.fn().mockResolvedValue({
        items: [mockSector],
        total: 1,
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ListSectorsUseCase,
        {
          provide: SECTOR_REPOSITORY,
          useValue: mockSectorRepository,
        },
      ],
    }).compile();

    useCase = module.get<ListSectorsUseCase>(ListSectorsUseCase);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should list sectors with pagination', async () => {
    const result = await useCase.execute({ limit: 10, offset: 0 });
    expect(result.items).toHaveLength(1);
    expect(result.total).toBe(1);
    const listSpy = jest.spyOn(mockSectorRepository, 'list');
    expect(listSpy).toHaveBeenCalledWith({
      limit: 10,
      offset: 0,
    });
  });

  it('should list sectors with filters', async () => {
    const result = await useCase.execute({
      limit: 10,
      offset: 0,
      code: 'HR',
      description: 'Human',
    });
    expect(result.items).toHaveLength(1);
    expect(result.total).toBe(1);
    const listSpy = jest.spyOn(mockSectorRepository, 'list');
    expect(listSpy).toHaveBeenCalledWith({
      limit: 10,
      offset: 0,
      code: 'HR',
      description: 'Human',
    });
  });
});
