import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  UnauthorizedException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Response } from 'express';

interface ExceptionResponse {
  message: string | string[];
  details?: string[];
}

interface ResponseBody {
  code: string;
  message: string;
  details?: string[];
}

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    console.error('HTTP Exception Filter:', exception);
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse() as ExceptionResponse;

    let code = HttpStatus[status] as keyof typeof HttpStatus;
    let message = 'Requisição inválida';
    let details: string[] = [];

    // Se o exception.response já tem code/message, repassa eles
    if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
      if ('code' in exceptionResponse && typeof exceptionResponse.code === 'string') {
        code = exceptionResponse.code as any;
      }
      if ('message' in exceptionResponse && typeof exceptionResponse.message === 'string') {
        message = exceptionResponse.message;
      }
    }

    // Tratamento específico para cada tipo de erro
    if (exception instanceof UnauthorizedException) {
      code = 'UNAUTHORIZED';
      message = 'Não autorizado';
    } else if (exception instanceof InternalServerErrorException) {
      code = 'INTERNAL_SERVER_ERROR';
      message = 'Erro interno do servidor';
    } else if (status === (HttpStatus.BAD_REQUEST as number)) {
      code = 'BAD_REQUEST';
      message = 'Requisição inválida';
      if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        if ('message' in exceptionResponse) {
          if (Array.isArray(exceptionResponse.message)) {
            details = exceptionResponse.message.map((msg) => String(msg));
          } else if (typeof exceptionResponse.message === 'string') {
            if (exceptionResponse.message.includes('email')) {
              details = [exceptionResponse.message];
            }
          }
        }
      }
    }

    const responseBody: ResponseBody = {
      code,
      message,
    };

    // Adiciona details apenas se existirem
    if (details.length > 0) {
      responseBody.details = details;
    }

    response.status(status).json(responseBody);
  }
}
