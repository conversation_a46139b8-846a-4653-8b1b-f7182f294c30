import * as snsService from '../sns.service';
import type {
  SnsNotification,
  SnsSubscriptionConfirmation,
} from '../types/sns.types';

/**
 * Exemplo de uso básico do serviço SNS
 */
export async function basicSnsUsage() {
  try {
    // 1. Criar um tópico
    const topicResult = await snsService.createTopic('user-notifications', {
      DisplayName: 'User Notifications Topic',
    });
    console.log('Topic created:', topicResult.TopicArn);

    // 2. Subscrever um email ao tópico
    const subscriptionResult = await snsService.subscribe(
      'user-notifications',
      'email',
      '<EMAIL>'
    );
    console.log('Subscription created:', subscriptionResult.SubscriptionArn);

    // 3. Publicar uma mensagem simples
    const publishResult = await snsService.publishMessage(
      'user-notifications',
      'Welcome to our service!',
      {
        Subject: 'Welcome Message',
      }
    );
    console.log('Message published:', publishResult.MessageId);

    // 4. Publicar uma mensagem JSON
    await snsService.publishJsonMessage(
      'user-notifications',
      {
        userId: 123,
        action: 'user_created',
        timestamp: new Date().toISOString(),
        metadata: {
          source: 'user-service',
          version: '1.0.0',
        },
      },
      {
        Subject: 'User Created Event',
      }
    );

    // 5. Publicar em lote
    await snsService.publishBatch('user-notifications', [
      {
        id: '1',
        message: 'First batch message',
        Subject: 'Batch Message 1',
      },
      {
        id: '2',
        message: 'Second batch message',
        Subject: 'Batch Message 2',
      },
    ]);

    // 6. Listar tópicos
    const topics = await snsService.listTopics();
    console.log('Available topics:', topics.Topics);

    // 7. Listar subscrições do tópico
    const subscriptions =
      await snsService.listSubscriptions('user-notifications');
    console.log('Topic subscriptions:', subscriptions.Subscriptions);
  } catch (error) {
    console.error('SNS operation failed:', error);
  }
}

/**
 * Exemplo de uso com notificações de usuário
 */
export async function userNotificationExample() {
  try {
    // Notificar sobre criação de usuário
    await snsService.publishJsonMessage(
      'user-events',
      {
        eventType: 'USER_CREATED',
        userId: 123,
        userEmail: '<EMAIL>',
        timestamp: new Date().toISOString(),
      },
      {
        Subject: 'New User Created',
        MessageAttributes: {
          eventType: {
            DataType: 'String',
            StringValue: 'USER_CREATED',
          },
          priority: {
            DataType: 'String',
            StringValue: 'HIGH',
          },
        },
      }
    );

    // Notificar sobre atualização de perfil
    await snsService.publishJsonMessage(
      'user-events',
      {
        eventType: 'USER_PROFILE_UPDATED',
        userId: 123,
        updatedFields: ['name', 'email'],
        timestamp: new Date().toISOString(),
      },
      {
        Subject: 'User Profile Updated',
      }
    );
  } catch (error) {
    console.error('User notification failed:', error);
  }
}

/**
 * Função para processar notificações SNS recebidas
 */
export function processSnsNotification(notification: SnsNotification) {
  console.log('Processing SNS notification:');
  console.log('- Message ID:', notification.MessageId);
  console.log('- Topic ARN:', notification.TopicArn);
  console.log('- Subject:', notification.Subject);
  console.log('- Message:', notification.Message);

  try {
    // Tentar fazer parse da mensagem como JSON
    const messageData = JSON.parse(notification.Message);
    console.log('- Parsed message data:', messageData);

    // Processar baseado no tipo de evento
    if (messageData.eventType) {
      switch (messageData.eventType) {
        case 'USER_CREATED':
          console.log('Processing user creation event');
          // Lógica para processar criação de usuário
          break;
        case 'USER_PROFILE_UPDATED':
          console.log('Processing user profile update event');
          // Lógica para processar atualização de perfil
          break;
        default:
          console.log('Unknown event type:', messageData.eventType);
      }
    }
  } catch (error) {
    console.log('- Message is not JSON, processing as plain text');
  }
}

/**
 * Função para confirmar subscrição SNS
 */
export async function confirmSnsSubscription(
  confirmation: SnsSubscriptionConfirmation
) {
  console.log('SNS Subscription confirmation received:');
  console.log('- Token:', confirmation.Token);
  console.log('- Topic ARN:', confirmation.TopicArn);
  console.log('- Subscribe URL:', confirmation.SubscribeURL);

  // Em um cenário real, você faria uma requisição HTTP para o SubscribeURL
  // para confirmar a subscrição automaticamente
  console.log('To confirm subscription, visit:', confirmation.SubscribeURL);
}
