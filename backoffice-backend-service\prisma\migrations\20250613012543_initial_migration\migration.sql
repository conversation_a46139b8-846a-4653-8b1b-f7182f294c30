-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "core";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "events";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "finance";

-- CreateEnum
CREATE TYPE "core"."Role" AS ENUM ('ADMIN', 'USER', 'FINANCE_ADMIN', 'FINANCE_USER', 'DOCUMENT_ARCHIVER', 'DOCUMENT_UPLOADER', 'DOCUMENT_VIEWER', 'DOCUMENT_DOWNLOADER', 'SUPPLIER_VIEWER');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "core"."SupplierStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- <PERSON>reate<PERSON>num
CREATE TYPE "core"."SupplierClassification" AS ENUM ('CORE', 'GENERAL');

-- CreateEnum
CREATE TYPE "core"."SupplierType" AS ENUM ('BANK', 'GAME', 'SPORTSBOOK', 'KYC', 'OTHER');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "core"."CompanyStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- CreateEnum
CREATE TYPE "core"."EmployeeStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- CreateEnum
CREATE TYPE "core"."CustomerType" AS ENUM ('CASSINO', 'POKER', 'EGAMES', 'SPORT_BETTING');

-- CreateEnum
CREATE TYPE "core"."CustomerStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- CreateEnum
CREATE TYPE "core"."ContractType" AS ENUM ('CERT_GAME', 'CERT_RNG', 'CERT_RGS', 'CERT_PLATFORM', 'CERT_INTEGRATION', 'CERT_KYC', 'CERT_PAYMENT', 'CERT_SPORTSBOOK');

-- CreateEnum
CREATE TYPE "core"."ContractStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "core"."EntityType" AS ENUM ('CLIENT', 'COLLABORATE', 'SUPPLIER');

-- CreateEnum
CREATE TYPE "core"."DocumentStatus" AS ENUM ('ACTIVE', 'ARCHIVED');

-- CreateTable
CREATE TABLE "core"."users" (
    "id" TEXT NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "password" VARCHAR(255) NOT NULL,
    "role" "core"."Role" NOT NULL DEFAULT 'USER',
    "keycloak_id" VARCHAR(50),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),
    "created_by" VARCHAR(50) NOT NULL,
    "updated_by" VARCHAR(50) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."suppliers" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "document" CHAR(14) NOT NULL,
    "trade_name" VARCHAR(100),
    "address" JSONB NOT NULL,
    "contact" JSONB NOT NULL,
    "classification" "core"."SupplierClassification" NOT NULL DEFAULT 'CORE',
    "type" "core"."SupplierType" NOT NULL DEFAULT 'BANK',
    "status" "core"."SupplierStatus" NOT NULL DEFAULT 'ACTIVE',
    "user_id" VARCHAR(50),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" VARCHAR(50) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by" VARCHAR(50) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "suppliers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."companies" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "cnpj" TEXT NOT NULL,
    "address" JSONB NOT NULL,
    "phone" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "status" "core"."CompanyStatus" NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "companies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "finance"."payment_methods" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "label" VARCHAR(100) NOT NULL,
    "description" VARCHAR(255),
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "payment_methods_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "finance"."sectors" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "code" VARCHAR(50) NOT NULL,
    "description" VARCHAR(255) NOT NULL,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "sectors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "events"."domain_events" (
    "id" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "eventName" TEXT NOT NULL,
    "occurredAt" TIMESTAMP(3) NOT NULL,
    "correlationId" TEXT,
    "data" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "domain_events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "finance"."payables_types" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "code" VARCHAR(50) NOT NULL,
    "description" VARCHAR(255) NOT NULL,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "payables_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."employees" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "email" VARCHAR(320) NOT NULL,
    "position" VARCHAR(100) NOT NULL,
    "department" VARCHAR(100) NOT NULL,
    "hire_date" TIMESTAMP(3) NOT NULL,
    "address" JSONB,
    "personal_documents" JSONB,
    "dependents" JSONB,
    "status" "core"."EmployeeStatus" NOT NULL DEFAULT 'ACTIVE',
    "work_schedule" JSONB,
    "shift" JSONB,
    "gross_salary" DECIMAL(10,2),
    "meal_allowance" DECIMAL(10,2),
    "transport_allowance" DECIMAL(10,2),
    "health_plan" VARCHAR(100),
    "contract_type" JSONB,
    "seniority" JSONB,
    "phone" VARCHAR(20),
    "birth_date" TIMESTAMP(3),
    "work_hours" VARCHAR(50),
    "overtime_bank" BOOLEAN DEFAULT false,
    "vacations" JSONB,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "employees_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."customers" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "document" VARCHAR(20) NOT NULL,
    "email" VARCHAR(320) NOT NULL,
    "phone" VARCHAR(20),
    "address" JSONB,
    "status" "core"."CustomerStatus" NOT NULL DEFAULT 'ACTIVE',
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "customers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "finance"."finance.cost_centers" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "description" VARCHAR(255) NOT NULL,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "finance.cost_centers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."password_reset_tokens" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "used" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "password_reset_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."contracts" (
    "id" TEXT NOT NULL,
    "entityType" "core"."EntityType" NOT NULL,
    "entityUuid" TEXT NOT NULL,
    "contractType" "core"."ContractType" NOT NULL,
    "currentVersion" INTEGER NOT NULL DEFAULT 1,
    "status" "core"."ContractStatus" NOT NULL DEFAULT 'PENDING',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" VARCHAR(50) NOT NULL,
    "updated_by" VARCHAR(50) NOT NULL,

    CONSTRAINT "contracts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."contract_versions" (
    "id" TEXT NOT NULL,
    "versionId" INTEGER NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "uploadedBy" VARCHAR(50) NOT NULL,
    "filePath" TEXT NOT NULL,
    "signed" BOOLEAN NOT NULL DEFAULT false,
    "validatedBy" VARCHAR(50),
    "validatedAt" TIMESTAMP(3),
    "expirationDate" TIMESTAMP(3),
    "contractId" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "contract_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."document" (
    "id" TEXT NOT NULL,
    "entityType" "core"."EntityType" NOT NULL,
    "entityUuid" TEXT NOT NULL,
    "uploadedBy" TEXT NOT NULL,
    "currentVersion" INTEGER NOT NULL,
    "status" "core"."DocumentStatus" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "updated_by" TEXT,

    CONSTRAINT "document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "core"."document_version" (
    "id" TEXT NOT NULL,
    "versionId" INTEGER NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "uploadedBy" TEXT NOT NULL,
    "expirationDate" TIMESTAMP(3),
    "filePath" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "document_version_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "core"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "suppliers_document_key" ON "core"."suppliers"("document");

-- CreateIndex
CREATE UNIQUE INDEX "suppliers_user_id_key" ON "core"."suppliers"("user_id");

-- CreateIndex
CREATE INDEX "suppliers_name_idx" ON "core"."suppliers"("name");

-- CreateIndex
CREATE INDEX "suppliers_document_idx" ON "core"."suppliers"("document");

-- CreateIndex
CREATE INDEX "suppliers_user_id_idx" ON "core"."suppliers"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "companies_uuid_key" ON "core"."companies"("uuid");

-- CreateIndex
CREATE UNIQUE INDEX "companies_cnpj_key" ON "core"."companies"("cnpj");

-- CreateIndex
CREATE UNIQUE INDEX "payment_methods_uuid_key" ON "finance"."payment_methods"("uuid");

-- CreateIndex
CREATE UNIQUE INDEX "payment_methods_label_key" ON "finance"."payment_methods"("label");

-- CreateIndex
CREATE UNIQUE INDEX "sectors_uuid_key" ON "finance"."sectors"("uuid");

-- CreateIndex
CREATE UNIQUE INDEX "sectors_code_key" ON "finance"."sectors"("code");

-- CreateIndex
CREATE INDEX "sectors_code_idx" ON "finance"."sectors"("code");

-- CreateIndex
CREATE INDEX "sectors_description_idx" ON "finance"."sectors"("description");

-- CreateIndex
CREATE UNIQUE INDEX "domain_events_eventId_key" ON "events"."domain_events"("eventId");

-- CreateIndex
CREATE UNIQUE INDEX "payables_types_uuid_key" ON "finance"."payables_types"("uuid");

-- CreateIndex
CREATE UNIQUE INDEX "payables_types_code_key" ON "finance"."payables_types"("code");

-- CreateIndex
CREATE INDEX "payables_types_code_idx" ON "finance"."payables_types"("code");

-- CreateIndex
CREATE INDEX "payables_types_description_idx" ON "finance"."payables_types"("description");

-- CreateIndex
CREATE UNIQUE INDEX "employees_uuid_key" ON "core"."employees"("uuid");

-- CreateIndex
CREATE UNIQUE INDEX "employees_email_key" ON "core"."employees"("email");

-- CreateIndex
CREATE INDEX "employees_name_idx" ON "core"."employees"("name");

-- CreateIndex
CREATE INDEX "employees_email_idx" ON "core"."employees"("email");

-- CreateIndex
CREATE INDEX "employees_position_idx" ON "core"."employees"("position");

-- CreateIndex
CREATE INDEX "employees_department_idx" ON "core"."employees"("department");

-- CreateIndex
CREATE UNIQUE INDEX "customers_uuid_key" ON "core"."customers"("uuid");

-- CreateIndex
CREATE UNIQUE INDEX "customers_document_key" ON "core"."customers"("document");

-- CreateIndex
CREATE UNIQUE INDEX "customers_email_key" ON "core"."customers"("email");

-- CreateIndex
CREATE INDEX "customers_name_idx" ON "core"."customers"("name");

-- CreateIndex
CREATE INDEX "customers_document_idx" ON "core"."customers"("document");

-- CreateIndex
CREATE INDEX "customers_email_idx" ON "core"."customers"("email");

-- CreateIndex
CREATE UNIQUE INDEX "finance.cost_centers_uuid_key" ON "finance"."finance.cost_centers"("uuid");

-- CreateIndex
CREATE INDEX "finance.cost_centers_description_idx" ON "finance"."finance.cost_centers"("description");

-- CreateIndex
CREATE UNIQUE INDEX "password_reset_tokens_token_key" ON "core"."password_reset_tokens"("token");

-- CreateIndex
CREATE INDEX "password_reset_tokens_token_idx" ON "core"."password_reset_tokens"("token");

-- CreateIndex
CREATE INDEX "password_reset_tokens_userId_idx" ON "core"."password_reset_tokens"("userId");

-- CreateIndex
CREATE INDEX "password_reset_tokens_expiresAt_idx" ON "core"."password_reset_tokens"("expiresAt");

-- CreateIndex
CREATE INDEX "contracts_entityType_entityUuid_idx" ON "core"."contracts"("entityType", "entityUuid");

-- CreateIndex
CREATE UNIQUE INDEX "contract_versions_contractId_versionId_key" ON "core"."contract_versions"("contractId", "versionId");

-- CreateIndex
CREATE UNIQUE INDEX "document_version_documentId_versionId_key" ON "core"."document_version"("documentId", "versionId");

-- AddForeignKey
ALTER TABLE "core"."suppliers" ADD CONSTRAINT "suppliers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "core"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "core"."contract_versions" ADD CONSTRAINT "contract_versions_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "core"."contracts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "core"."document_version" ADD CONSTRAINT "document_version_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "core"."document"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
