{"level":"info","message":"Server started on http://localhost:3000","timestamp":"2025-07-28 03:01:47.888 PM"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-28 03:01:47.890 PM"}
{"level":"info","message":"Database: localhost:5432/fastwhite","timestamp":"2025-07-28 03:01:47.890 PM"}
{"level":"info","message":"Server started on http://localhost:3000","timestamp":"2025-07-28 06:09:05.753 PM"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-28 06:09:05.754 PM"}
{"level":"info","message":"Database: postgres:5432/fastwhite","timestamp":"2025-07-28 06:09:05.754 PM"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":4,"span_id":"9cbafc1d09f673d6","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 06:13:15.586 PM","trace_flags":"01","trace_id":"796c7bd898a111d01798d5ece89d9ecf","url":"/api/docs","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":3,"span_id":"12caf3dbb2835a91","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 06:13:16.887 PM","trace_flags":"01","trace_id":"10490f604faf7ddada8ceb0a792053d8","url":"/api/docs","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":0,"span_id":"b141dc259daed2ab","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 06:13:19.693 PM","trace_flags":"01","trace_id":"879679569528811eed7febff1c172ac8","url":"/metrics","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":0,"span_id":"6eefdb3fc2510961","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 06:13:19.898 PM","trace_flags":"01","trace_id":"b6260a35c234676ca5d613a34bc5da58","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":0,"span_id":"e092641d0b081540","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 06:21:03.632 PM","trace_flags":"01","trace_id":"8ccb46ce3cf55776bac8cc155687d7a1","url":"/metrics","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Server started on http://localhost:3000","timestamp":"2025-07-28 06:22:31.571 PM"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-28 06:22:31.573 PM"}
{"level":"info","message":"Database: postgres:5432/fastwhite","timestamp":"2025-07-28 06:22:31.573 PM"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":1,"span_id":"d0527697fad206a9","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 06:23:24.562 PM","trace_flags":"01","trace_id":"bb74e7238a881196712564817ddc090c","url":"/metrics","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Server started on http://localhost:3000","timestamp":"2025-07-28 06:47:32.262 PM"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-28 06:47:32.263 PM"}
{"level":"info","message":"Database: postgres:5432/fastwhite","timestamp":"2025-07-28 06:47:32.264 PM"}
{"level":"info","message":"Server started on http://localhost:3000","timestamp":"2025-07-28 07:06:54.031 PM"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-28 07:06:54.032 PM"}
{"level":"info","message":"Database: postgres:5432/fastwhite","timestamp":"2025-07-28 07:06:54.033 PM"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":3,"span_id":"49c47b9d10bc821d","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 07:34:09.332 PM","trace_flags":"01","trace_id":"a07bb9f2be42ec1d8e4ed320a064bd20","url":"/metrics","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":7,"span_id":"23ed721bbaf00c72","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 07:34:22.273 PM","trace_flags":"01","trace_id":"c01248b274f6681c8025ec693d6c4b93","url":"/api/docs","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":0,"span_id":"0467c772049f7a7b","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 07:34:47.084 PM","trace_flags":"01","trace_id":"823b9b0589631e1f13a55c319558561f","url":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Server started on http://localhost:3000","timestamp":"2025-07-28 07:48:48.918 PM"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-28 07:48:48.920 PM"}
{"level":"info","message":"Database: postgres:5432/fastwhite","timestamp":"2025-07-28 07:48:48.920 PM"}
{"level":"info","message":"Server started on http://localhost:3000","timestamp":"2025-07-28 07:58:30.093 PM"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-28 07:58:30.095 PM"}
{"level":"info","message":"Database: postgres:5432/fastwhite","timestamp":"2025-07-28 07:58:30.095 PM"}
{"event":"security_threat_detected","ip":"**********","level":"info","message":"Security threat detected and mitigated","method":"GET","processingTime":0,"span_id":"ccaacafc298bd6af","threats":["Malicious User-Agent detected"],"timestamp":"2025-07-28 07:59:28.403 PM","trace_flags":"01","trace_id":"b31eccac6cd6fdecd4d3a6168f7c526e","url":"/metrics","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-07-28 20:38:34.969","level":"info","message":"Server started on http://localhost:3000","service":"fastify-api","environment":"development"}
{"timestamp":"2025-07-28 20:38:34.970","level":"info","message":"Environment: development","service":"fastify-api","environment":"development"}
{"timestamp":"2025-07-28 20:38:34.970","level":"info","message":"Database: postgres:5432/fastwhite","service":"fastify-api","environment":"development"}
{"timestamp":"2025-07-28 20:41:58.641","level":"info","message":"Security threat detected and mitigated","service":"fastify-api","environment":"development","method":"GET","event":"security_threat_detected","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"/metrics","threats":["Malicious User-Agent detected"],"processingTime":0,"trace_id":"aac3447f60ed6ec999be0aa774445f1a","span_id":"3b98e0dfaee4d36b","trace_flags":"01"}
{"timestamp":"2025-07-28 20:42:04.435","level":"info","message":"Security threat detected and mitigated","service":"fastify-api","environment":"development","method":"GET","event":"security_threat_detected","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"/health/ready","threats":["Malicious User-Agent detected"],"processingTime":0,"trace_id":"6e0362f813716990f4ac7d13c015824f","span_id":"a76edd9ab54f10b9","trace_flags":"01"}
{"timestamp":"2025-07-28 20:42:04.441","level":"error","message":"Database health check failed","service":"fastify-api","environment":"development","error":"query.getSQL is not a function","responseTime":1,"trace_id":"6e0362f813716990f4ac7d13c015824f","span_id":"a76edd9ab54f10b9","trace_flags":"01"}
{"timestamp":"2025-07-28 20:42:04.772","level":"error","message":"Redis health check failed","service":"fastify-api","environment":"development","error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.","responseTime":329,"trace_id":"6e0362f813716990f4ac7d13c015824f","span_id":"a76edd9ab54f10b9","trace_flags":"01"}
{"timestamp":"2025-07-28 20:42:04.773","level":"warn","message":"Health check failed","service":"fastify-api","environment":"development","healthStatus":{"status":"unhealthy","timestamp":"2025-07-28T20:42:04.437Z","checks":{"database":{"status":"down","responseTime":1,"error":"query.getSQL is not a function"},"redis":{"status":"down","responseTime":329,"error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details."}}},"trace_id":"6e0362f813716990f4ac7d13c015824f","span_id":"a76edd9ab54f10b9","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:26.162","level":"info","message":"Security threat detected and mitigated","service":"fastify-api","environment":"development","method":"GET","event":"security_threat_detected","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"/health/ready","threats":["Malicious User-Agent detected"],"processingTime":0,"trace_id":"79ee52f2c14e1219fa7e30856ef974ab","span_id":"b499b820b4334912","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:26.163","level":"error","message":"Database health check failed","service":"fastify-api","environment":"development","error":"query.getSQL is not a function","responseTime":0,"trace_id":"79ee52f2c14e1219fa7e30856ef974ab","span_id":"b499b820b4334912","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:28.339","level":"info","message":"Request started","service":"fastify-api","environment":"development","requestId":"req-x","method":"GET","path":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"**********","trace_id":"b5e0d40deaa151dc3cd3c6c759b4491f","span_id":"0264de55b1216d91","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:28.340","level":"info","message":"Security threat detected and mitigated","service":"fastify-api","environment":"development","method":"GET","event":"security_threat_detected","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"/health","threats":["Malicious User-Agent detected"],"processingTime":1,"trace_id":"b5e0d40deaa151dc3cd3c6c759b4491f","span_id":"0264de55b1216d91","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:28.341","level":"error","message":"Database health check failed","service":"fastify-api","environment":"development","error":"query.getSQL is not a function","responseTime":0,"trace_id":"b5e0d40deaa151dc3cd3c6c759b4491f","span_id":"0264de55b1216d91","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:31.599","level":"error","message":"Redis health check failed","service":"fastify-api","environment":"development","error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.","responseTime":5436,"trace_id":"79ee52f2c14e1219fa7e30856ef974ab","span_id":"b499b820b4334912","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:31.600","level":"error","message":"Redis health check failed","service":"fastify-api","environment":"development","error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.","responseTime":3259,"trace_id":"b5e0d40deaa151dc3cd3c6c759b4491f","span_id":"0264de55b1216d91","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:31.600","level":"warn","message":"Health check failed","service":"fastify-api","environment":"development","healthStatus":{"status":"unhealthy","timestamp":"2025-07-28T20:43:26.163Z","checks":{"database":{"status":"down","responseTime":0,"error":"query.getSQL is not a function"},"redis":{"status":"down","responseTime":5436,"error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details."}}},"trace_id":"79ee52f2c14e1219fa7e30856ef974ab","span_id":"b499b820b4334912","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:31.600","level":"warn","message":"Health check failed","service":"fastify-api","environment":"development","healthStatus":{"status":"unhealthy","timestamp":"2025-07-28T20:43:28.340Z","checks":{"database":{"status":"down","responseTime":0,"error":"query.getSQL is not a function"},"redis":{"status":"down","responseTime":3259,"error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details."}}},"trace_id":"b5e0d40deaa151dc3cd3c6c759b4491f","span_id":"0264de55b1216d91","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:31.602","level":"warn","message":"Slow request detected","service":"fastify-api","environment":"development","requestId":"req-x","duration":3262,"statusCode":503,"method":"GET","path":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"**********","trace_id":"b5e0d40deaa151dc3cd3c6c759b4491f","span_id":"0264de55b1216d91","trace_flags":"01"}
{"timestamp":"2025-07-28 20:43:31.602","level":"http","message":"GET /health 503 - 3262ms","service":"fastify-api","environment":"development","requestId":"req-x","duration":3262,"statusCode":503,"method":"GET","path":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"**********","trace_id":"b5e0d40deaa151dc3cd3c6c759b4491f","span_id":"0264de55b1216d91","trace_flags":"01"}
{"timestamp":"2025-07-28 20:45:31.473","level":"info","message":"Security threat detected and mitigated","service":"fastify-api","environment":"development","method":"GET","event":"security_threat_detected","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"/health/ready","threats":["Malicious User-Agent detected"],"processingTime":1,"trace_id":"20bcb7850f5defcb720ef0bb620c223e","span_id":"286e8622ca163eb1","trace_flags":"01"}
{"timestamp":"2025-07-28 20:45:31.473","level":"error","message":"Database health check failed","service":"fastify-api","environment":"development","error":"query.getSQL is not a function","responseTime":0,"trace_id":"20bcb7850f5defcb720ef0bb620c223e","span_id":"286e8622ca163eb1","trace_flags":"01"}
{"timestamp":"2025-07-28 20:45:31.713","level":"error","message":"Redis health check failed","service":"fastify-api","environment":"development","error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.","responseTime":239,"trace_id":"20bcb7850f5defcb720ef0bb620c223e","span_id":"286e8622ca163eb1","trace_flags":"01"}
{"timestamp":"2025-07-28 20:45:31.714","level":"warn","message":"Health check failed","service":"fastify-api","environment":"development","healthStatus":{"status":"unhealthy","timestamp":"2025-07-28T20:45:31.473Z","checks":{"database":{"status":"down","responseTime":0,"error":"query.getSQL is not a function"},"redis":{"status":"down","responseTime":239,"error":"Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details."}}},"trace_id":"20bcb7850f5defcb720ef0bb620c223e","span_id":"286e8622ca163eb1","trace_flags":"01"}
