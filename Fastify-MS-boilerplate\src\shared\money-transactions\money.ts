/**
 * A class to handle money operations, ensuring precision and safety.
 * Internally, this class handles amounts in cents to avoid floating-point issues.
 */
export class Money {
  private valueInCents: number;

  /**
   * Create a new Money instance.
   * @param {string | number | Money} value - The monetary value, can be a string, number, or another Money instance.
   * @param {Object} [options] - Options for the constructor.
   * @param {boolean} [options.isCents=false] - If true, the value is treated as cents.
   */
  constructor(value: string | number | Money, options: { isCents?: boolean } = {}) {
    const { isCents = false } = options;

    if (value instanceof Money) {
      this.valueInCents = value.getInCents().number();
    } else {
      const parsedValue = this.parseToNumber(value);
      this.valueInCents = isCents
        ? Math.round(parsedValue) // Se for centavos, não multiplica por 100
        : Math.round(parsedValue * 100); // Se for reais, converte para centavos
    }
  }

  /**
   * Parse and validate the input to ensure it's a valid number.
   * @param {string | number} value - The value to parse and validate.
   * @returns {number} - The parsed number.
   * @throws {Error} - Throws an error if the input is invalid.
   */
  private parseToNumber(value: string | number): number {
    const parsed = parseFloat(value.toString().replace(",", ".")); // Lida com valores no formato "1.234,56"
    if (isNaN(parsed)) {
      throw new Error(`Invalid monetary value: ${value}`);
    }
    return parsed;
  }

  /**
   * Get the value in cents.
   * @returns {{ string: () => string, number: () => number }} - The value in cents as string and number.
   */
  getInCents(): { string: () => string; number: () => number } {
    return {
      string: () => this.valueInCents.toString(),
      number: () => this.valueInCents
    };
  }

  /**
   * Get the value in reais.
   * @returns {{ string: () => string, number: () => number }} - The value in reais as string and number.
   */
  getInReais(): { string: () => string; number: () => number } {
    const valueInReais = this.valueInCents / 100;
    return {
      string: () => valueInReais.toFixed(2),
      number: () => valueInReais
    };
  }

  /**
   * Add another monetary value to the current Money instance.
   * @param {Money} amount - The amount to add, must be an instance of Money.
   * @returns {Money} - A new Money instance with the sum.
   */
  add(amount: Money): Money {
    return new Money(this.valueInCents + amount.getInCents().number(), { isCents: true });
  }

  /**
   * Subtract a monetary value from the current Money instance.
   * @param {Money} amount - The amount to subtract, must be an instance of Money.
   * @returns {Money} - A new Money instance with the difference.
   */
  subtract(amount: Money): Money {
    return new Money(this.valueInCents - amount.getInCents().number(), { isCents: true });
  }

  /**
   * Multiply the current value by a percentage or multiplier.
   * @param {number} multiplier - The multiplier to apply.
   * @returns {Money} - A new Money instance with the multiplied value.
   */
  multiply(multiplier: number): Money {
    const multipliedValue = Math.round(this.valueInCents * multiplier);
    return new Money(multipliedValue, { isCents: true }); // Operação em centavos
  }

  /**
   * Compare if the current Money instance is equal to another monetary value.
   * @param {Money} other - The value to compare with, must be a Money instance.
   * @returns {boolean} - Returns true if values are equal, false otherwise.
   */
  isEqualTo(other: Money): boolean {
    return this.valueInCents === other.getInCents().number();
  }

  /**
   * Compare if the current Money instance is greater than another monetary value.
   * @param {Money} other - The value to compare with, must be a Money instance.
   * @returns {boolean} - Returns true if the current value is greater, false otherwise.
   */
  isGreaterThan(other: Money): boolean {
    return this.valueInCents > other.getInCents().number();
  }

  /**
   * Compare if the current Money instance is less than another monetary value.
   * @param {Money} other - The value to compare with, must be a Money instance.
   * @returns {boolean} - Returns true if the current value is less, false otherwise.
   */
  isLessThan(other: Money): boolean {
    return this.valueInCents < other.getInCents().number();
  }
}
