import { Task } from '../../../domain/task.entity';
import { TaskRepository } from '../../../ports/repositories/task-repository.interface';
import { TaskStatus } from '../../../domain/task-status.enum';

export interface UpdateTaskInput {
  id: string;
  title?: string;
  description?: string;
  status?: TaskStatus;
}

export class UpdateTaskUseCase {
  constructor(private taskRepository: TaskRepository) {}

  async execute(input: UpdateTaskInput): Promise<Task> {
    const { id, title, description, status } = input;

    // Buscar a tarefa existente
    const task = await this.taskRepository.findById(id);

    if (!task) {
      throw new Error(`Tarefa com ID ${id} não encontrada`);
    }

    // Aplicar as atualizações na entidade de domínio
    if (title) {
      task.updateTitle(title);
    }

    if (description !== undefined) {
      task.updateDescription(description);
    }

    if (status) {
      switch (status) {
        case TaskStatus.IN_PROGRESS:
          task.start();
          break;
        case TaskStatus.COMPLETED:
          task.complete();
          break;
        case TaskStatus.CANCELED:
          task.cancel();
          break;
      }
    }

    // Persistir a tarefa atualizada
    return this.taskRepository.update(task);
  }
}
