import { Test, type TestingModule } from '@nestjs/testing';
import { type ExecutionContext, ForbiddenException } from '@nestjs/common';
import { RolesGuard } from '../../../guards/roles.guard';
import { Reflector } from '@nestjs/core';

interface KeycloakUser {
  realm_access?: {
    roles: string[];
  };
  resource_access?: {
    [clientId: string]: {
      roles: string[];
    };
  };
  azp?: string;
}

interface MockRequest {
  user?: KeycloakUser | null;
}

describe('RolesGuard', () => {
  let guard: RolesGuard;
  let reflector: Reflector;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolesGuard,
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<RolesGuard>(RolesGuard);
    reflector = module.get<Reflector>(Reflector);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('should return true when no roles are required', () => {
    jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(null);

    const context = createMockExecutionContext({
      user: {
        realm_access: {
          roles: ['user'],
        },
      },
    });

    expect(guard.canActivate(context)).toBe(true);
  });

  it('should throw ForbiddenException when user is not authenticated', () => {
    jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['admin']);

    const context = createMockExecutionContext({
      user: null,
    });

    expect(() => guard.canActivate(context)).toThrow(ForbiddenException);
  });

  it('should return true when user has required role in realm_access', () => {
    jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['admin']);

    const context = createMockExecutionContext({
      user: {
        realm_access: {
          roles: ['admin'],
        },
      },
    });

    expect(guard.canActivate(context)).toBe(true);
  });

  it('should return true when user has required role in resource_access', () => {
    jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['admin']);

    const context = createMockExecutionContext({
      user: {
        azp: 'test-client',
        resource_access: {
          'test-client': {
            roles: ['admin'],
          },
        },
      },
    });

    expect(guard.canActivate(context)).toBe(true);
  });

  it('should allow access when user has required role', () => {
    jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['ADMIN']);

    const context = createMockExecutionContext({
      user: {
        realm_access: {
          roles: ['ADMIN'],
        },
        resource_access: {
          'test-client': {
            roles: ['ADMIN'],
          },
        },
      },
    });

    expect(guard.canActivate(context)).toBe(true);
  });

  it('should allow access when user has multiple roles including required role', () => {
    jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['ADMIN']);

    const context = createMockExecutionContext({
      user: {
        realm_access: {
          roles: ['USER', 'ADMIN'],
        },
        resource_access: {
          'test-client': {
            roles: ['USER', 'ADMIN'],
          },
        },
      },
    });

    expect(guard.canActivate(context)).toBe(true);
  });

  it('should throw ForbiddenException when user has no roles', () => {
    jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['ADMIN']);

    const context = createMockExecutionContext({
      user: {
        realm_access: {
          roles: [],
        },
        resource_access: {
          'test-client': {
            roles: [],
          },
        },
      },
    });

    expect(() => guard.canActivate(context)).toThrow(ForbiddenException);
  });

  it('should throw ForbiddenException when user does not have required role', () => {
    jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['ADMIN']);

    const context = createMockExecutionContext({
      user: {
        realm_access: {
          roles: ['USER'],
        },
        resource_access: {
          'test-client': {
            roles: ['USER'],
          },
        },
      },
    });

    expect(() => guard.canActivate(context)).toThrow(ForbiddenException);
  });
});

function createMockExecutionContext(
  mockRequest: MockRequest,
): ExecutionContext {
  return {
    getClass: jest.fn(),
    getHandler: jest.fn(),
    getArgs: jest.fn(),
    getArgByIndex: jest.fn(),
    getType: jest.fn(),
    switchToHttp: () => ({
      getRequest: <T = unknown>() => mockRequest as T,
      getResponse: jest.fn(),
      getNext: jest.fn(),
    }),
    switchToRpc: jest.fn(),
    switchToWs: jest.fn(),
  };
}
