import { DeleteCompanyUseCase } from './delete-company.use-case';
import { CompanyRepository } from '../../../ports/repositories/company-repository.interface';
import { Company, CompanyStatus } from '../../../domain/company.entity';

describe('DeleteCompanyUseCase', () => {
  let useCase: DeleteCompanyUseCase;
  let companyRepository: jest.Mocked<CompanyRepository>;

  beforeEach(() => {
    companyRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      findByUuid: jest.fn(),
      findByCnpj: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    useCase = new DeleteCompanyUseCase(companyRepository);
  });

  it('should delete a company successfully', async () => {
    const input = {
      uuid: '550e8400-e29b-41d4-a716-************',
    };

    const mockCompany = new Company(
      1,
      '550e8400-e29b-41d4-a716-************',
      'Company Name',
      '12345678000195',
      {
        street: 'Av. Paulista, 1000',
        city: 'São Paulo',
        zipCode: '01310-100',
        state: 'SP',
      },
      '(11) 98765-4321',
      '<EMAIL>',
      CompanyStatus.ACTIVE,
      'admin-user',
    );

    const findByUuidSpy = jest.spyOn(companyRepository, 'findByUuid');
    const deleteSpy = jest.spyOn(companyRepository, 'delete');

    findByUuidSpy.mockResolvedValue(mockCompany);
    deleteSpy.mockResolvedValue(undefined);

    await useCase.execute(input);

    expect(findByUuidSpy).toHaveBeenCalledWith(input.uuid);
    expect(deleteSpy).toHaveBeenCalled();
  });

  it('should throw an error if company not exists', async () => {
    const input = {
      uuid: '550e8400-e29b-41d4-a716-************',
    };

    const findByUuidSpy = jest.spyOn(companyRepository, 'findByUuid');
    const deleteSpy = jest.spyOn(companyRepository, 'delete');

    findByUuidSpy.mockResolvedValue(null);

    await expect(useCase.execute(input)).rejects.toThrow(
      'Empresa não encontrada.',
    );
    expect(findByUuidSpy).toHaveBeenCalledWith(input.uuid);
    expect(deleteSpy).not.toHaveBeenCalled();
  });
});
