import { Test, TestingModule } from '@nestjs/testing';
import { ValidateSupplierActivationUseCase } from './validate-supplier-activation.use-case';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '@prisma/client';
import { Contact } from '../../../domain/supplier/value-objects/contact.value-object';

describe('ValidateSupplierActivationUseCase', () => {
  let useCase: ValidateSupplierActivationUseCase;
  let supplierRepository: jest.Mocked<SupplierRepositoryPort>;
  let prismaService: jest.Mocked<PrismaService>;

  beforeEach(async () => {
    const mockSupplierRepository = {
      findById: jest.fn(),
      update: jest.fn(),
      create: jest.fn(),
      findAll: jest.fn(),
      findByDocument: jest.fn(),
      delete: jest.fn(),
    };

    const mockPrismaService = {
      contract: {
        count: jest.fn(),
      },
      document: {
        count: jest.fn(),
      },
      service: {
        count: jest.fn(),
      },
      supplierContact: {
        count: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ValidateSupplierActivationUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockSupplierRepository,
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    useCase = module.get<ValidateSupplierActivationUseCase>(ValidateSupplierActivationUseCase);
    supplierRepository = module.get(SUPPLIER_REPOSITORY);
    prismaService = module.get(PrismaService);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should activate supplier when all requirements are met', async () => {
      const mockSupplier = new Supplier(
        'test-id',
        'Test Supplier',
        '**************',
        'Test Trade',
        new Address('Test Street', '123', null, null, 'Test City', '12345-678', 'TS'),
        '<EMAIL>',
        SupplierClassification.CORE,
        SupplierType.BANK,
        SupplierStatus.PENDING,
        'user-id',
        'created-by',
        new Date('2024-01-01T00:00:00Z'),
        new Date('2024-01-01T00:00:00Z'),
        'created-by',
      );
      supplierRepository.findById.mockResolvedValue(mockSupplier);
      (prismaService.contract.count as jest.Mock).mockResolvedValue(1);
      (prismaService.document.count as jest.Mock).mockResolvedValue(1);
      (prismaService.service.count as jest.Mock).mockResolvedValue(1);
      (prismaService.supplierContact.count as jest.Mock).mockResolvedValue(1);
      supplierRepository.update.mockResolvedValue(mockSupplier);

      const result = await useCase.execute('test-id', 'updated-by');

      expect(result).toBe(true);
      expect(supplierRepository.update).toHaveBeenCalled();
    });

    it('should not activate supplier when missing requirements', async () => {
      const mockSupplier = new Supplier(
        'test-id',
        'Test Supplier',
        '**************',
        'Test Trade',
        new Address('Test Street', '123', null, null, 'Test City', '12345-678', 'TS'),
        '<EMAIL>',
        SupplierClassification.CORE,
        SupplierType.BANK,
        SupplierStatus.PENDING,
        'user-id',
        'created-by',
        new Date('2024-01-01T00:00:00Z'),
        new Date('2024-01-01T00:00:00Z'),
        'created-by',
      );
      supplierRepository.findById.mockResolvedValue(mockSupplier);
      (prismaService.contract.count as jest.Mock).mockResolvedValue(0); // Missing contract
      (prismaService.document.count as jest.Mock).mockResolvedValue(1);
      (prismaService.service.count as jest.Mock).mockResolvedValue(1);
      (prismaService.supplierContact.count as jest.Mock).mockResolvedValue(1);

      const result = await useCase.execute('test-id', 'updated-by');

      expect(result).toBe(false);
      expect(supplierRepository.update).not.toHaveBeenCalled();
    });

    it('should return true if supplier is already active', async () => {
      const activeSupplier = new Supplier(
        'test-id',
        'Test Supplier',
        '**************',
        'Test Trade',
        new Address('Test Street', '123', null, null, 'Test City', '12345-678', 'TS'),
        '<EMAIL>',
        SupplierClassification.CORE,
        SupplierType.BANK,
        SupplierStatus.ACTIVE,
        'user-id',
        'created-by',
        new Date('2024-01-01T00:00:00Z'),
        new Date('2024-01-01T00:00:00Z'),
        'created-by',
      );

      supplierRepository.findById.mockResolvedValue(activeSupplier);

      const result = await useCase.execute('test-id', 'updated-by');

      expect(result).toBe(true);
      expect((prismaService.contract.count as jest.Mock).mock.calls.length).toBe(0);
    });

    it('should throw error when supplier not found', async () => {
      supplierRepository.findById.mockResolvedValue(null);

      await expect(useCase.execute('test-id', 'updated-by')).rejects.toThrow('Supplier not found');
    });
  });

  describe('validateRequirements', () => {
    it('should return correct validation status', async () => {
      const mockSupplier = new Supplier(
        'test-id',
        'Test Supplier',
        '**************',
        'Test Trade',
        new Address('Test Street', '123', null, null, 'Test City', '12345-678', 'TS'),
        '<EMAIL>',
        SupplierClassification.CORE,
        SupplierType.BANK,
        SupplierStatus.PENDING,
        'user-id',
        'created-by',
        new Date('2024-01-01T00:00:00Z'),
        new Date('2024-01-01T00:00:00Z'),
        'created-by',
      );
      supplierRepository.findById.mockResolvedValue(mockSupplier);
      (prismaService.contract.count as jest.Mock).mockResolvedValue(1);
      (prismaService.document.count as jest.Mock).mockResolvedValue(0); // Missing document
      (prismaService.service.count as jest.Mock).mockResolvedValue(1);
      (prismaService.supplierContact.count as jest.Mock).mockResolvedValue(1);

      const result = await useCase.validateRequirements('test-id');

      expect(result).toEqual({
        hasContract: true,
        hasDocument: false,
        hasService: true,
        hasContact: true,
        canActivate: false,
        currentStatus: SupplierStatus.PENDING,
      });
    });

    it('should return true for canActivate when all requirements are met', async () => {
      const mockSupplier = new Supplier(
        'test-id',
        'Test Supplier',
        '**************',
        'Test Trade',
        new Address('Test Street', '123', null, null, 'Test City', '12345-678', 'TS'),
        '<EMAIL>',
        SupplierClassification.CORE,
        SupplierType.BANK,
        SupplierStatus.PENDING,
        'user-id',
        'created-by',
        new Date('2024-01-01T00:00:00Z'),
        new Date('2024-01-01T00:00:00Z'),
        'created-by',
      );
      supplierRepository.findById.mockResolvedValue(mockSupplier);
      (prismaService.contract.count as jest.Mock).mockResolvedValue(1);
      (prismaService.document.count as jest.Mock).mockResolvedValue(1);
      (prismaService.service.count as jest.Mock).mockResolvedValue(1);
      (prismaService.supplierContact.count as jest.Mock).mockResolvedValue(1);

      const result = await useCase.validateRequirements('test-id');

      expect(result.canActivate).toBe(true);
    });

    it('should throw error when supplier not found', async () => {
      supplierRepository.findById.mockResolvedValue(null);

      await expect(useCase.validateRequirements('test-id')).rejects.toThrow('Supplier not found');
    });
  });
}); 