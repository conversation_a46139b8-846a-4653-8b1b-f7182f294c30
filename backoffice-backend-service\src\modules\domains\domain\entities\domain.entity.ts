import { LicenseType } from '@prisma/client';
import { Customer } from '../../../customers/domain/entities/customer.entity';
// import { User } from '../../../users/domain/entities/user.entity';

export class Domain {
  id: number;
  uuid: string;
  customerUuid: string;
  domain: string;
  brandName: string;
  notes: string | undefined;
  licenseNumber: string;
  licenseType: LicenseType;
  createdBy: string;
  updatedBy: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;

  // Relations
  customer?: Customer;
  creator?: any;
  updater?: any;
} 