import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  MinLength,
  MaxLength,
  Matches,
  ValidateNested,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CustomerStatus } from '../../domain/entities/customer.entity';

class AddressDto {
  @ApiProperty({
    description: 'Street address',
    example: 'Rua das Flores',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  street: string;

  @ApiProperty({
    description: 'Number of the address',
    example: '123',
    required: false,
    maxLength: 10,
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  number?: string;

  @ApiProperty({
    description: 'Complementary information (optional)',
    example: 'Apt 45',
    required: false,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  complement?: string;

  @ApiProperty({
    description: 'Neighborhood name',
    example: '<PERSON><PERSON><PERSON>',
    required: false,
    minLength: 3,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  neighborhood?: string;

  @ApiProperty({
    description: 'City name',
    example: 'São Paulo',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  city: string;

  @ApiProperty({
    description: 'State abbreviation (2 characters)',
    example: 'SP',
    minLength: 2,
    maxLength: 2,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(2)
  state: string;

  @ApiProperty({
    description: 'ZIP code in format 00000-000 or 00000000',
    example: '01311-000',
    pattern: '^\\d{5}-?\\d{3}$',
  })
  @IsString()
  @Matches(/^\d{5}-?\d{3}$/, {
    message: 'Invalid zip code format. Use: 00000-000 or 00000000',
  })
  zipCode: string;
}

export class CreateCustomerDto {
  @ApiProperty({ description: 'Razão Social', example: 'ACME Corp' })
  @IsString()
  @IsNotEmpty()
  razaoSocial: string;

  @ApiProperty({ description: 'CNPJ', example: '12345678901234' })
  @IsString()
  @IsNotEmpty()
  cnpj: string;

  @ApiProperty({ description: 'Email', example: '<EMAIL>' })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'Phone', example: '******-456-7890' })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ description: 'Address information', type: AddressDto })
  @ValidateNested()
  @Type(() => AddressDto)
  @IsNotEmpty()
  address: AddressDto;

  @ApiPropertyOptional({ description: 'Imagem (base64 ou URL)', example: 'https://example.com/image.png' })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({
    description: 'Customer status',
    enum: CustomerStatus,
    example: CustomerStatus.ACTIVE,
  })
  @IsEnum(CustomerStatus)
  status?: CustomerStatus;

  @ApiProperty({ description: 'url', example: 'https://example.com' })
  @IsString()
  @IsNotEmpty()
  url: string;
}
