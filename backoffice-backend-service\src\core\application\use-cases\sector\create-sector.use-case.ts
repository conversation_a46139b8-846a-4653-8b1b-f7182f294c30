import { Injectable, Inject } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { Sector } from '@/core/domain/entities/sector.entity';
import { SectorRepositoryPort } from '@/core/ports/repositories/sector-repository.port';
import { EventPublisherService } from '@/core/ports/events/event-publisher.service';
import { DuplicateCodeError } from '@/infrastructure/exceptions/duplicate-code.error';

export interface CreateSectorInput {
  code: string;
  description: string;
  createdBy: string;
  updatedBy: string;
}

@Injectable()
export class CreateSectorUseCase {
  constructor(
    @Inject('SECTOR_REPOSITORY')
    private readonly sectorRepository: SectorRepositoryPort,
    @Inject('EVENT_PUBLISHER')
    private readonly eventPublisher: EventPublisherService,
  ) {}

  async execute(input: CreateSectorInput): Promise<Sector> {
    const existingSector = await this.sectorRepository.findByCode(input.code);

    if (existingSector) {
      throw new DuplicateCodeError(input.code);
    }

    const sector = Sector.create(
      0, // ID será gerado pelo banco
      uuidv4(),
      input.code,
      input.description,
      input.createdBy,
      input.updatedBy,
      new Date(),
      new Date(),
    );

    const savedSector = await this.sectorRepository.save(sector);

    // Publicar eventos de domínio
    await this.eventPublisher.publishAll(savedSector.getDomainEvents());
    savedSector.clearEvents();

    return savedSector;
  }
}
