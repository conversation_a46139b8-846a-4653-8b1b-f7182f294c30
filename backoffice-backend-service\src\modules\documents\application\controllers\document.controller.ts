import {
  Controller,
  Get,
  Post,
  UploadedFile,
  UseInterceptors,
  Body,
  Query,
  Param,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  UseGuards,
  BadRequestException,
  Patch,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { ListDocumentsUseCase } from '../use-cases/list-documents.use-case';
import { FindDocumentByUuidUseCase } from '../use-cases/find-document-by-uuid.use-case';
import { CreateDocumentDto } from '../../infrastructure/dto/create-document.dto';
import { FilterDocumentsDto } from '../../infrastructure/dto/filter-document.dto';
import { DocumentListResponseDto } from '../../infrastructure/dto/document-list-response.dto';
import { DocumentResponseDto } from '../../infrastructure/dto/document-response.dto';
import { Roles } from '@/core/decorators/roles.decorator';
import { Role } from '@/core/enums/role.enum';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { CreateDocumentUseCase } from '../use-cases/create-document.use-case';
import { ArchiveDocumentUseCase } from '../use-cases/archive-documento.use-case';
import { ArchiveDocumentDto } from '../../infrastructure/dto/archive-document.dto';
import { DownloadDocumentUseCase } from '../use-cases/download-document.use-case';
import { CreateDocumentVersionDto } from '../../infrastructure/dto/create-document-version.dto';
import { DocumentVersionResponseDto } from '../../infrastructure/dto/document-version-response.dto';
import { AddDocumentVersionUseCase } from '../use-cases/add-version-document.use-case';
import { ListDocumentVersionsUseCase } from '../use-cases/list-document-version.use-case';

@ApiTags('Documents')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('admin/documents')
export class DocumentsController {
  constructor(
    private readonly createDocumentUseCase: CreateDocumentUseCase,
    private readonly listDocumentsUseCase: ListDocumentsUseCase,
    private readonly findDocumentByUuidUseCase: FindDocumentByUuidUseCase,
    private readonly archiveDocumentUseCase: ArchiveDocumentUseCase,
    private readonly downloadDocumentUseCase: DownloadDocumentUseCase,
    private readonly addDocumentVersionUseCase: AddDocumentVersionUseCase,
    private readonly listDocumentVersionsUseCase: ListDocumentVersionsUseCase,
  ) {}

  @Post()
  @Roles(Role.DOCUMENT_UPLOADER)
  @HttpCode(HttpStatus.CREATED)
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: CreateDocumentDto })
  @ApiOperation({ summary: 'Upload de novo documento para uma entidade' })
  @ApiResponse({ status: 201, description: 'Documento criado com sucesso' })
  @UseInterceptors(FileInterceptor('file'))
  async upload(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: CreateDocumentDto,
  ) {
    if (!file) {
      throw new BadRequestException('Arquivo é obrigatório');
    }
    const document = await this.createDocumentUseCase.execute(dto, file);
    return DocumentResponseDto.fromEntity(document);
  }

  @Get()
  @Roles(Role.DOCUMENT_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lista documentos com filtros e paginação' })
  async list(
    @Query() filters: FilterDocumentsDto,
  ): Promise<DocumentListResponseDto> {
    const result = await this.listDocumentsUseCase.execute(filters);
    return {
      items: result.items.map((item) => DocumentResponseDto.fromEntity(item)),
      limit: result.limit,
      offset: result.offset,
      total: result.total,
    };
  }

  @Get(':uuid')
  @Roles(Role.DOCUMENT_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Obtém metadados de um documento' })
  async getMetadata(
    @Param('uuid', ParseUUIDPipe) uuid: string,
  ): Promise<DocumentResponseDto> {
    const document = await this.findDocumentByUuidUseCase.execute(uuid);
    return DocumentResponseDto.fromEntity(document);
  }

  @Patch(':uuid')
  @Roles(Role.DOCUMENT_ARCHIVER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Arquiva um documento' })
  @ApiResponse({ status: 200, description: 'Documento arquivado com sucesso' })
  @ApiResponse({ status: 400, description: 'Documento já está arquivado' })
  @ApiResponse({ status: 404, description: 'Documento não encontrado' })
  async archive(
    @Param('uuid', ParseUUIDPipe) uuid: string,
    @Body() dto: ArchiveDocumentDto,
  ): Promise<{ message: string }> {
    await this.archiveDocumentUseCase.execute(uuid, dto.archivedBy, new Date());
    return { message: 'Documento com status archived' };
  }

  @Get(':uuid/download')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Retorna a URL para download do documento' })
  @ApiQuery({ name: 'versionId', required: false })
  async getDownloadUrl(
    @Param('uuid', ParseUUIDPipe) uuid: string,
    @Query('versionId') versionId?: string,
  ): Promise<{ url: string; fileName: string }> {
    return this.downloadDocumentUseCase.execute(
      uuid,
      versionId ? Number(versionId) : undefined,
    );
  }
  

  @Post(':uuid/versions')
  @Roles(Role.DOCUMENT_UPLOADER)
  @HttpCode(HttpStatus.CREATED)
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: CreateDocumentVersionDto })
  @ApiOperation({ summary: 'Adiciona nova versão ao documento' })
  @ApiResponse({ status: 201, description: 'Versão criada com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 404, description: 'Documento não encontrado' })
  @UseInterceptors(FileInterceptor('file'))
  async addVersion(
    @Param('uuid', ParseUUIDPipe) uuid: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: CreateDocumentVersionDto,
  ): Promise<DocumentVersionResponseDto> {
    if (!file) {
      throw new BadRequestException('Arquivo é obrigatório');
    }

    const version = await this.addDocumentVersionUseCase.execute(
      uuid,
      dto,
      file,
    );

    if (!version || typeof version.versionId !== 'number') {
      throw new BadRequestException('Invalid document version response');
    }

    return DocumentVersionResponseDto.fromEntity(version);
  }

  @Get(':uuid/versions')
  @Roles(Role.DOCUMENT_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lista todas as versões de um documento' })
  @ApiResponse({ status: 200, description: 'Versões listadas com sucesso' })
  @ApiResponse({ status: 404, description: 'Documento não encontrado' })
  async listVersions(
    @Param('uuid', ParseUUIDPipe) uuid: string,
  ): Promise<DocumentVersionResponseDto[]> {
    const versions = await this.listDocumentVersionsUseCase.execute(uuid);
    return versions.map((version) =>
      DocumentVersionResponseDto.fromEntity(version),
    );
  }
}
