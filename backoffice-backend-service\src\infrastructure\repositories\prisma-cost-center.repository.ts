import { Injectable } from '@nestjs/common';
import { PrismaService } from '@infrastructure/prisma/prisma.service';
import { CostCenterRepositoryPort } from '@core/ports/repositories/cost-center-repository.port';
import { CostCenter } from '@core/domain/cost-center/entities/cost-center.entity';

// Definição temporária do tipo PrismaCostCenter até que o Prisma gere o tipo
interface PrismaCostCenter {
  id: number;
  uuid: string;
  description: string;
  createdBy: string;
  updatedBy: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

@Injectable()
export class PrismaCostCenterRepository implements CostCenterRepositoryPort {
  constructor(private readonly prisma: PrismaService) {}

  private toDomain(prismaCostCenter: PrismaCostCenter): CostCenter {
    return new CostCenter({
      id: prismaCostCenter.uuid,
      description: prismaCostCenter.description,
      createdBy: prismaCostCenter.createdBy,
      updatedBy: prismaCostCenter.updatedBy,
      createdAt: prismaCostCenter.createdAt,
      updatedAt: prismaCostCenter.updatedAt,
    });
  }

  async create(costCenter: CostCenter): Promise<CostCenter> {
    const created = await this.prisma.costCenter.create({
      data: {
        uuid: costCenter.id,
        description: costCenter.description,
        createdBy: costCenter.createdBy,
        updatedBy: costCenter.updatedBy,
      },
    });
    return this.toDomain(created);
  }

  async findById(id: number): Promise<CostCenter | null> {
    const costCenter = await this.prisma.costCenter.findUnique({
      where: { id },
    });
    if (!costCenter || costCenter.deletedAt) return null;
    return this.toDomain(costCenter);
  }

  async findByUuid(uuid: string): Promise<CostCenter | null> {
    const costCenter = await this.prisma.costCenter.findUnique({
      where: { uuid },
    });
    if (!costCenter || costCenter.deletedAt) return null;
    return this.toDomain(costCenter);
  }

  async findAll(): Promise<CostCenter[]> {
    const costCenters = await this.prisma.costCenter.findMany({
      where: { deletedAt: null },
    });
    return costCenters.map((cc) => this.toDomain(cc));
  }

  async findWithPagination(params: {
    limit: number;
    offset: number;
    description?: string;
  }): Promise<{ items: CostCenter[]; total: number }> {
    const where: Record<string, unknown> = { deletedAt: null };
    if (params.description) {
      where.description = {
        contains: params.description,
        mode: 'insensitive',
      };
    }
    const [items, total] = await Promise.all([
      this.prisma.costCenter.findMany({
        where,
        skip: params.offset,
        take: params.limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.costCenter.count({ where }),
    ]);
    return {
      items: items.map((cc) => this.toDomain(cc)),
      total: total,
    };
  }

  async update(costCenter: CostCenter): Promise<CostCenter> {
    const updated = await this.prisma.costCenter.update({
      where: { uuid: costCenter.id },
      data: {
        description: costCenter.description,
        updatedBy: costCenter.updatedBy,
      },
    });
    return this.toDomain(updated);
  }

  async delete(uuid: string): Promise<void> {
    await this.prisma.costCenter.update({
      where: { uuid },
      data: { deletedAt: new Date() },
    });
  }
}
