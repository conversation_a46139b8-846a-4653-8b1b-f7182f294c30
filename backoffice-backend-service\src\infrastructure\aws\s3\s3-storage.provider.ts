import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { fromEnv, fromNodeProviderChain } from '@aws-sdk/credential-providers';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { Readable } from 'stream';
import { basename } from 'path';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'; 

@Injectable()
export class S3StorageProvider implements IStorageProvider {
  private readonly s3: S3Client;

  constructor() {
    const region = process.env.AWS_REGION ?? 'us-east-1';
    const env = process.env.NODE_ENV ?? 'development';

    const credentialsProvider =
      env === 'development' || env === 'test'
        ? fromEnv()
        : fromNodeProviderChain();

    this.s3 = new S3Client({
      region,

      ...(credentialsProvider && { credentials: credentialsProvider }),
    });
  }

  async upload(
    buffer: Buffer,
    contentType: string,
    path: string,
  ): Promise<void> {
    const bucket = process.env.AWS_S3_BUCKET;
    if (!bucket) throw new Error('Bucket S3 não definido');

    await this.s3.send(
      new PutObjectCommand({
        Bucket: bucket,
        Key: path,
        Body: buffer,
        ContentType: contentType,
        ServerSideEncryption: 'aws:kms',
      }),
    );
  }

  async getFileStream(path: string): Promise<{
    stream: Readable;
    contentType: string;
    fileName: string;
  }> {
    const bucket = process.env.AWS_S3_BUCKET;
    if (!bucket) throw new Error('Bucket S3 não definido');

    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: path,
    });

    const response = await this.s3.send(command);

    if (!response.Body || !(response.Body instanceof Readable)) {
      throw new Error('Erro ao obter stream do arquivo no S3');
    }

    return {
      stream: response.Body,
      contentType: response.ContentType || 'application/octet-stream',
      fileName: basename(path),
    };
  }

  async getFileUrl(path: string, expiresIn = 3600): Promise<string> {
    const bucket = process.env.AWS_S3_BUCKET;
    if (!bucket) throw new Error('Bucket S3 não definido');

    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: path,
    });

    return await getSignedUrl(this.s3, command, { expiresIn });
  }

  async getDownloadUrl(path: string, fileName?: string, expiresIn = 3600): Promise<string> {
    const bucket = process.env.AWS_S3_BUCKET;
    if (!bucket) throw new Error('Bucket S3 não definido');

    const finalFileName = fileName || basename(path);

    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: path,
      ResponseContentDisposition: `attachment; filename="${finalFileName}"`,
      ResponseContentType: 'application/octet-stream',
    });

    return await getSignedUrl(this.s3, command, { expiresIn });
  }

  async delete(path: string): Promise<void> {
    const bucket = process.env.AWS_S3_BUCKET;
    if (!bucket) throw new Error('Bucket S3 não definido');

    await this.s3.send(new DeleteObjectCommand({ Bucket: bucket, Key: path }));
  }
}
