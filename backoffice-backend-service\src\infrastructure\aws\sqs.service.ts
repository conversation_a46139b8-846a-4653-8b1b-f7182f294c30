import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  SQSClient,
  SendMessageCommand,
  ReceiveMessageCommand,
  DeleteMessageCommand,
  Message,
} from '@aws-sdk/client-sqs';

interface SqsMessage<T = Record<string, unknown>> {
  id?: string;
  receiptHandle?: string;
  body: T;
}

@Injectable()
export class SqsService {
  private readonly client: SQSClient;
  private readonly logger = new Logger(SqsService.name);
  private readonly queueUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.client = new SQSClient({
      region: this.configService.get<string>('AWS_REGION') || 'us-east-1',
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID') || '',
        secretAccessKey:
          this.configService.get<string>('AWS_SECRET_ACCESS_KEY') || '',
      },
    });
    this.queueUrl = this.configService.get<string>('AWS_SQS_QUEUE_URL') || '';
  }

  async sendMessage<T>(message: T, delaySeconds = 0): Promise<void> {
    try {
      const command = new SendMessageCommand({
        QueueUrl: this.queueUrl,
        MessageBody: JSON.stringify(message),
        DelaySeconds: delaySeconds,
      });

      const response = await this.client.send(command);
      this.logger.log(
        `Mensagem enviada para SQS com ID: ${response.MessageId}`,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Erro ao enviar mensagem para SQS: ${error.message}`);
      } else {
        this.logger.error(
          'Erro ao enviar mensagem para SQS: erro desconhecido',
        );
      }
      throw error;
    }
  }

  async receiveMessages<T = Record<string, unknown>>(
    maxMessages = 10,
  ): Promise<SqsMessage<T>[]> {
    try {
      const command = new ReceiveMessageCommand({
        QueueUrl: this.queueUrl,
        MaxNumberOfMessages: maxMessages,
        WaitTimeSeconds: 20, // Polling longo
      });

      const response = await this.client.send(command);

      if (!response.Messages || response.Messages.length === 0) {
        return [];
      }

      this.logger.log(`Recebidas ${response.Messages.length} mensagens do SQS`);

      return response.Messages.map((message: Message) => ({
        id: message.MessageId,
        receiptHandle: message.ReceiptHandle,
        body: message.Body ? (JSON.parse(message.Body) as T) : ({} as T),
      }));
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Erro ao receber mensagens do SQS: ${error.message}`);
      } else {
        this.logger.error(
          'Erro ao receber mensagens do SQS: erro desconhecido',
        );
      }
      throw error;
    }
  }

  async deleteMessage(receiptHandle: string): Promise<void> {
    try {
      const command = new DeleteMessageCommand({
        QueueUrl: this.queueUrl,
        ReceiptHandle: receiptHandle,
      });

      await this.client.send(command);
      this.logger.log('Mensagem excluída do SQS');
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Erro ao excluir mensagem do SQS: ${error.message}`);
      } else {
        this.logger.error('Erro ao excluir mensagem do SQS: erro desconhecido');
      }
      throw error;
    }
  }
}
