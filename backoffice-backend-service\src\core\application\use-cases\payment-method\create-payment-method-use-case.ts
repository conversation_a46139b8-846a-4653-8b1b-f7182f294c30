import { Injectable, Inject } from '@nestjs/common';
import { PaymentMethodRepository } from '../../../ports/repositories/payment-method-repository.interface';
import { PaymentMethod } from '../../../domain/payment-method';
import { v4 as uuidv4 } from 'uuid';

export interface CreatePaymentMethodInput {
  label: string;
  description: string;
  createdBy: string;
}

export interface PaymentMethodOutput {
  id: string;
  label: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

export interface CreatePaymentMethodOutput {
  paymentMethod: PaymentMethodOutput;
}

@Injectable()
export class CreatePaymentMethodUseCase {
  constructor(
    @Inject('PaymentMethodRepository')
    private readonly paymentMethodRepository: PaymentMethodRepository,
  ) {}

  async execute(
    input: CreatePaymentMethodInput,
  ): Promise<CreatePaymentMethodOutput> {
    const existingPaymentMethod =
      await this.paymentMethodRepository.findByLabel(input.label);

    if (existingPaymentMethod) {
      throw new Error(
        `Já existe uma forma de pagamento cadastrado com o label ${input.label}. Por favor, verifique os dados e tente novamente.`,
      );
    }

    const paymentMethod = new PaymentMethod(
      undefined,
      uuidv4(),
      input.label,
      input.description,
      input.createdBy,
      new Date(), // createdAts
      new Date(), // updatedAt
      input.createdBy, // createdBy
    );

    const createdPaymentMethod =
      await this.paymentMethodRepository.create(paymentMethod);

    return {
      paymentMethod: createdPaymentMethod.toJSON() as PaymentMethodOutput,
    };
  }
}
