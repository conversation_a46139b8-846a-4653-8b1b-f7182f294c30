import { Test, TestingModule } from '@nestjs/testing';
import { PaymentMethodController } from './payment.method.controller';
import { CreatePaymentMethodUseCase } from '../../core/application/use-cases/payment-method/create-payment-method-use-case';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ConfigService } from '@nestjs/config';
import { ListPaymentMethodUseCase } from '../../core/application/use-cases/payment-method/list-payment-methods-use-case';
import { PaymentMethod } from '../../core/domain/payment-method';
import { DeletePaymentMethodUseCase } from '../../core/application/use-cases/payment-method/delete-payment-methods-use-case';
import { UpdatePaymentMethodUseCase } from '../../core/application/use-cases/payment-method/update-payment-method-use-case';

describe('PaymentMethodController', () => {
  let controller: PaymentMethodController;
  let createPaymentMethodUseCase: jest.Mocked<CreatePaymentMethodUseCase>;
  let listPaymentMethodUseCase: jest.Mocked<ListPaymentMethodUseCase>;
  let updatePaymentMethodUseCase: jest.Mocked<UpdatePaymentMethodUseCase>;
  let deletePaymentMethodUseCase: jest.Mocked<DeletePaymentMethodUseCase>;

  beforeEach(async () => {
    createPaymentMethodUseCase = {
      execute: jest.fn(),
    } as unknown as jest.Mocked<CreatePaymentMethodUseCase>;

    listPaymentMethodUseCase = {
      execute: jest.fn(),
    } as unknown as jest.Mocked<ListPaymentMethodUseCase>;

    updatePaymentMethodUseCase = {
      execute: jest.fn(),
    } as unknown as jest.Mocked<UpdatePaymentMethodUseCase>;

    deletePaymentMethodUseCase = {
      execute: jest.fn(),
    } as unknown as jest.Mocked<DeletePaymentMethodUseCase>;

    const module: TestingModule = await Test.createTestingModule({
      controllers: [PaymentMethodController],
      providers: [
        {
          provide: CreatePaymentMethodUseCase,
          useValue: createPaymentMethodUseCase,
        },
        {
          provide: ListPaymentMethodUseCase,
          useValue: listPaymentMethodUseCase,
        },
        {
          provide: UpdatePaymentMethodUseCase,
          useValue: updatePaymentMethodUseCase,
        },
        {
          provide: DeletePaymentMethodUseCase,
          useValue: deletePaymentMethodUseCase,
        },
        {
          provide: JwtAuthGuard,
          useValue: { canActivate: jest.fn().mockReturnValue(true) },
        },
        {
          provide: ConfigService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<PaymentMethodController>(PaymentMethodController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a PaymentMethod successfully', async () => {
    const createPaymentMethodDto = {
      label: 'Credit Card',
      description: 'Visa and Mastercard',
    };

    const mockPaymentMethod = {
      id: '550e8400-e29b-41d4-a716-************',
      label: createPaymentMethodDto.label,
      description: createPaymentMethodDto.description,
      createdBy: 'admin-user',
      createdAt: new Date(),
      updatedAt: new Date(),
      updatedBy: 'admin-user',
    };

    const executeSpy = jest.spyOn(createPaymentMethodUseCase, 'execute');
    executeSpy.mockResolvedValue({ paymentMethod: mockPaymentMethod });

    const result = await controller.createPaymentMethod(
      createPaymentMethodDto,
      'admin-user',
    );

    expect(result.paymentMethod).toEqual(mockPaymentMethod);
    expect(executeSpy).toHaveBeenCalledWith({
      ...createPaymentMethodDto,
      createdBy: 'admin-user',
    });
  });

  it('should list PaymentMethods successfully', async () => {
    const mockPaymentMethods = [
      {
        id: 1,
        uuid: '550e8400-e29b-41d4-a716-************',
        label: 'Credit Card',
        description: 'Visa and Mastercard',
        createdBy: 'admin-user',
        createdAt: new Date(),
        updatedAt: new Date(),
        updatedBy: 'admin-user',
      } as PaymentMethod,
    ];

    const executeSpy = jest.spyOn(listPaymentMethodUseCase, 'execute');
    executeSpy.mockResolvedValue({
      items: mockPaymentMethods,
      total: 1,
    });

    const result = await controller.listPaymentMethods({
      limit: 10,
      offset: 0,
    });

    expect(result).toEqual({
      items: mockPaymentMethods,
      total: 1,
    });
    expect(executeSpy).toHaveBeenCalledWith({
      limit: 10,
      offset: 0,
    });
  });

  it('should update PaymentMethod sucessfully', async () => {
    const updatePaymentMethodDto = {
      label: 'Updated Label',
      description: 'Updated Description',
    };

    const mockPaymentMethod = {
      id: 1,
      uuid: '550e8400-e29b-41d4-a716-************',
      label: updatePaymentMethodDto.label,
      description: updatePaymentMethodDto.description,
      createdBy: 'admin-user',
      createdAt: new Date(),
      updatedAt: new Date(),
      updatedBy: 'admin-user',
    } as PaymentMethod;

    const executeSpy = jest.spyOn(updatePaymentMethodUseCase, 'execute');
    executeSpy.mockResolvedValue(mockPaymentMethod);

    const result = await controller.updatePaymentMethodPatch(
      mockPaymentMethod.uuid,
      updatePaymentMethodDto,
      'admin-user',
    );

    expect(result).toEqual(mockPaymentMethod);
    expect(executeSpy).toHaveBeenCalledWith({
      uuid: mockPaymentMethod.uuid,
      paymentMethod: {
        ...updatePaymentMethodDto,
      },
      updatedBy: 'admin-user',
    });
  });

  it('should delete PaymentMethod successfully', async () => {
    const mockUuid = '550e8400-e29b-41d4-a716-************';

    const executeSpy = jest.spyOn(deletePaymentMethodUseCase, 'execute');
    executeSpy.mockResolvedValue();

    await controller.deletePaymentMethod(mockUuid);

    expect(executeSpy).toHaveBeenCalledWith({
      uuid: mockUuid,
    });
  });
});
