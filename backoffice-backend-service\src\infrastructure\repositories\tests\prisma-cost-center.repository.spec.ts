import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '@infrastructure/prisma/prisma.service';
import { PrismaCostCenterRepository } from '../prisma-cost-center.repository';
import { CostCenter } from '@core/domain/cost-center/entities/cost-center.entity';
import { v4 as uuidv4 } from 'uuid';

// Interface para o mock do PrismaService
interface MockPrismaService {
  costCenter: {
    create: jest.Mock;
    findUnique: jest.Mock;
    findMany: jest.Mock;
    update: jest.Mock;
    delete: jest.Mock;
    count: jest.Mock;
  };
  $transaction: jest.Mock;
}

describe('PrismaCostCenterRepository', () => {
  let repository: PrismaCostCenterRepository;
  // Usamos o tipo MockPrismaService para o mock
  let prismaService: MockPrismaService;

  beforeEach(async () => {
    // Mock do PrismaService com tipagem adequada
    const mockPrismaService: MockPrismaService = {
      costCenter: {
        create: jest.fn(),
        findUnique: jest.fn(),
        findMany: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        count: jest.fn(),
      },
      $transaction: jest
        .fn()
        .mockImplementation(<T>(callback: () => T): T => callback()),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PrismaCostCenterRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<PrismaCostCenterRepository>(
      PrismaCostCenterRepository,
    );
    prismaService = module.get(PrismaService);
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('create', () => {
    it('should create a cost center', async () => {
      // Arrange
      const uuid = uuidv4();
      const description = 'Test Cost Center';
      const createdBy = uuidv4();
      const updatedBy = createdBy;
      const createdAt = new Date();
      const updatedAt = new Date();

      const costCenter = new CostCenter({
        id: uuid,
        description,
        createdBy,
        updatedBy,
        createdAt,
        updatedAt,
      });

      const prismaCostCenter = {
        id: 1,
        uuid,
        description,
        createdBy,
        updatedBy,
        createdAt,
        updatedAt,
      };

      // Usando o mock diretamente
      prismaService.costCenter.create.mockResolvedValue(prismaCostCenter);

      // Act
      const result = await repository.create(costCenter);

      // Assert
      expect(prismaService.costCenter.create).toHaveBeenCalledWith({
        data: {
          uuid: costCenter.id,
          description: costCenter.description,
          createdBy: costCenter.createdBy,
          updatedBy: costCenter.updatedBy,
        },
      });

      expect(result).toBeInstanceOf(CostCenter);
      expect(result.id).toBe(uuid);
      expect(result.description).toBe(description);
      expect(result.createdBy).toBe(createdBy);
      expect(result.updatedBy).toBe(updatedBy);
    });
  });

  describe('findByUuid', () => {
    it('should find a cost center by uuid', async () => {
      // Arrange
      const uuid = uuidv4();
      const prismaCostCenter = {
        id: 1,
        uuid,
        description: 'Test Cost Center',
        createdBy: uuidv4(),
        updatedBy: uuidv4(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Usando o mock diretamente
      prismaService.costCenter.findUnique.mockResolvedValue(prismaCostCenter);

      // Act
      const result = await repository.findByUuid(uuid);

      // Assert
      expect(prismaService.costCenter.findUnique).toHaveBeenCalledWith({
        where: { uuid },
      });

      // Verificamos que o resultado não é nulo antes de acessar suas propriedades
      expect(result).not.toBeNull();
      if (result) {
        expect(result).toBeInstanceOf(CostCenter);
        expect(result.id).toBe(uuid);
        expect(result.description).toBe(prismaCostCenter.description);
      }
    });

    it('should return null if cost center not found', async () => {
      // Arrange
      const uuid = uuidv4();
      // Usando o mock diretamente
      prismaService.costCenter.findUnique.mockResolvedValue(null);

      // Act
      const result = await repository.findByUuid(uuid);

      // Assert
      expect(prismaService.costCenter.findUnique).toHaveBeenCalledWith({
        where: { uuid },
      });
      // Sabemos que o resultado é null porque mockamos assim
      expect(result).toBeNull();
    });
  });

  describe('findWithPagination', () => {
    it('should find cost centers with pagination', async () => {
      // Arrange
      const limit = 10;
      const offset = 0;
      const description = 'Test';

      const prismaCostCenters = [
        {
          id: 1,
          uuid: uuidv4(),
          description: 'Test Cost Center 1',
          createdBy: uuidv4(),
          updatedBy: uuidv4(),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 2,
          uuid: uuidv4(),
          description: 'Test Cost Center 2',
          createdBy: uuidv4(),
          updatedBy: uuidv4(),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      // Usando o mock diretamente
      prismaService.costCenter.findMany.mockResolvedValue(prismaCostCenters);
      prismaService.costCenter.count.mockResolvedValue(2);

      // Act
      const result = await repository.findWithPagination({
        limit,
        offset,
        description,
      });

      // Assert
      expect(prismaService.costCenter.findMany).toHaveBeenCalledWith({
        where: {
          description: {
            contains: description,
            mode: 'insensitive',
          },
          deletedAt: null,
        },
        skip: offset,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      });

      expect(prismaService.costCenter.count).toHaveBeenCalledWith({
        where: {
          description: {
            contains: description,
            mode: 'insensitive',
          },
          deletedAt: null,
        },
      });

      expect(result.items).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.items[0]).toBeInstanceOf(CostCenter);
      expect(result.items[1]).toBeInstanceOf(CostCenter);
    });
  });
});
