/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */
import { VersioningType } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

// Define types for our mocks
interface MockDocumentBuilder {
  setTitle: jest.MockedFunction<(title: string) => MockDocumentBuilder>;
  setDescription: jest.MockedFunction<
    (description: string) => MockDocumentBuilder
  >;
  setVersion: jest.MockedFunction<(version: string) => MockDocumentBuilder>;
  build: jest.MockedFunction<() => any>;
}

interface MockSwaggerDocument {
  openapi: string;
  info: { title: string };
}

interface MockApp {
  enableVersioning: jest.MockedFunction<(options: any) => void>;
  getHttpAdapter: jest.MockedFunction<() => any>;
  listen: jest.MockedFunction<(port: number) => Promise<void>>;
}

// Mock all NestJS modules before any imports that might use them
jest.mock('@nestjs/core', () => ({
  NestFactory: {
    create: jest.fn(),
  },
}));

jest.mock('@nestjs/swagger', () => ({
  DocumentBuilder: jest.fn().mockImplementation(() => ({
    setTitle: jest.fn().mockReturnThis(),
    setDescription: jest.fn().mockReturnThis(),
    setVersion: jest.fn().mockReturnThis(),
    build: jest.fn().mockReturnValue({
      title: 'Backoffice Audit Service',
      description:
        'API for audit logging and management in the backoffice system',
      version: '1.0',
    }),
  })),
  SwaggerModule: {
    createDocument: jest.fn().mockReturnValue({
      openapi: '3.0.0',
      info: { title: 'Test API' },
    }),
    setup: jest.fn(),
  },
}));

// Mock the app module to prevent circular dependency issues
jest.mock('./app.module', () => ({
  AppModule: class MockAppModule {},
}));

describe('Main Configuration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear module cache to ensure fresh imports
    jest.resetModules();
  });

  afterEach(() => {
    delete process.env.PORT;
  });

  it('should configure DocumentBuilder with correct metadata', () => {
    const { DocumentBuilder: MockedDocumentBuilder } =
      jest.requireMock('@nestjs/swagger');

    // Simulate the configuration that happens in main.ts

    const builder =
      new MockedDocumentBuilder() as unknown as MockDocumentBuilder;

    const config = builder
      .setTitle('Backoffice Audit Service')
      .setDescription(
        'API for audit logging and management in the backoffice system',
      )
      .setVersion('1.0')
      .build();

    // Assert
    expect(MockedDocumentBuilder).toHaveBeenCalled();
    expect(builder.setTitle).toHaveBeenCalledWith('Backoffice Audit Service');
    expect(builder.setDescription).toHaveBeenCalledWith(
      'API for audit logging and management in the backoffice system',
    );
    expect(builder.setVersion).toHaveBeenCalledWith('1.0');
    expect(config).toEqual({
      title: 'Backoffice Audit Service',
      description:
        'API for audit logging and management in the backoffice system',
      version: '1.0',
    });
  });

  it('should have correct VersioningType configuration', () => {
    // Test that we're using the correct versioning type
    expect(VersioningType.URI).toBe(0); // Verify the enum value we're using
  });

  it('should handle PORT environment variable correctly', () => {
    // Test default port
    const defaultPort = process.env.PORT ?? 3000;
    expect(defaultPort).toBe(3000);

    // Test with PORT environment variable
    process.env.PORT = '8080';
    const envPort = process.env.PORT ?? 3000;
    expect(envPort).toBe('8080');
  });

  it('should create OpenAPI JSON endpoint callback function', () => {
    // Test the callback logic that would be used in the /api-json endpoint
    const mockDocument: MockSwaggerDocument = {
      openapi: '3.0.0',
      info: { title: 'Backoffice Audit Service' },
    };

    const mockReq = {};
    const mockRes = {
      json: jest.fn(),
    };

    // Simulate the callback function logic from main.ts
    const apiJsonCallback = (
      req: unknown,
      res: { json: (doc: MockSwaggerDocument) => void },
    ) => {
      res.json(mockDocument);
    };

    // Execute the callback
    apiJsonCallback(mockReq, mockRes);

    // Assert
    expect(mockRes.json).toHaveBeenCalledWith(mockDocument);
  });

  it('should have SwaggerModule.setup call with correct parameters', () => {
    const {
      SwaggerModule: MockedSwaggerModule,
      DocumentBuilder: MockedDocumentBuilder,
    } = jest.requireMock('@nestjs/swagger');

    // Simulate the Swagger setup logic
    const mockApp: MockApp = {
      enableVersioning: jest.fn(),
      getHttpAdapter: jest.fn(),
      listen: jest.fn(),
    };

    const builder =
      new MockedDocumentBuilder() as unknown as MockDocumentBuilder;

    const config = builder
      .setTitle('Backoffice Audit Service')
      .setDescription(
        'API for audit logging and management in the backoffice system',
      )
      .setVersion('1.0')
      .build();

    const document = MockedSwaggerModule.createDocument(mockApp, config);

    MockedSwaggerModule.setup('api', mockApp, document);

    // Assert

    expect(MockedSwaggerModule.createDocument).toHaveBeenCalledWith(
      mockApp,
      expect.any(Object),
    );

    expect(MockedSwaggerModule.setup).toHaveBeenCalledWith(
      'api',
      mockApp,
      expect.any(Object),
    );
  });
});

describe('Main Bootstrap Function Structure', () => {
  it('should verify bootstrap function exists and is async', () => {
    // This test verifies that main.ts contains the expected structure
    // without actually executing it to avoid side effects

    const mainFileContent = fs.readFileSync(
      path.join(__dirname, 'main.ts'),
      'utf8',
    );

    // Verify key components are present in the file
    expect(mainFileContent).toContain('async function bootstrap()');
    expect(mainFileContent).toContain('NestFactory.create(AppModule)');
    expect(mainFileContent).toContain('app.enableVersioning');
    expect(mainFileContent).toContain('DocumentBuilder');
    expect(mainFileContent).toContain('SwaggerModule.setup');
    expect(mainFileContent).toContain('app.listen');
    expect(mainFileContent).toContain('void bootstrap()');
  });

  it('should verify correct imports are present', () => {
    const mainFileContent = fs.readFileSync(
      path.join(__dirname, 'main.ts'),
      'utf8',
    );

    // Verify required imports
    expect(mainFileContent).toContain(
      "import { NestFactory } from '@nestjs/core'",
    );
    expect(mainFileContent).toContain(
      "import { VersioningType } from '@nestjs/common'",
    );
    expect(mainFileContent).toContain(
      "import { AppModule } from './app.module'",
    );
    expect(mainFileContent).toContain(
      "import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'",
    );
  });
});
