import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { SectorRepositoryPort } from '@/core/ports/repositories/sector-repository.port';
import { UpdateSectorUseCase } from './update-sector.use-case';
import { Sector } from '@/core/domain/entities/sector.entity';
import { SECTOR_REPOSITORY } from '@/core/constants/injection-tokens';

describe('UpdateSectorUseCase', () => {
  let useCase: UpdateSectorUseCase;
  let repository: jest.Mocked<SectorRepositoryPort>;

  const mockSector = Sector.create(
    1,
    'test-uuid',
    'test-code',
    'test-description',
    'test-created-by',
    'test-updated-by',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SectorRepositoryPort> = {
      findByUuid: jest.fn(),
      update: jest.fn(),
      findByCode: jest.fn(),
      save: jest.fn(),
      findById: jest.fn(),
      delete: jest.fn(),
      list: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateSectorUseCase,
        {
          provide: SECTOR_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<UpdateSectorUseCase>(UpdateSectorUseCase);
    repository =
      module.get<jest.Mocked<SectorRepositoryPort>>(SECTOR_REPOSITORY);
  });

  describe('execute', () => {
    it('should update sector when user is authorized', async () => {
      // Arrange
      repository.findByUuid.mockResolvedValue(mockSector);
      repository.update.mockImplementation((sector) => Promise.resolve(sector));

      // Act
      const result = await useCase.execute('test-uuid', {
        code: 'New Code',
        description: 'New Description',
        updatedBy: 'test-updated-by',
      });

      // Assert
      expect(result.code).toBe('New Code');
      expect(result.description).toBe('New Description');
      expect(repository.update.mock.calls.length).toBeGreaterThan(0);
    });

    it('should throw NotFoundException when sector does not exist', async () => {
      // Arrange
      repository.findByUuid.mockResolvedValue(null);

      // Act & Assert
      await expect(
        useCase.execute('non-existent-id', {
          code: 'New Code',
          description: 'New Description',
          updatedBy: 'test-updated-by',
        }),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
