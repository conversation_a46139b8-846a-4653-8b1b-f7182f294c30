import { AggregateRoot } from '../shared/aggregate-root';

export enum CompanyStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export interface CompanyAddress {
  street: string;
  city: string;
  zipCode: string;
  state: string;
}

export class Company extends AggregateRoot {
  readonly id: number;
  readonly uuid: string;
  private _razaoSocial: string;
  private _cnpj: string;
  private _address: CompanyAddress;
  private _phone: string;
  private _email: string;
  private _status: CompanyStatus;
  readonly createdAt: Date;
  private _updatedAt: Date;
  readonly createdBy: string;
  private _updatedBy: string;

  constructor(
    id: number,
    uuid: string,
    razaoSocial: string,
    cnpj: string,
    address: CompanyAddress,
    phone: string,
    email: string,
    status: CompanyStatus,
    createdBy: string,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
    updatedBy: string = createdBy,
  ) {
    super();
    this.id = id;
    this.uuid = uuid;
    this._razaoSocial = razaoSocial;
    this._cnpj = cnpj;
    this._address = address;
    this._phone = phone;
    this._email = email;
    this._status = status;
    this.createdAt = createdAt;
    this._updatedAt = updatedAt;
    this.createdBy = createdBy;
    this._updatedBy = updatedBy;
  }

  get razaoSocial(): string {
    return this._razaoSocial;
  }

  get cnpj(): string {
    return this._cnpj;
  }

  get address(): Record<string, string> {
    return { ...this._address };
  }

  get phone(): string {
    return this._phone;
  }

  get email(): string {
    return this._email;
  }

  get status(): CompanyStatus {
    return this._status;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  get updatedBy(): string {
    return this._updatedBy;
  }

  updateRazaoSocial(razaoSocial: string, updatedBy: string): void {
    this._razaoSocial = razaoSocial;
    this._updatedAt = new Date();
    this._updatedBy = updatedBy;
  }

  updateAddress(address: CompanyAddress, updatedBy: string): void {
    this._address = address;
    this._updatedAt = new Date();
    this._updatedBy = updatedBy;
  }

  updatePhone(phone: string, updatedBy: string): void {
    this._phone = phone;
    this._updatedAt = new Date();
    this._updatedBy = updatedBy;
  }

  updateEmail(email: string, updatedBy: string): void {
    this._email = email;
    this._updatedAt = new Date();
    this._updatedBy = updatedBy;
  }

  updateStatus(status: CompanyStatus, updatedBy: string): void {
    this._status = status;
    this._updatedAt = new Date();
    this._updatedBy = updatedBy;
  }

  toJSON() {
    return {
      id: this.id,
      uuid: this.uuid,
      razaoSocial: this._razaoSocial,
      cnpj: this._cnpj,
      address: this._address,
      phone: this._phone,
      email: this._email,
      status: this._status,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this._updatedAt,
      updatedBy: this._updatedBy,
    };
  }
}
