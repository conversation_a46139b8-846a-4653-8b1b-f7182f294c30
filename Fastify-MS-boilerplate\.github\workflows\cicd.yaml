name: build-deploy

on:
  pull_request:
    types: [opened, synchronize, labeled, unlabeled]

env:
  NAME: fastify-ms-boilerplate
  ACCOUNTID_HML: ************
  ACCOUNTID_PRD: ************
  AWS_REGION: us-east-2
  EKS_CLUSTER_NAME_HML: hml
  EKS_CLUSTER_NAME_PRD: prd

jobs:
  homologation:
    if: ${{ github.event.pull_request.labels && contains(join(github.event.pull_request.labels.*.name, ','), 'hml') }}
    runs-on: ubuntu-latest
    steps:
      - name: Set short git commit SHA
        id: commit
        uses: prompt/actions-commit-hash@v2

      - name: Checkout the repository
        uses: actions/checkout@v3

      - name: Install kubectl
        uses: azure/setup-kubectl@v2.0
        with:
          version: "v1.24.0" # default is latest stable
        id: install

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_CI }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_CI }}
          aws-region: us-east-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
        with:
          registries: "************"

      - name: Build Docker image
        id: build-image
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ env.NAME }}
          IMAGE_TAG: ${{ steps.commit.outputs.short }}
        run: |
          docker build -t $REGISTRY/$REPOSITORY:$IMAGE_TAG --target development .
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

      - name: Configure Kubernetes client
        uses: silverlyra/setup-aws-eks@v0.1.1
        with:
          cluster: ${{ env.EKS_CLUSTER_NAME_HML }}

      - name: Install Helm
        run: |
          curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

      - name: Deploy to EKS
        env:
          IMAGE_TAG: ${{ steps.commit.outputs.short }}
          NAME: ${{ env.NAME }}
        run: |
          cd helm/hml/petrus-${{ env.NAME }}-hml/
          sed -i.bak "s|latest|${IMAGE_TAG}|g" values.yaml
          helm upgrade petrus-${{ env.NAME }}-hml . -f values.yaml -n petrus-hml  -i --force
