import { DomainEvent } from './domain-event';
import { Company } from '../entities/company.entity';

export class CompanyCreatedEvent extends DomainEvent {
  constructor(private readonly company: Company) {
    super('company.created');
  }

  getEventName(): string {
    return 'company.created';
  }

  getData(): object {
    return {
      companyId: this.company.id,
      companyUuid: this.company.uuid,
      companyCnpj: this.company.cnpj,
      companyName: this.company.razaoSocial,
      companyAddress: this.company.address,
      companyPhone: this.company.phone,
      companyEmail: this.company.email,
      companyStatus: this.company.status,
      createdAt: this.company.createdAt.toISOString(),
    };
  }
}
