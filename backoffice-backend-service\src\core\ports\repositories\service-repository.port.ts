import { Service } from '../../domain/service/entities/service.entity';
import { EntityType } from '../../domain/service/enums/entity-type.enum';

export interface ServiceRepositoryPort {
  create(service: Service): Promise<Service>;
  findById(id: string): Promise<Service | null>;
  findByEntityUuid(entityUuid: string, entityType: EntityType): Promise<Service[]>;
  findAll(): Promise<Service[]>;
  update(service: Service): Promise<Service>;
  delete(id: string): Promise<void>;
  findWithPagination(params: {
    limit: number;
    offset: number;
    entityUuid?: string;
    entityType?: EntityType;
    type?: string;
  }): Promise<{ items: Service[]; total: number }>;
} 