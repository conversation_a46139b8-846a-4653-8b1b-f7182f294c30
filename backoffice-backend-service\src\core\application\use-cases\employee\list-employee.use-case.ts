import { Injectable, Inject } from '@nestjs/common';
import {
  EMPLOYEE_REPOSITORY,
  ListEmployeeResult,
} from '@/core/ports/repositories/employee-repository.port';
import { EmployeeRepositoryPort } from '@/core/ports/repositories/employee-repository.port';

export interface ListEmployeeInput {
  limit: number;
  offset: number;
  name?: string;
  email?: string;
}

@Injectable()
export class ListEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,
  ) {}

  async execute(params: ListEmployeeInput): Promise<ListEmployeeResult> {
    return this.employeeRepository.findWithPagination(params);
  }
}
