// Global API types
export interface ApiResponse<T = unknown> {
  success: boolean;
  message: string;
  data?: T;
}

export interface PaginatedApiResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ErrorResponse {
  success: false;
  message: string;
  errors?: Record<string, string>;
}

// Database types
export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Common filters
export interface BaseFilters {
  search?: string;
  createdAt?: {
    from?: string;
    to?: string;
  };
  updatedAt?: {
    from?: string;
    to?: string;
  };
}

// HTTP Status Codes
export type HttpStatusCode =
  | 200 // OK
  | 201 // Created
  | 204 // No Content
  | 400 // Bad Request
  | 401 // Unauthorized
  | 403 // Forbidden
  | 404 // Not Found
  | 422 // Unprocessable Entity
  | 500; // Internal Server Error

// Common error codes
export type CommonErrorCode =
  | 'VALIDATION_ERROR'
  | 'UNAUTHORIZED'
  | 'FORBIDDEN'
  | 'NOT_FOUND'
  | 'INTERNAL_ERROR'
  | 'DUPLICATE_ENTRY'
  | 'INVALID_INPUT';

// JWT types
export interface JwtPayload {
  userId: number;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

// Environment types
export type NodeEnv = 'development' | 'production' | 'test';

// Database connection types
export interface DatabaseConfig {
  url: string;
  authToken?: string;
}

// Request context types
export interface RequestContext {
  requestId: string;
  userId?: number;
  userRole?: string;
  startTime: number;
}

// Logging types
export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, unknown>;
}

// Utility types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type StringKeys<T> = Extract<keyof T, string>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type OmitBy<T, K extends keyof T> = Omit<T, K>;

// Entity base types
export interface BaseEntity {
  id: number;
  createdAt: string;
  updatedAt: string;
}

export interface SoftDeleteEntity extends BaseEntity {
  deletedAt: string | null;
  isDeleted: boolean;
}

// Audit types
export interface AuditFields {
  createdBy?: number;
  updatedBy?: number;
  version?: number;
}

export interface AuditEntity extends BaseEntity, AuditFields {}

// API versioning
export type ApiVersion = 'v1' | 'v2';

// Rate limiting types
export interface RateLimitConfig {
  max: number;
  timeWindow: string;
}

// CORS types
export interface CorsConfig {
  origin: string[] | boolean;
  credentials: boolean;
}
