import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Length, MinLength } from 'class-validator';

export class LoginDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email do usuário',
  })
  @IsEmail({}, { message: 'Nome de Usuão inválido' })
  @IsNotEmpty({ message: 'Username é obrigatório' })
  username: string;

  @ApiProperty({
    example: 'Senha123!',
    description: '<PERSON>ha do usuário (mínimo 6 caracteres)',
  })
  @IsString({ message: 'Senha deve ser uma string' })
  @IsNotEmpty({ message: 'Senha é obrigatória' })
  @MinLength(6, { message: 'Senha deve ter no mínimo 6 caracteres' })
  password: string;
}


export class LoginOtpDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email do usuário',
  })
  @IsEmail({}, { message: 'Email do usuário é inválido' })
  @IsNotEmpty({ message: 'Email é obrigatório' })
  email: string;
}

export class VerifyOtpDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email do usuário',
  })
  @IsEmail({}, { message: 'Email do usuário é inválido' })
  @IsNotEmpty({ message: 'Email é obrigatório' })
  email: string;

  @ApiProperty({
    example: '123456',
    description: 'Código OTP enviado para o usuário',
  })
  @IsString({ message: 'Código OTP deve ser uma string' })
  @IsNotEmpty({ message: 'Código OTP é obrigatório' })
  @Length(6, 6)
  code: string;

  @ApiProperty({
    example: 'Senha123!',
    description: 'Senha do usuário (mínimo 6 caracteres)',
  })
  @IsString({ message: 'Senha deve ser uma string' })
  @IsNotEmpty({ message: 'Senha é obrigatória' })
  @MinLength(6, { message: 'Senha deve ter no mínimo 6 caracteres' })
  password: string;
}
