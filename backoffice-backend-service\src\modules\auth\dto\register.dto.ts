import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  Min<PERSON>ength,
  IsEnum,
  IsOptional,
} from 'class-validator';
import { Role } from '../../../core/domain/role.enum';

export class RegisterDto {
  @ApiProperty({
    example: 'Usuario Exemplo',
    description: 'Nome completo do usuário',
  })
  @IsString({ message: 'Nome deve ser uma string' })
  @IsNotEmpty({ message: 'Nome é obrigatório' })
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email do usuário',
  })
  @IsEmail({}, { message: 'Email inválido' })
  @IsNotEmpty({ message: 'Email é obrigatório' })
  email: string;

  @ApiProperty({
    example: 'Senha123!',
    description: 'Senha do usuário (mínimo 6 caracteres)',
  })
  @IsString({ message: '<PERSON>ha deve ser uma string' })
  @IsNotEmpty({ message: 'Senha é obrigatória' })
  @MinLength(6, { message: 'Senha deve ter no mínimo 6 caracteres' })
  password: string;

  @ApiProperty({
    example: 'USER',
    description: 'Role do usuário (opcional)',
    enum: Role,
    required: false,
  })
  @IsOptional()
  @IsEnum(Role, { message: 'Role inválida' })
  role?: Role;
}
