import { ARCHIVE_REPOSITORY, ArchiveRepositoryPort } from "@/core/ports/repositories/archive-repository.port";
import { IStorageProvider } from "@/core/ports/storage/storage-provider.port";
import { Inject, Injectable, NotFoundException } from "@nestjs/common";

@Injectable()
export class DeleteEmployeeArchiveUseCase {
  constructor(
    @Inject(ARCHIVE_REPOSITORY)
    private readonly archiveRepository: ArchiveRepositoryPort,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) { }

  async execute(id: string): Promise<void> {
    const archive = await this.archiveRepository.findOne(id);
    if (!archive) {
      throw new NotFoundException('Arquivo não encontrado.');
    }
    await this.archiveRepository.delete(id);
  }
}