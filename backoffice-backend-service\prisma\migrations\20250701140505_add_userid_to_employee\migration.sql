/*
  Warnings:

  - A unique constraint covering the columns `[user_id]` on the table `employees` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "core"."employees" ADD COLUMN     "user_id" VARCHAR(50);

-- CreateIndex
CREATE UNIQUE INDEX "employees_user_id_key" ON "core"."employees"("user_id");

-- AddForeignKey
ALTER TABLE "core"."employees" ADD CONSTRAINT "employees_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "core"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
