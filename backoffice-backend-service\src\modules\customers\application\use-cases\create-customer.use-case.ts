import { Injectable, Inject, forwardRef, ConflictException, NotFoundException } from '@nestjs/common';
import {
  Customer,
  CustomerStatus,
} from '../../domain/entities/customer.entity';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { CreateCustomerDto } from '../../infrastructure/dtos/customer-create.dto';
import { v4 as uuidv4 } from 'uuid';
import { UsersService } from '@/modules/users/users.service';
import { Role } from '@/core/domain/role.enum';
import { AuthService } from '@/modules/auth/auth.service';
import { DuplicateEmailError } from '@/infrastructure/exceptions/duplicate-email.error';
import { DuplicateCnpjError } from '@/infrastructure/exceptions/duplicate-cnpj.error';

export interface CreateCustomerInput {
  customer: CreateCustomerDto;
  createdBy: string;
}

@Injectable()
export class CreateCustomerUseCase {
  constructor(
    @Inject('ICustomerRepository')
    private readonly customerRepository: ICustomerRepository,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
  ) {}

  async execute(input: CreateCustomerInput): Promise<Customer> {
    // Verificar se já existe um cliente com o mesmo CNPJ
    const existingCustomerByCnpj = await this.customerRepository.findByDocument(
      input.customer.cnpj,
    );

    if (existingCustomerByCnpj) {
      throw new DuplicateCnpjError(input.customer.cnpj, 'cliente');
    }

    const existingCustomerByEmail = await this.customerRepository.findByEmail(
      input.customer.email,
    );

    if (existingCustomerByEmail) {
      throw new DuplicateEmailError(input.customer.email, 'cliente');
    }

    const user = await this.usersService.findByEmail(input.customer.email).catch(error => {
      if (error instanceof NotFoundException) {
      console.info(`User with email ${input.customer.email} not found, creating new user.`);
      return null;
      }
      throw error; // Re-throw unexpected errors
    });
    
    if (user) {
      throw new DuplicateEmailError(input.customer.email, 'usuário');
    }

    // Criar o usuario
    const userCreated = await this.usersService.create({
      name: input.customer.razaoSocial,
      email: input.customer.email,
      password: 'sut@t6@LuhKX29*C', // Senha temporária, deve ser alterada pelo usuário
      role: Role.CUSTOMER_VIEWER, // Role específica para clientes
    });

    const customer = {
      uuid: uuidv4(), // Generate a new UUID
      razaoSocial: input.customer.razaoSocial,
      cnpj: input.customer.cnpj,
      email: input.customer.email,
      phone: input.customer.phone || undefined,
      address: { ...input.customer.address },
      image: input.customer.image || undefined,
      status: CustomerStatus.PENDING, // Default status
      userId: userCreated.id,
      url: input.customer.url || '', // Default URL if not provided
      createdBy: input.createdBy,
      createdAt: new Date(),
      updatedAt: new Date(),
      updatedBy: input.createdBy,
    } as Customer;

    const createdCustomer = await this.customerRepository.create(customer);

    // Envia email de confirmação de cadastro
    await this.authService.forgotPassword({
      email: userCreated.email,
    })

    return createdCustomer;
  }
}
