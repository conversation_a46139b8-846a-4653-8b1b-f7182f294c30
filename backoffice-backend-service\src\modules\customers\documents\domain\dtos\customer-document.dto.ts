import { IsString, IsNotEmpty, IsUUID, IsOptional, IsArray, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CustomerDocument } from '../entities/customer-document.entity';

export class UploadCustomerDocumentDto {
  @ApiProperty({
    description: 'Nome do documento',
    example: 'RG',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class CustomerDocumentMetadataDto {
  @ApiProperty({
    description: 'Nome do responsável pelo documento',
    example: '<PERSON>'
  })
  @IsString()
  responsible: string;

  @ApiProperty({
    description: 'Departamento responsável pelo documento',
    example: 'Financeiro'
  })
  @IsString()
  department: string;

  @ApiPropertyOptional({
    description: 'Descrição adicional do documento',
    example: 'Contrato de prestação de serviços'
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Data de expiração do documento (YYYY-MM-DD)',
    example: '2024-12-31'
  })
  @IsString()
  @IsOptional()
  expirationDate?: string;
}

export class CreateCustomerDocumentsDto {
  @ApiProperty({
    description: 'Lista de metadados dos documentos',
    type: [CustomerDocumentMetadataDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomerDocumentMetadataDto)
  documents: CustomerDocumentMetadataDto[];
}

export class CustomerDocumentApiBodyDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'binary' },
    description: "Arquivos dos documentos a serem enviados. Deve haver um arquivo para cada objeto na string JSON 'documentsMetadata'."
  })
  files: any[];

  @ApiProperty({
    description: "Uma string JSON contendo um array de metadados para cada documento. O número de objetos neste array deve corresponder ao número de arquivos enviados em 'files'.",
    example: '[{"responsible": "João Silva", "department": "Financeiro", "description": "Documento principal", "expirationDate": "2025-12-31"}]'
  })
  @IsString()
  documentsMetadata: string;
}

export class CustomerDocumentTextFormDataDto {
  @ApiProperty({
    description: "Uma string JSON contendo um array de metadados para cada documento. O número de objetos neste array deve corresponder ao número de arquivos enviados em 'files'.",
    example: '[{"responsible": "João Silva", "department": "Financeiro", "description": "Documento principal", "expirationDate": "2025-12-31"}]'
  })
  @IsString()
  documentsMetadata: string;
}

export class CreateCustomerDocumentDto {
  @ApiProperty({
    description: 'UUID do cliente',
    example: '',
  })
  @IsUUID()
  @IsNotEmpty()
  customerUuid: string;

  @ApiProperty({
    description: 'Nome do documento',
    example: 'RG',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'URL do documento em base64',
    example: 'data:application/pdf;base64,JVBERi0xLjcKJeLjz9MKN...',
  })
  @IsString()
  @IsNotEmpty()
  url: string;
}

export class GetCustomerDocumentDto {
  @ApiProperty({
    description: 'UUID do documento',
    example: '',
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;
}

export class ListCustomerDocumentsDto {
  @ApiProperty({
    description: 'UUID do cliente',
    example: '',
  })
  @IsUUID()
  @IsNotEmpty()
  customerUuid: string;
}

export class UpdateCustomerDocumentDto {
  @ApiPropertyOptional({
    description: 'Nome do documento',
    example: 'RG',
  })
  @IsString()
  @IsOptional()
  name?: string;
}

export class DeleteCustomerDocumentDto {
  @ApiProperty({
    description: 'UUID do documento',
    example: '',
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;
}

export class CustomerDocumentResponseDto {
  @ApiProperty({
    description: 'ID do documento',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID do cliente',
    example: '123e4567-e89b-12d3-a456-************',
  })
  customerUuid: string;

  @ApiProperty({
    description: 'Nome do documento',
    example: 'RG',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Nome do responsável pelo documento',
    example: 'João Silva',
  })
  responsible?: string;

  @ApiPropertyOptional({
    description: 'Departamento responsável pelo documento',
    example: 'Financeiro',
  })
  department?: string;

  @ApiPropertyOptional({
    description: 'Descrição adicional do documento',
    example: 'Documento de identificação',
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Data de expiração do documento',
    example: '2025-12-31',
  })
  expirationDate?: string;

  @ApiPropertyOptional({
    description: 'Nome do arquivo do documento',
    example: 'documento.pdf',
  })
  fileName?: string;

  @ApiPropertyOptional({
    description: 'URL para download do documento',
    example: 'https://s3.amazonaws.com/bucket/path/to/file?signature=...',
  })
  downloadUrl?: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2024-03-20T10:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data da última atualização',
    example: '2024-03-20T10:00:00Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Data de exclusão',
    example: '2024-03-20T10:00:00Z',
  })
  deletedAt?: Date;

  static fromEntity(entity: CustomerDocument, fileName?: string, downloadUrl?: string): CustomerDocumentResponseDto {
    const dto = new CustomerDocumentResponseDto();
    dto.id = entity.id;
    dto.customerUuid = entity.customerUuid;
    dto.name = entity.name;
    dto.responsible = entity.responsible;
    dto.department = entity.department;
    dto.description = entity.description;
    dto.expirationDate = entity.expirationDate;
    dto.fileName = fileName || entity.fileName;
    dto.downloadUrl = downloadUrl;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    dto.deletedAt = entity.deletedAt;
    return dto;
  }
}

export class CustomerDocumentListItemDto {
  @ApiProperty({
    description: 'ID do documento',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID do cliente',
    example: '123e4567-e89b-12d3-a456-************',
  })
  customerUuid: string;

  @ApiProperty({
    description: 'Nome do documento',
    example: 'RG',
  })
  name: string;

  @ApiProperty({
    description: 'Nome do arquivo do documento',
    example: 'documento.pdf',
  })
  fileName: string;

  @ApiProperty({
    description: 'URL para download do documento',
    example: 'https://s3.amazonaws.com/bucket/path/to/file?signature=...',
    required: false,
  })
  downloadUrl?: string;

  @ApiPropertyOptional({
    description: 'Nome do responsável pelo documento',
    example: 'João Silva',
  })
  responsible?: string;

  @ApiPropertyOptional({
    description: 'Departamento responsável pelo documento',
    example: 'Financeiro',
  })
  department?: string;

  @ApiPropertyOptional({
    description: 'Descrição adicional do documento',
    example: 'Documento de identificação',
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Data de expiração do documento',
    example: '2025-12-31',
  })
  expirationDate?: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2024-03-20T10:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data da última atualização',
    example: '2024-03-20T10:00:00Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Data de exclusão',
    example: '2024-03-20T10:00:00Z',
  })
  deletedAt?: Date;

  static fromEntity(entity: CustomerDocument, fileName?: string, downloadUrl?: string): CustomerDocumentListItemDto {
    const dto = new CustomerDocumentListItemDto();
    dto.id = entity.id;
    dto.customerUuid = entity.customerUuid;
    dto.name = entity.name;
    dto.fileName = fileName || entity.fileName || `${entity.name}.pdf`;
    dto.downloadUrl = downloadUrl; // URL de download do S3
    dto.responsible = entity.responsible;
    dto.department = entity.department;
    dto.description = entity.description;
    dto.expirationDate = entity.expirationDate;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    dto.deletedAt = entity.deletedAt;
    return dto;
  }
}
