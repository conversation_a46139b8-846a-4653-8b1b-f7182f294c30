import { PrismaClient } from '@prisma/client';
import * as dotenv from 'dotenv';

dotenv.config({ path: '.env.test' });

const prisma = new PrismaClient();

// Setup global beforeAll
beforeAll(async () => {
  // Clean database before tests
  await cleanupDatabase();
});

// Setup global afterAll
afterAll(async () => {
  // Clean database after tests
  await cleanupDatabase();
  // Disconnect Prisma
  await prisma.$disconnect();
});

// Helper function to clean database
async function cleanupDatabase() {
  const models = ['task', 'user', 'product', 'category'];

  // Disable foreign key checks for PostgreSQL
  await prisma.$executeRawUnsafe('SET session_replication_role = "replica";');

  for (const model of models) {
    try {
      await prisma.$executeRawUnsafe(`TRUNCATE TABLE "${model}" CASCADE;`);
    } catch (error) {
      console.log(`Error cleaning ${model} table:`, error);
    }
  }

  // Re-enable foreign key checks
  await prisma.$executeRawUnsafe('SET session_replication_role = "origin";');
}
