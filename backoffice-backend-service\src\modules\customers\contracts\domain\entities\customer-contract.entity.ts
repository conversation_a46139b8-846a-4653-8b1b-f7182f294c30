import { ContractStatus } from "@prisma/client";

export interface CustomerContract {
  uuid: string;
  customerUuid: string;
  name: string;
  url: string;
  isSigned: boolean;
  downloadUrl?: string | null;
  fileName?: string | null;
  contractIdentifier?: string;
  contractType?: string;
  expirationDate?: string | null;
  uploadedBy?: string;
  createdAt: Date;
  updatedAt: Date;
  status: ContractStatus;
} 