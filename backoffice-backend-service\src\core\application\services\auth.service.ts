import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import {
  AuthenticateUserUseCase,
  AuthenticateUserOutput,
} from '../use-cases/auth/authenticate-user.use-case';
import { RegisterUserUseCase } from '../use-cases/auth/register-user.use-case';
import { UserRepository } from '../../ports/repositories/user-repository.interface';

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private authenticateUserUseCase: AuthenticateUserUseCase,
    private registerUserUseCase: RegisterUserUseCase,
    private userRepository: UserRepository,
  ) {}

  // Métodos que delegam para os casos de uso

  async validateUser(
    email: string,
    password: string,
  ): Promise<AuthenticateUserOutput | null> {
    return this.authenticateUserUseCase.execute({ email, password });
  }

  async register(
    name: string,
    email: string,
    password: string,
  ): Promise<{
    success: boolean;
    message: string;
    user?: AuthenticateUserOutput;
  }> {
    const result = await this.registerUserUseCase.execute({
      name,
      email,
      password,
    });
    return {
      success: true,
      message: 'Usuário registrado com sucesso',
      user: result.user,
    };
  }
}
