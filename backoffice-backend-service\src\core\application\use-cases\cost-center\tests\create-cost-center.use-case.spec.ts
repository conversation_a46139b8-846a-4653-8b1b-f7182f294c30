import { Test, TestingModule } from '@nestjs/testing';
import { CreateCostCenterUseCase } from '../create-cost-center.use-case';
import { COST_CENTER_REPOSITORY } from '@core/ports/repositories/cost-center-repository.port';
import { CostCenter } from '../../../../domain/cost-center/entities/cost-center.entity';
import { CreateCostCenterDto } from '@modules/finance/cost-center/dto/create-cost-center.dto';
import { v4 as uuidv4 } from 'uuid';

type MockCostCenterRepository = {
  create: jest.Mock;
};

describe('CreateCostCenterUseCase', () => {
  let useCase: CreateCostCenterUseCase;
  let costCenterRepository: MockCostCenterRepository;

  beforeEach(async () => {
    const mockCostCenterRepository: MockCostCenterRepository = {
      create: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateCostCenterUseCase,
        {
          provide: COST_CENTER_REPOSITORY,
          useValue: mockCostCenterRepository,
        },
      ],
    }).compile();

    useCase = module.get<CreateCostCenterUseCase>(CreateCostCenterUseCase);
    costCenterRepository = module.get(COST_CENTER_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should create a cost center successfully', async () => {
      // Arrange
      const dto: CreateCostCenterDto = {
        description: 'Test Cost Center',
        createdBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        updatedBy: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
      };

      const expectedCostCenter = new CostCenter({
        id: '11111111-**************-************',
        description: dto.description,
        createdBy: dto.createdBy,
        updatedBy: dto.updatedBy,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Mock da função uuidv4 para retornar um valor fixo
      jest.mock('uuid', () => ({
        v4: jest.fn().mockReturnValue('11111111-**************-************'),
      }));
      costCenterRepository.create.mockResolvedValue(expectedCostCenter);

      // Act
      const result = await useCase.execute(dto);

      // Assert
      expect(costCenterRepository.create).toHaveBeenCalled();
      expect(result).toBeInstanceOf(CostCenter);
      expect(result.description).toBe(dto.description);
      expect(result.createdBy).toBe(dto.createdBy);
      expect(result.updatedBy).toBe(dto.updatedBy);
    });

    it('should throw an error if repository throws', async () => {
      // Arrange
      const dto: CreateCostCenterDto = {
        description: 'Test Cost Center',
        createdBy: uuidv4(),
        updatedBy: uuidv4(),
      };

      const error = new Error('Repository error');
      void costCenterRepository.create.mockRejectedValue(error);

      // Act & Assert
      await expect(useCase.execute(dto)).rejects.toThrow(error);
    });
  });
});
