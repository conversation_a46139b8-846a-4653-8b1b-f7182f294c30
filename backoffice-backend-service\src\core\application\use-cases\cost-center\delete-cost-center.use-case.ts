import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import {
  CostCenterRepositoryPort,
  COST_CENTER_REPOSITORY,
} from '@core/ports/repositories/cost-center-repository.port';

@Injectable()
export class DeleteCostCenterUseCase {
  constructor(
    @Inject(COST_CENTER_REPOSITORY)
    private readonly costCenterRepository: CostCenterRepositoryPort,
  ) {}

  async execute(uuid: string): Promise<void> {
    const costCenter = await this.costCenterRepository.findByUuid(uuid);

    if (!costCenter) {
      throw new NotFoundException('Centro de custo não encontrado.');
    }

    await this.costCenterRepository.delete(uuid);
  }
}
