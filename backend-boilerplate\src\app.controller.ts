import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('Health Check')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Exibe uma mensagem de boas-vindas' })
  @ApiResponse({
    status: 200,
    description: 'Retorna uma mensagem de boas-vindas',
  })
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  @ApiOperation({ summary: 'Verifica a saúde da aplicação' })
  @ApiResponse({ status: 200, description: 'A aplicação está saudável' })
  @ApiResponse({ status: 503, description: 'A aplicação não está saudável' })
  healthCheck() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: {
        api: 'up',
        database: 'up',
      },
    };
  }
}
