apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.name }}-config
  labels:
    app: {{ .Values.name }}
    chart: "{{.Chart.Name}}-{{.Chart.Version}}"
    buildTimestamp: "{{ now | unixEpoch }}"
  annotations:
    reloader.stakater.com/auto: "true"
data:
{{- range $name, $value := .Values.environment }}
{{- if not (empty $value) }}
  {{ $name | quote }}: {{ $value | quote }}
{{- end }}
{{- end }}