version: '3.8'

services:
  # Aplicação principal
  api:
    image: node:20-alpine
    container_name: nest_boilerplate_api
    command: >
      sh -c "cd /app && 
             apk add --no-cache --virtual .build-deps make gcc g++ python3 && 
             npm install && 
             apk del .build-deps && 
             npm run start:dev"
    volumes:
      - .:/app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/nest_boilerplate?schema=public
      - RABBITMQ_HOST=rabbitmq
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://jaeger:4318
      - JWT_SECRET=seu_jwt_secret
      - JWT_EXPIRATION=3600
    depends_on:
      - postgres
      - rabbitmq
      - jaeger
    networks:
      - nest_network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    container_name: nest_boilerplate_postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: nest_boilerplate
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - nest_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  rabbitmq:
    image: rabbitmq:3-management
    container_name: nest_boilerplate_rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped
    networks:
      - nest_network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # OpenTelemetry e Observabilidade
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: nest_boilerplate_jaeger
    ports:
      - "16686:16686"  # UI
      - "14250:14250"  # Modelo GRPC
      - "14268:14268"  # Modelo HTTP
      - "4318:4318"    # OTLP HTTP
    restart: unless-stopped
    networks:
      - nest_network

  prometheus:
    image: prom/prometheus:latest
    container_name: nest_boilerplate_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./local/prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped
    networks:
      - nest_network
    depends_on:
      - api

  grafana:
    image: grafana/grafana:latest
    container_name: nest_boilerplate_grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - nest_network
    depends_on:
      - prometheus

  # Ferramenta para gestão de banco de dados
  pgadmin:
    image: dpage/pgadmin4
    container_name: nest_boilerplate_pgadmin
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - nest_network
    depends_on:
      - postgres

volumes:
  postgres_data:
  rabbitmq_data:
  grafana_data:
  pgadmin_data:

networks:
  nest_network:
    driver: bridge 