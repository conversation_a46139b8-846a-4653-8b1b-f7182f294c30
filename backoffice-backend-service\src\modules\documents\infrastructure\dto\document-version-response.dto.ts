import { DocumentVersion } from '../../domain/entities/document-version.entity';

export class DocumentVersionResponseDto {
  versionId: number;
  uploadedAt: Date;
  uploadedBy: string;
  expirationDate?: Date;
  filePath: string;
  createdAt: Date;

  static fromEntity(entity: DocumentVersion): DocumentVersionResponseDto {
    return {
      versionId: entity.versionId,
      uploadedAt: entity.uploadedAt,
      uploadedBy: entity.uploadedBy,
      expirationDate: entity.expirationDate,
      filePath: entity.filePath,
      createdAt: entity.createdAt,
    };
  }
}
