import {
  describe,
  beforeAll,
  afterAll,
  beforeEach,
  afterEach,
  it,
  expect,
} from '@jest/globals';
import { db } from '@/shared/database/connection';
import { users } from '../../entites/user.entity';
import { eq } from 'drizzle-orm';
import * as userService from '../../services/user.service';

describe('User Service Integration Tests', () => {
  let createdUserIds: number[] = [];

  beforeAll(async () => {});

  afterAll(async () => {});

  beforeEach(async () => {
    createdUserIds = [];
  });

  afterEach(async () => {
    if (createdUserIds.length > 0) {
      try {
        for (const id of createdUserIds) {
          await db.delete(users).where(eq(users.id, id));
        }
        console.log(`Limpeza: ${createdUserIds.length} usuários removidos`);
      } catch (error) {
        console.warn('Erro ao limpar dados de teste:', error);
      }
    }
  });

  describe('User Service', () => {
    it('should create and cleanup user', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123',
      };

      const createdUser = await userService.create(userData);
      expect(createdUser).toBeDefined();
      expect(createdUser.id).toBeDefined();

      createdUserIds.push(createdUser.id);

      const foundUser = await userService.getById(createdUser.id);
      expect(foundUser).toBeDefined();
      expect(foundUser.email).toBe(userData.email);
    });

    it('should handle multiple users creation and cleanup', async () => {
      const usersData = [
        {
          name: 'Test User 1',
          email: '<EMAIL>',
          username: 'testuser1',
          password: 'password123',
        },
        {
          name: 'Test User 2',
          email: '<EMAIL>',
          username: 'testuser2',
          password: 'password123',
        },
      ];

      for (const userData of usersData) {
        const createdUser = await userService.create(userData);
        createdUserIds.push(createdUser.id);
      }

      expect(createdUserIds).toHaveLength(2);

      const user1 = await userService.getById(createdUserIds[0]);
      const user2 = await userService.getById(createdUserIds[1]);

      expect(user1.email).toBe(usersData[0].email);
      expect(user2.email).toBe(usersData[1].email);
    });
  });
});
