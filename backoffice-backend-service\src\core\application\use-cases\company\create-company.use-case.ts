import { Injectable, Inject } from '@nestjs/common';
import { CompanyRepository } from '../../../ports/repositories/company-repository.interface';
import { Company, CompanyStatus } from '../../../domain/company.entity';
import { v4 as uuidv4 } from 'uuid';
import { COMPANY_REPOSITORY } from '../../../constants/injection-tokens';

export interface CreateCompanyInput {
  razaoSocial: string;
  cnpj: string;
  address: {
    street: string;
    city: string;
    zipCode: string;
    state: string;
  };
  phone: string;
  email: string;
  status: CompanyStatus;
  createdBy: string;
}

export interface CompanyOutput {
  id: number;
  uuid: string;
  razaoSocial: string;
  cnpj: string;
  address: {
    street: string;
    city: string;
    zipCode: string;
    state: string;
  };
  phone: string;
  email: string;
  status: CompanyStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

export interface CreateCompanyOutput {
  company: CompanyOutput;
}

@Injectable()
export class CreateCompanyUseCase {
  constructor(
    @Inject(COMPANY_REPOSITORY)
    private readonly companyRepository: CompanyRepository,
  ) {}

  async execute(input: CreateCompanyInput): Promise<CreateCompanyOutput> {
    const existingCompany = await this.companyRepository.findByCnpj(input.cnpj);

    if (existingCompany) {
      throw new Error(
        `Já existe uma empresa cadastrada com o CNPJ ${input.cnpj}. Por favor, verifique os dados e tente novamente.`,
      );
    }

    const company = new Company(
      0, // ID will be set by the database
      uuidv4(),
      input.razaoSocial,
      input.cnpj,
      input.address,
      input.phone,
      input.email,
      input.status,
      input.createdBy,
    );

    const createdCompany = await this.companyRepository.create(company);

    return {
      company: createdCompany.toJSON() as CompanyOutput,
    };
  }
}
