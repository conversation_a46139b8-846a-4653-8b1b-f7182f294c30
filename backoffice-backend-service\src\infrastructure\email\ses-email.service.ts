import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EmailService } from './nodemailer-email.service.interface';
import { SESService } from '../aws/ses/ses.service';

@Injectable()
export class SESEmailService implements EmailService {
  private readonly frontendUrl: string;

  constructor(
    private readonly sesService: SESService,
    private readonly configService: ConfigService,
  ) {
    this.frontendUrl = this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000');
  }

  async sendPasswordResetEmail(email: string, resetToken: string): Promise<void> {
    const resetUrl = `${this.frontendUrl}/auth/reset-password`;
    
    try {
      await this.sesService.sendPasswordResetEmail(email, resetToken);
    } catch (error) {
      console.error('Erro ao enviar email de reset de senha via SES:', error);
      throw error;
    }
  }

  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    try {
      await this.sesService.sendWelcomeEmail(email, name);
    } catch (error) {
      console.error('Erro ao enviar email de boas-vindas via SES:', error);
      throw error;
    }
  }

  async sendSecurityNotificationEmailWithToken(
    email: string, 
    token: string
  ): Promise<void> {
    const subject = 'Seu Código de Verificação';
    
    const html = `
      <div style="font-family: Arial, sans-serif; text-align: center; padding: 40px;">
        <h1 style="color: #333; margin-bottom: 30px;">Código de Verificação</h1>
        
        <div style="background-color: #f8f9fa; border: 2px solid #007bff; border-radius: 10px; padding: 30px; margin: 20px 0; display: inline-block;">
          <h2 style="color: #007bff; font-size: 36px; margin: 0; letter-spacing: 3px;">${token}</h2>
        </div>
        
        <p style="color: #666; margin-top: 20px;">Use este código na tela da aplicação</p>
      </div>
    `;

    const text = `Seu código de verificação é: ${token}`;

    try {
      await this.sesService.sendEmail({
        to: email,
        subject,
        html,
        text,
      });
    } catch (error) {
      console.error('Erro ao enviar código de verificação via SES:', error);
      throw error;
    }
  }
} 