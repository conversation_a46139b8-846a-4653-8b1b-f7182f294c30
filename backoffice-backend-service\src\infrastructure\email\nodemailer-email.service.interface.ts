export interface EmailService {
  sendPasswordResetEmail(email: string, resetToken: string): Promise<void>;
  sendWelcomeEmail?(email: string, name: string): Promise<void>;
  sendSecurityNotificationEmail?(
    email: string, 
    action: string, 
    ipAddress?: string,
    userAgent?: string
  ): Promise<void>;
  sendSecurityNotificationEmailWithToken?(
    email: string, 
    token: string
  ): Promise<void>;
}
