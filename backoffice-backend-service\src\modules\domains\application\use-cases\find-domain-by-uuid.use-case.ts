import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { IFindDomainByUuidUseCase } from '../../domain/use-cases/find-domain-by-uuid.use-case.interface';
import { Domain } from '../../domain/entities/domain.entity';
import { IDomainRepository } from '../../domain/repositories/domain.repository.interface';

@Injectable()
export class FindDomainByUuidUseCase implements IFindDomainByUuidUseCase {
  constructor(
    @Inject('IDomainRepository')
    private readonly domainRepository: IDomainRepository,
  ) {}

  async execute(uuid: string): Promise<Domain> {
    const domain = await this.domainRepository.findByUuid(uuid);
    if (!domain) {
      throw new NotFoundException(`Domain with UUID ${uuid} not found`);
    }
    return domain;
  }
} 