import { v4 as uuid } from 'uuid';

/**
 * Classe base para todos os eventos de domínio
 */
export abstract class DomainEvent {
  /**
   * Identificador único do evento
   */
  readonly eventId: string;

  /**
   * Tipo/nome do evento
   */
  readonly eventName: string;

  /**
   * Timestamp de quando o evento ocorreu
   */
  readonly occurredAt: Date;

  /**
   * Identificador de correlação para rastreamento de fluxos de eventos
   */
  readonly correlationId?: string;

  /**
   * Identificador do usuário que causou o evento (se aplicável)
   */
  readonly userId?: string;

  /**
   * Construtor do evento de domínio
   *
   * @param eventName Nome/tipo do evento
   * @param metadata Metadados adicionais (correlationId, userId, etc)
   */
  constructor(
    eventName: string,
    metadata?: {
      correlationId?: string;
      userId?: string;
      occurredAt?: Date;
      eventId?: string;
    },
  ) {
    this.eventId = metadata?.eventId || uuid();
    this.eventName = eventName;
    this.occurredAt = metadata?.occurredAt || new Date();
    this.correlationId = metadata?.correlationId;
    this.userId = metadata?.userId;
  }

  /**
   * Serializa o evento para JSON, incluindo metadados e dados do evento
   */
  serialize(): object {
    interface SerializedEvent {
      eventId: string;
      eventName: string;
      occurredAt: string;
      data: object;
      correlationId?: string;
      userId?: string;
    }

    const serialized: SerializedEvent = {
      eventId: this.eventId,
      eventName: this.eventName,
      occurredAt: this.occurredAt.toISOString(),
      data: this.getData(),
    };

    if (this.correlationId) {
      serialized.correlationId = this.correlationId;
    }

    if (this.userId) {
      serialized.userId = this.userId;
    }

    return serialized;
  }

  /**
   * Método abstrato para fornecer os dados específicos do evento
   * Deve ser implementado por cada classe de evento
   */
  abstract getData(): object;
}
