import { Modu<PERSON> } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { TasksController } from './tasks.controller';
import { PrismaModule } from '../../infrastructure/prisma/prisma.module';
import { PrismaTaskRepository } from '../../infrastructure/repositories/prisma-task.repository';

@Module({
  imports: [PrismaModule],
  providers: [
    TasksService,
    {
      provide: 'TaskRepository',
      useClass: PrismaTaskRepository,
    },
  ],
  controllers: [TasksController],
  exports: [TasksService],
})
export class TasksModule {}
