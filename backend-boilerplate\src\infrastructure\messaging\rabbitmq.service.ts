import {
  Injectable,
  Lo<PERSON>,
  On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as amqp from 'amqplib';

@Injectable()
export class RabbitMQService implements OnModuleInit, OnModuleDestroy {
  private connection: amqp.ChannelModel | null = null;
  private channel: amqp.Channel | null = null;
  private readonly logger = new Logger(RabbitMQService.name);

  constructor(private configService: ConfigService) {}

  async onModuleInit(): Promise<void> {
    try {
      await this.connect();
    } catch (error) {
      this.logError('Falha ao conectar ao RabbitMQ', error);
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      await this.close();
    } catch (error) {
      this.logError('Falha ao desconectar do RabbitMQ', error);
    }
  }

  private async connect(): Promise<void> {
    try {
      const host = this.configService.get<string>('RABBITMQ_HOST');
      const port = this.configService.get<number>('RABBITMQ_PORT');
      const username = this.configService.get<string>('RABBITMQ_USERNAME');
      const password = this.configService.get<string>('RABBITMQ_PASSWORD');

      const url = `amqp://${username}:${password}@${host}:${port}`;

      this.connection = await amqp.connect(url);
      if (this.connection) {
        this.channel = await this.connection.createChannel();
      }
      this.logger.log('Conectado ao RabbitMQ com sucesso');
    } catch (error) {
      this.logError('Erro ao conectar ao RabbitMQ', error);
      throw new Error('Falha ao conectar ao RabbitMQ');
    }
  }

  private async close(): Promise<void> {
    try {
      if (this.channel) {
        await this.channel.close();
      }
      if (this.connection) {
        await this.connection.close();
      }
      this.logger.log('Conexão com RabbitMQ fechada com sucesso');
    } catch (error) {
      this.logError('Erro ao fechar conexão com RabbitMQ', error);
    }
  }

  private logError(message: string, error: unknown): void {
    let errorDetails: string;

    if (error instanceof Error) {
      errorDetails = error.message;
      if (error.stack) {
        this.logger.debug(error.stack);
      }
    } else if (error !== null && error !== undefined) {
      try {
        if (typeof error === 'object') {
          errorDetails = JSON.stringify(error) || 'objeto vazio';
        } else {
          errorDetails = String(error);
        }
      } catch {
        errorDetails = 'erro não serializável';
      }
    } else {
      errorDetails = 'erro desconhecido';
    }

    this.logger.error(`${message}: ${errorDetails}`);
  }

  async publish<T>(
    exchange: string,
    routingKey: string,
    content: T,
  ): Promise<void> {
    try {
      if (!this.channel) {
        await this.connect();
      }

      if (!this.channel) {
        throw new Error('Não foi possível estabelecer conexão com o RabbitMQ');
      }

      await this.channel.assertExchange(exchange, 'direct', { durable: true });

      this.channel.publish(
        exchange,
        routingKey,
        Buffer.from(JSON.stringify(content)),
        { persistent: true },
      );

      this.logger.log(`Mensagem publicada: ${routingKey}`);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Falha ao publicar mensagem: ${error.message}`);
      } else {
        this.logger.error('Falha ao publicar mensagem: erro desconhecido');
      }
      throw error;
    }
  }

  async subscribe<T>(
    queue: string,
    exchange: string,
    routingKey: string,
    callback: (message: T) => void,
  ): Promise<void> {
    try {
      if (!this.channel) {
        await this.connect();
      }

      if (!this.channel) {
        throw new Error('Não foi possível estabelecer conexão com o RabbitMQ');
      }

      await this.channel.assertExchange(exchange, 'direct', { durable: true });
      await this.channel.assertQueue(queue, { durable: true });
      await this.channel.bindQueue(queue, exchange, routingKey);

      void this.channel.consume(queue, (message) => {
        if (message) {
          try {
            const content = JSON.parse(message.content.toString()) as T;
            callback(content);
            if (this.channel) {
              this.channel.ack(message);
            }
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'erro desconhecido';
            this.logger.error(`Erro ao processar mensagem: ${errorMessage}`);
            if (this.channel) {
              // Rejeitar mensagem malformada
              this.channel.nack(message, false, false);
            }
          }
        }
      });

      this.logger.log(
        `Inscrito na fila: ${queue}, exchange: ${exchange}, routingKey: ${routingKey}`,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Falha ao se inscrever na fila: ${error.message}`);
      } else {
        this.logger.error('Falha ao se inscrever na fila: erro desconhecido');
      }
      throw error;
    }
  }
}
