import autocannon from 'autocannon';
const reporter = require('autocannon-reporter');
import path from 'path';
import fs from 'fs';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { env } from '../config/env';
import { users } from '@/modules/users/entites/user.entity';
import { eq, inArray } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import logger from '../logger/logger';

interface User {
  id: number;
  email: string;
  username: string;
  password: string;
}

interface AuthResponse {
  success: boolean;
  token: string;
  refreshToken: string;
  user: {
    id: number;
    email: string;
    username: string;
    role: string;
  };
}

class LoadTestRunner {
  private baseUrl: string;
  private testUser: User | null = null;
  private authToken: string | null = null;
  private createdUsers: number[] = [];
  private db: any;
  private pool: Pool;
  private usersToCreate: number = 10; // Número de usuários de teste a serem criados
  private testConnections: number = 100; // Número de conexões simultâneas para o teste
  private testPipelining: number = 1; // Número de requisições em pipeline  
  private testDuration: number = 10; // Duração do teste em segundos

  constructor(baseUrl = 'http://localhost:3000/api/') {
    this.baseUrl = baseUrl;

    this.pool = new Pool({
      connectionString: `postgresql://${env.DB_USER}:${env.DB_PASSWORD}@${env.DB_HOST}:${env.DB_PORT}/${env.DB_NAME}`,
    });
    this.db = drizzle(this.pool);

    this.usersToCreate = 10; // Número de usuários de teste a serem criados
  }

  /**
   * Cria um usuário admin diretamente no banco de dados
   */
  async createTestUser(): Promise<void> {
    const userData = {
      name: `Load Test Admin ${Date.now()}`,
      email: `loadtest_${Date.now()}@test.com`,
      username: `loadtest_${Date.now()}`,
      password: 'TestPassword123!',
    };

    try {
      logger.info('🔨 Criando usuário admin diretamente no banco...');

      const hashedPassword = await bcrypt.hash(
        userData.password,
        env.BCRYPT_ROUNDS
      );

      const [createdUser] = await this.db
        .insert(users)
        .values({
          name: userData.name,
          email: userData.email,
          username: userData.username,
          password: hashedPassword,
          role: 'admin',
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning({
          id: users.id,
          email: users.email,
          username: users.username,
        });

      this.testUser = {
        id: createdUser.id,
        email: userData.email,
        username: userData.username,
        password: userData.password,
      };

      logger.info(
        `✅ Usuário admin criado no banco: ${this.testUser.username}`
      );
    } catch (error: any) {
      console.error('❌ Erro ao criar usuário admin no banco:', error.message);
      throw error;
    }
  }

  async loginTestUser(): Promise<void> {
    if (!this.testUser) {
      throw new Error('Usuário de teste não foi criado');
    }

    try {
      logger.info('🔐 Fazendo login...');
      const response = await fetch(`${this.baseUrl}auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: this.testUser.email,
          password: this.testUser.password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(JSON.stringify(errorData));
      }

      const responseData: AuthResponse = await response.json();
      this.authToken = responseData.token;
      logger.info('✅ Login realizado com sucesso');
    } catch (error: any) {
      console.error('❌ Erro no login:', error.message);
      throw error;
    }
  }

  async createTestUsers(count = this.usersToCreate): Promise<void> {
    logger.info(`🔨 Criando ${count} usuários de teste...`);

    const promises = Array.from({ length: count }, async (_, index) => {
      const userData = {
        name: `Test User ${Date.now()}_${index}`,
        email: `user_${Date.now()}_${index}@test.com`,
        username: `user_${Date.now()}_${index}`,
        password: 'TestPassword123!',
        role: 'user',
      };

      try {
        const response = await fetch(`${this.baseUrl}users`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(userData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(JSON.stringify(errorData));
        }

        const responseData = await response.json();
        this.createdUsers.push(responseData.data.id);
        return responseData.data.id;
      } catch (error: any) {
        console.error(`❌ Erro ao criar usuário ${index}:`, error.message);
        throw error;
      }
    });

    await Promise.all(promises);
    logger.info(`✅ ${count} usuários criados com sucesso`);
  }

  async runLoadTest(): Promise<void> {
    if (!this.authToken) {
      throw new Error('Token de autenticação não encontrado');
    }

    logger.info('🚀 Iniciando teste de carga...');

    return new Promise((resolve, reject) => {
      const instance = autocannon(
        {
          url: `${this.baseUrl}users`,
          connections: this.testConnections,
          pipelining: this.testPipelining,
          duration: this.testDuration,
          headers: {
            Authorization: `Bearer ${this.authToken}`,
            'Content-Type': 'application/json',
          },
          method: 'GET',
        },
        (err: any, results: any) => {
          if (err) {
            console.error('❌ Erro no teste de carga:', err);
            reject(err);
          } else {
            logger.info('✅ Teste de carga concluído!');
            logger.info('\n📊 Resultados:');
            logger.info(`   • Requests/sec: ${results.requests.average}`);
            logger.info(`   • Latência média: ${results.latency.average}ms`);
            logger.info(`   • Total de requests: ${results.requests.total}`);
            logger.info(`   • Erros: ${results.errors}`);
            logger.info(`   • Timeouts: ${results.timeouts}`);
            logger.info(`   • Non-2xx responses: ${results.non2xx}`);

            // Gerar relatório HTML na raiz do projeto
            this.generateReport(results);

            resolve();
          }
        }
      );

      autocannon.track(instance, { renderProgressBar: true });
    });
  }

  async cleanupUsers(): Promise<void> {
    if (this.createdUsers.length === 0 && !this.testUser) {
      logger.info('📝 Nenhum usuário para limpar');
      return;
    }

    logger.info('🧹 Limpando usuários de teste diretamente do banco...');

    if (this.createdUsers.length > 0) {
      try {
        await this.db.delete(users).where(inArray(users.id, this.createdUsers));
        logger.info(
          `✅ ${this.createdUsers.length} usuários removidos do banco (hard delete)`
        );
      } catch (error: any) {
        console.warn('⚠️ Erro ao deletar usuários do banco:', error.message);

        logger.info('🔄 Tentando deletar usuários individualmente...');
        const deletePromises = this.createdUsers.map(async (userId) => {
          try {
            await this.db.delete(users).where(eq(users.id, userId));
          } catch (error: any) {
            console.warn(
              `⚠️ Erro ao deletar usuário ${userId} do banco:`,
              error.message
            );
          }
        });

        await Promise.allSettled(deletePromises);
        logger.info('✅ Cleanup individual concluído');
      }
    }

    if (this.testUser) {
      try {
        await this.db.delete(users).where(eq(users.id, this.testUser.id));
        logger.info('✅ Usuário admin removido do banco (hard delete)');
      } catch (error: any) {
        console.warn(
          '⚠️ Erro ao deletar usuário admin do banco:',
          error.message
        );
      }
    }
  }

  /**
   * Fecha a conexão com o banco de dados
   */
  async closeDatabase(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      logger.info('🔌 Conexão com banco fechada');
    }
  }

  /**
   * Gera relatório HTML usando autocannon-reporter
   */
  private generateReport(results: any): void {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

      // Criar diretório load-tests se não existir
      const reportsDir = path.join(process.cwd(), 'load-tests-output');
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }

      const reportPath = path.join(
        reportsDir,
        `load-test-report-${timestamp}.html`
      );

      logger.info('\n📊 Gerando relatório HTML...');

      // Primeiro gerar o HTML usando buildReport
      const htmlReport = reporter.buildReport(results);

      // Escrever o arquivo manualmente
      fs.writeFileSync(reportPath, htmlReport, 'utf8');

      logger.info(`✅ Relatório HTML gerado: ${reportPath}`);
      logger.info(
        `🌐 Abra o arquivo no navegador para ver os resultados detalhados`
      );
    } catch (error: any) {
      console.error('❌ Erro ao processar relatório:', error.message);

      // Fallback: gerar relatório simples em caso de erro
      this.generateSimpleReport(results);
    }
  }

  /**
   * Gera relatório simples em caso de falha do autocannon-reporter
   */
  private generateSimpleReport(results: any): void {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

      // Criar diretório load-tests se não existir
      const reportsDir = path.join(process.cwd(), 'load-tests-output');
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }

      const reportPath = path.join(
        reportsDir,
        `load-test-simple-report-${timestamp}.html`
      );

      const simpleHtml = `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Load Test Report - Simple</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { background: #4CAF50; color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .metric { background: #f5f5f5; padding: 15px; border-radius: 8px; text-align: center; }
        .metric h3 { margin: 0 0 10px 0; color: #333; }
        .metric .value { font-size: 24px; font-weight: bold; color: #4CAF50; }
        .details { background: white; border: 1px solid #ddd; padding: 20px; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f5f5f5; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Load Test Report</h1>
        <p>Gerado em: ${new Date().toLocaleString('pt-BR')}</p>
    </div>
    
    <div class="metrics">
        <div class="metric">
            <h3>Requests/sec</h3>
            <div class="value">${results.requests?.average || 'N/A'}</div>
        </div>
        <div class="metric">
            <h3>Latência Média</h3>
            <div class="value">${results.latency?.average || 'N/A'}ms</div>
        </div>
        <div class="metric">
            <h3>Total Requests</h3>
            <div class="value">${results.requests?.total || 'N/A'}</div>
        </div>
        <div class="metric">
            <h3>Erros</h3>
            <div class="value">${results.errors || 0}</div>
        </div>
    </div>
    
    <div class="details">
        <h2>Detalhes do Teste</h2>
        <table>
            <tr><th>Métrica</th><th>Valor</th></tr>
            <tr><td>URL Testada</td><td>${this.baseUrl}users</td></tr>
            <tr><td>Método</td><td>GET</td></tr>
            <tr><td>Conexões</td><td>100</td></tr>
            <tr><td>Duração</td><td>10 segundos</td></tr>
            <tr><td>Requests Mín/sec</td><td>${
              results.requests?.min || 'N/A'
            }</td></tr>
            <tr><td>Requests Máx/sec</td><td>${
              results.requests?.max || 'N/A'
            }</td></tr>
            <tr><td>Latência Mínima</td><td>${
              results.latency?.min || 'N/A'
            }ms</td></tr>
            <tr><td>Latência Máxima</td><td>${
              results.latency?.max || 'N/A'
            }ms</td></tr>
            <tr><td>Percentil 50</td><td>${
              results.latency?.p50 || 'N/A'
            }ms</td></tr>
            <tr><td>Percentil 90</td><td>${
              results.latency?.p90 || 'N/A'
            }ms</td></tr>
            <tr><td>Percentil 99</td><td>${
              results.latency?.p99 || 'N/A'
            }ms</td></tr>
            <tr><td>Timeouts</td><td>${results.timeouts || 0}</td></tr>
            <tr><td>Non-2xx Responses</td><td>${results.non2xx || 0}</td></tr>
        </table>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #666;">
        <p>Relatório gerado por Fastify Load Test Runner</p>
    </div>
</body>
</html>`;

      fs.writeFileSync(reportPath, simpleHtml, 'utf8');
      logger.info(`✅ Relatório simples gerado: ${reportPath}`);
    } catch (error: any) {
      console.error('❌ Erro ao gerar relatório simples:', error.message);
    }
  }

  /**
   * Executa o teste completo
   */
  async run(): Promise<void> {
    try {
      logger.info('🎯 Iniciando teste de carga completo...\n');

      await this.createTestUser();

      await this.loginTestUser();

      await this.createTestUsers(this.usersToCreate);

      await this.runLoadTest();
    } catch (error) {
      console.error('❌ Erro durante o teste:', error);
    } finally {
      await this.cleanupUsers();

      await this.closeDatabase();

      logger.info('\n🏁 Teste finalizado!');
    }
  }
}

async function main() {
  const loadTest = new LoadTestRunner('http://localhost:3000/api/');
  await loadTest.run();
}

if (require.main === module) {
  main().catch(console.error);
}

export { LoadTestRunner };
