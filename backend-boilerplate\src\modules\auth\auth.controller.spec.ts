/* eslint-disable @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */

import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { INestApplication } from '@nestjs/common';

import { createConfig } from '../../config.module';
import { AuthModule } from './auth.module';
import {
  setupTestDatabase,
  deleteTestDatabase,
} from '../../core/helpers/test-db';

describe('AuthController', () => {
  let app: INestApplication;
  let testSchema: string;

  beforeAll(async () => {
    testSchema = setupTestDatabase();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [createConfig(), AuthModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
    await deleteTestDatabase(testSchema);
  });

  describe('POST /auth/register', () => {
    const userDto = {
      name: '<PERSON>e',
      email: '<EMAIL>',
      password: '123456',
    };

    it('deveria registrar um novo usuário', async () => {
      const res = await request(app.getHttpServer())
        .post('/auth/register')
        .send(userDto)
        .expect(201);

      expect(res.body).toHaveProperty('user');
      expect(res.body.user).toMatchObject({
        name: userDto.name,
        email: userDto.email,
        role: 'USER',
      });
      expect(res.body).toHaveProperty('access_token');
    });

    it('deveria recusar registro duplicado com o mesmo email', async () => {
      const res = await request(app.getHttpServer())
        .post('/auth/register')
        .send(userDto)
        .expect(400);

      expect(res.body).toHaveProperty(
        'message',
        `Usuário com email ${userDto.email} já existe`,
      );
    });
  });

  describe('POST /auth/login', () => {
    it('deveria autenticar um usuário válido', async () => {
      const res = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: '123456',
        })
        .expect(201);

      expect(res.body).toHaveProperty('user');
      expect(res.body.user).toMatchObject({
        name: 'John Doe',
        email: '<EMAIL>',
      });
      expect(res.body).toHaveProperty('access_token');
    });

    it('deveria rejeitar login com senha inválida', async () => {
      const res = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'senha_errada',
        })
        .expect(401);

      expect(res.body).toHaveProperty('message', 'Credenciais inválidas');
    });
  });

  describe('GET /auth/validate', () => {
    it('deveria validar um token de acesso válido', async () => {
      const login = await request(app.getHttpServer())
        .post('/auth/login')
        .send({ email: '<EMAIL>', password: '123456' })
        .expect(201);

      const token: string = login.body.access_token;

      const res = await request(app.getHttpServer())
        .get('/auth/validate')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(res.body).toHaveProperty('message', 'Token válido');
      expect(res.body).toHaveProperty('user');
    });
  });
});
