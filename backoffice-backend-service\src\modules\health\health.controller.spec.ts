import { Test, TestingModule } from '@nestjs/testing';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { HttpStatus } from '@nestjs/common';
import { Response } from 'express';

describe('HealthController', () => {
  let controller: HealthController;
  let service: HealthService;
  let mockRes: Partial<Response>;

  beforeEach(async () => {
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        {
          provide: HealthService,
          useValue: {
            isReady: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
    service = module.get<HealthService>(HealthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return status 200 for /health/live', () => {
    const res = mockRes;
    controller.getLiveness(res as Response);
    expect(res.status).toHaveBeenCalledWith(HttpStatus.OK);
    expect(res.json).toHaveBeenCalledWith({ status: 'ok' });
  });

  it('should return status 200 for /health/ready if ready', async () => {
    const res = mockRes;
    jest.spyOn(service, 'isReady').mockResolvedValue(true);
    await controller.getReadiness(res as Response);
    expect(res.status).toHaveBeenCalledWith(HttpStatus.OK);
    expect(res.json).toHaveBeenCalledWith({ status: 'ready' });
  });

  it('should return status 503 for /health/ready if not ready', async () => {
    const res = mockRes;
    jest.spyOn(service, 'isReady').mockResolvedValue(false);
    await controller.getReadiness(res as Response);
    expect(res.status).toHaveBeenCalledWith(HttpStatus.SERVICE_UNAVAILABLE);
    expect(res.json).toHaveBeenCalledWith({ status: 'unavailable' });
  });
});
