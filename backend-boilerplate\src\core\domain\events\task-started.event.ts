import { DomainEvent } from './domain-event';
import { Task } from '../task.entity';

export class TaskStartedEvent extends DomainEvent {
  private readonly task: Task;

  constructor(
    task: Task,
    metadata?: {
      correlationId?: string;
      userId?: string;
    },
  ) {
    super('task.started', {
      correlationId: metadata?.correlationId,
      userId: metadata?.userId || task.userId,
    });

    this.task = task;
  }

  getData(): object {
    return {
      taskId: this.task.id,
      userId: this.task.userId,
      title: this.task.title,
      startedAt: this.task.updatedAt.toISOString(),
    };
  }
}
