import { Injectable, Inject } from '@nestjs/common';
import { CustomerContract } from '../../domain/entities/customer-contract.entity';
import { ICustomerContractRepository } from '../../domain/repositories/customer-contract.repository.interface';

@Injectable()
export class GetCustomerContractUseCase {
  constructor(
    @Inject('ICustomerContractRepository')
    private readonly repository: ICustomerContractRepository,
  ) {}

  async execute(customerUuid: string, contractUuid: string): Promise<CustomerContract | null> {
    return this.repository.findByUuid(customerUuid, contractUuid);
  }
} 