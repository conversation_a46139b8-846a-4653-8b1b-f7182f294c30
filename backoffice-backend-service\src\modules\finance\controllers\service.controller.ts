import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Role } from '../../../core/domain/role.enum';
import { CreateServiceDto } from '../dto/create-service.dto';
import { UpdateServiceDto } from '../dto/update-service.dto';
import { ServiceResponseDto, PaginatedServicesResponseDto } from '../dto/service-response.dto';
import { CreateServiceUseCase } from '../../../core/application/use-cases/service/create-service.use-case';
import { UpdateServiceUseCase } from '../../../core/application/use-cases/service/update-service.use-case';
import { DeleteServiceUseCase } from '../../../core/application/use-cases/service/delete-service.use-case';
import { FindServiceByIdUseCase } from '../../../core/application/use-cases/service/find-service-by-id.use-case';
import { ListServicesUseCase } from '../../../core/application/use-cases/service/list-services.use-case';
import { EntityType } from '../../../core/domain/service/enums/entity-type.enum';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
  };
}

@ApiTags('Services')
@Controller('core/services')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ServiceController {
  constructor(
    private readonly createServiceUseCase: CreateServiceUseCase,
    private readonly updateServiceUseCase: UpdateServiceUseCase,
    private readonly deleteServiceUseCase: DeleteServiceUseCase,
    private readonly findServiceByIdUseCase: FindServiceByIdUseCase,
    private readonly listServicesUseCase: ListServicesUseCase,
  ) {}

  @Post()
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Criar um novo serviço' })
  @ApiBody({ type: CreateServiceDto })
  @ApiResponse({
    status: 201,
    description: 'Serviço criado com sucesso',
    type: ServiceResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 403, description: 'Sem permissão' })
  async create(
    @Body() dto: CreateServiceDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<ServiceResponseDto> {
    const service = await this.createServiceUseCase.execute(dto, req.user.id);
    return ServiceResponseDto.fromEntity(service);
  }

  @Get()
  @Roles(Role.USER, Role.ADMIN)
  @ApiOperation({ summary: 'Listar serviços com filtros e paginação' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página', example: 10 })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset para paginação', example: 0 })
  @ApiQuery({ name: 'entityUuid', required: false, description: 'UUID da entidade' })
  @ApiQuery({ name: 'entityType', required: false, enum: EntityType, description: 'Tipo da entidade' })
  @ApiQuery({ name: 'type', required: false, description: 'Filtro por tipo de serviço' })
  @ApiResponse({
    status: 200,
    description: 'Lista de serviços retornada com sucesso',
    type: PaginatedServicesResponseDto,
  })
  async list(
    @Query('limit') limit: string = '10',
    @Query('offset') offset: string = '0',
    @Query('entityUuid') entityUuid?: string,
    @Query('entityType') entityType?: EntityType,
    @Query('type') type?: string,
  ): Promise<PaginatedServicesResponseDto> {
    const result = await this.listServicesUseCase.execute({
      limit: parseInt(limit, 10),
      offset: parseInt(offset, 10),
      entityUuid,
      entityType,
      type,
    });

    return {
      items: result.items.map(ServiceResponseDto.fromEntity),
      total: result.total,
      limit: result.limit,
      offset: result.offset,
    };
  }

  @Get(':id')
  @Roles(Role.USER, Role.ADMIN)
  @ApiOperation({ summary: 'Buscar serviço por ID' })
  @ApiParam({ name: 'id', required: true, description: 'ID do serviço' })
  @ApiResponse({
    status: 200,
    description: 'Serviço encontrado com sucesso.',
    type: ServiceResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Serviço não encontrado' })
  async findById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ServiceResponseDto> {
    const service = await this.findServiceByIdUseCase.execute(id);
    return ServiceResponseDto.fromEntity(service);
  }

  @Put(':id')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Atualizar serviço' })
  @ApiParam({ name: 'id', required: true, description: 'ID do serviço' })
  @ApiBody({ type: UpdateServiceDto })
  @ApiResponse({
    status: 200,
    description: 'Serviço atualizado com sucesso',
    type: ServiceResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Serviço não encontrado' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() dto: UpdateServiceDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<ServiceResponseDto> {
    const service = await this.updateServiceUseCase.execute(id, dto, req.user.id);
    return ServiceResponseDto.fromEntity(service);
  }

  @Delete(':id')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Excluir serviço' })
  @ApiParam({ name: 'id', required: true, description: 'ID do serviço' })
  @ApiResponse({ status: 204, description: 'Serviço excluído com sucesso' })
  @ApiResponse({ status: 404, description: 'Serviço não encontrado' })
  async delete(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.deleteServiceUseCase.execute(id);
  }
} 