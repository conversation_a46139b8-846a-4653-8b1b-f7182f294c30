import { Injectable, Inject, BadRequestException } from '@nestjs/common';
import { IListDomainsUseCase, DomainFilterCriteria, DomainListResponse } from '../../domain/use-cases/list-domains.use-case.interface';
import { IDomainRepository } from '../../domain/repositories/domain.repository.interface';

@Injectable()
export class ListDomainsUseCase implements IListDomainsUseCase {
  constructor(
    @Inject('IDomainRepository')
    private readonly domainRepository: IDomainRepository,
  ) {}

  async execute(
    customerUuid: string,
    criteria: DomainFilterCriteria,
    limit: number,
    offset: number,
  ): Promise<DomainListResponse> {
    if (limit < 1 || offset < 0) {
      throw new BadRequestException('Limit must be >= 1 and offset must be >= 0');
    }

    const { items, total } = await this.domainRepository.findByCustomerUuid(customerUuid, {
      limit,
      offset,
      domain: criteria.domain,
    });

    return {
      items,
      limit,
      offset,
      total,
    };
  }
} 