import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

export class UpdateLabelDto {
  @ApiPropertyOptional({ example: 'Financeiro', description: 'Nome da label' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  label?: string;

  @ApiPropertyOptional({
    example: 'metodtoPgto',
    description: 'ID do componente associado',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  idComponent?: string;

  @ApiPropertyOptional({ example: 'finance', description: 'Nome do módulo associado' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  modulo?: string;
} 