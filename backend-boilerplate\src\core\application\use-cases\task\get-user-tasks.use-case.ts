import { Task } from '../../../domain/task.entity';
import { TaskRepository } from '../../../ports/repositories/task-repository.interface';

export interface GetUserTasksInput {
  userId: string;
}

export class GetUserTasksUseCase {
  constructor(private taskRepository: TaskRepository) {}

  async execute(input: GetUserTasksInput): Promise<Task[]> {
    const { userId } = input;

    // Buscar todas as tarefas do usuário
    return this.taskRepository.findByUserId(userId);
  }
}
