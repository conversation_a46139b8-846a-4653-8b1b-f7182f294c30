import { CreatePaymentMethodUseCase } from './create-payment-method-use-case';
import { PaymentMethodRepository } from '../../../ports/repositories/payment-method-repository.interface';
import { PaymentMethod } from '../../../domain/payment-method';

describe('CreatePaymentMethodUseCase', () => {
  let useCase: CreatePaymentMethodUseCase;
  let PaymentMethodRepository: jest.Mocked<PaymentMethodRepository>;

  beforeEach(() => {
    PaymentMethodRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      findWithPagination: jest.fn(),
      findByLabel: jest.fn(),
      findAll: jest.fn(),
      findByUuid: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    useCase = new CreatePaymentMethodUseCase(PaymentMethodRepository);
  });

  it('should create a new PaymentMethod successfully', async () => {
    const input = {
      id: 1,
      uuid: '550e8400-e29b-41d4-a716-************',
      label: 'Credit Card',
      description: 'Payment via credit card',
      createdBy: 'admin-user',
    };

    const mockPaymentMethod = new PaymentMethod(
      input.id,
      input.uuid,
      input.label,
      input.description,
      input.createdBy,
      new Date(),
      new Date(),
      input.createdBy,
    );

    const findByLabel = jest.spyOn(PaymentMethodRepository, 'findByLabel');
    const createSpy = jest.spyOn(PaymentMethodRepository, 'create');

    findByLabel.mockResolvedValue(null);
    createSpy.mockResolvedValue(mockPaymentMethod);

    const result = await useCase.execute(input);

    expect(findByLabel).toHaveBeenCalledWith(input.label);
    expect(createSpy).toHaveBeenCalled();
    expect(result.paymentMethod).toEqual(mockPaymentMethod.toJSON());
  });

  it('should throw an error if PaymentMethod with label already exists', async () => {
    const input = {
      id: 1,
      uuid: '550e8400-e29b-41d4-a716-************',
      label: 'Credit Card',
      description: 'Payment via credit card',
      createdBy: 'admin-user',
    };

    const existingPaymentMethod = new PaymentMethod(
      input.id,
      input.uuid,
      input.label,
      input.description,
      input.createdBy,
      new Date(),
      new Date(),
      input.createdBy,
    );

    const findByLabel = jest.spyOn(PaymentMethodRepository, 'findByLabel');
    const createSpy = jest.spyOn(PaymentMethodRepository, 'create');

    findByLabel.mockResolvedValue(existingPaymentMethod);

    await expect(useCase.execute(input)).rejects.toThrow(
      `Já existe uma forma de pagamento cadastrado com o label ${input.label}. Por favor, verifique os dados e tente novamente.`,
    );
    expect(findByLabel).toHaveBeenCalledWith(input.label);
    expect(createSpy).not.toHaveBeenCalled();
  });
});
