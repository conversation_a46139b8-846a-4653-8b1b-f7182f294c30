import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { Contact } from '../../../domain/supplier/value-objects/contact.value-object';
import { CreateSupplierDto } from '../../../../modules/finance/dto/create-supplier.dto';
import { ConflictException,
  NotFoundException
 } from '@nestjs/common';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierType } from '@/core/domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '@prisma/client';
import { AuthService } from '../../../../modules/auth/auth.service';
import { Role } from '../../../domain/role.enum';
import { UsersService } from '@/modules/users/users.service';
import { SupplierStatus } from '@/core/domain/supplier/enums/supplier-status.enum';

@Injectable()
export class CreateSupplierUseCase {
  constructor(
    @Inject(SUPPLIER_REPOSITORY)
    private readonly supplierRepository: SupplierRepositoryPort,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
  ) {}

  async execute(dto: CreateSupplierDto, userId: string): Promise<Supplier> {
    // Verificar se já existe um fornecedor com o mesmo CNPJ
    const existingSupplier = await this.supplierRepository.findByDocument(
      dto.document,
    );
    if (existingSupplier) {
      throw new ConflictException(
        'A supplier with this Document already exists',
      );
    }

    // Verificar se o usuário existe
    const user = await this.usersService.findByEmail(dto.email).catch(error => {
      if (error instanceof NotFoundException) {
      console.info(`User with email ${dto.email} not found, creating new user.`);
      return null;
      }
      throw error; // Re-throw unexpected errors
    });
    
    if (user) {
      throw new ConflictException(
        `A user with email ${dto.email} already exists`,
      );
    }
    
    // Criar os value objects
    const address = new Address(
      dto.address.street,
      dto.address.number || null,
      dto.address.complement || null,
      dto.address.neighborhood || null,
      dto.address.city,
      dto.address.zipCode,
      dto.address.state,
    );

    // Criar o usuario
    const userCreated = await this.usersService.create({
      name: dto.name,
      email: dto.email,
      password: 'sut@t6@LuhKX29*C', // Senha temporária, deve ser alterada pelo usuário
      role: Role.SUPPLIER_VIEWER
    });
    
    const classification =
      dto.type === SupplierType.OTHER
        ? SupplierClassification.GENERAL
        : SupplierClassification.CORE;

    // Criar a entidade Supplier
    const supplier = new Supplier(
      uuidv4(),
      dto.name,
      dto.document,
      dto.tradeName || null,
      address,
      dto.email,
      classification,
      dto.type,
      SupplierStatus.PENDING,
      userCreated.id, // Use the created user's ID
      userId,
      new Date(),
      new Date(),
      userId,
      dto.stateRegistration,
      dto.municipalRegistration,
      dto.taxRegime,
      dto.companySize,
    )
    
    // Persistir no repositório
    const createdSupplier = await this.supplierRepository.create(supplier);

    // Envia email de confirmação de cadastro
    await this.authService.forgotPassword({
      email: userCreated.email,
    })

    return createdSupplier;
  }
}
