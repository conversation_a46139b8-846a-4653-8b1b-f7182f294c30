import { Injectable, Inject } from '@nestjs/common';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { SupplierStatus, SupplierType } from '@prisma/client';

@Injectable()
export class ListSuppliersUseCase {
  constructor(
    @Inject(SUPPLIER_REPOSITORY)
    private readonly supplierRepository: SupplierRepositoryPort,
  ) {}

  async execute(params: {
    limit: number;
    offset: number;
    cnpj?: string;
    name?: string;
    type?: SupplierType;
    status?: SupplierStatus;
  }): Promise<{ items: Supplier[]; total: number }> {
    return this.supplierRepository.findWithPagination(params);
  }
}
