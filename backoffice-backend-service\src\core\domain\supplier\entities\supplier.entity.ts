import { Address } from '../value-objects/address.value-object';
import { Contact } from '../value-objects/contact.value-object';
import { SupplierStatus } from '../enums/supplier-status.enum';
import { SupplierType } from '../enums/supplier-type.enum';
import { CompanySize, SupplierClassification, TaxRegime } from '@prisma/client';

export class Supplier {
  private readonly _id: string;
  private readonly _name: string;
  private readonly _document: string;
  private _tradeName: string | null;
  private readonly _address: Address;
  private readonly _classification: SupplierClassification;
  private readonly _email: string;
  private readonly _userId: string;
  private readonly _type: SupplierType;
  private readonly _stateRegistration: string | undefined;
  private readonly _municipalRegistration: string | undefined;
  private readonly _taxRegime: TaxRegime | undefined;
  private readonly _companySize: CompanySize | undefined;
  private _status: SupplierStatus;
  private readonly _createdAt: Date;
  private readonly _createdBy: string;
  private _updatedAt: Date;
  private _updatedBy: string;

  constructor(
    id: string,
    name: string,
    document: string,
    tradeName: string | null,
    address: Address,
    email: string,
    classification: SupplierClassification,
    type: SupplierType,
    status: SupplierStatus,
    userId: string,
    createdBy: string,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
    updatedBy: string = createdBy,
    stateRegistration?: string | undefined,
    municipalRegistration?: string | undefined,
    taxRegime?: TaxRegime | undefined,
    companySize?: CompanySize | undefined,

  ) {
    this._id = id;
    this._name = name;
    this._document = document.replace(/\D/g, '');
    this._tradeName = tradeName;
    this._address = address;
    this._email = email;
    this._type = type;
    this._classification = classification;
    this._status = status;
    this._userId = userId;
    this._stateRegistration = stateRegistration;
    this._municipalRegistration = municipalRegistration;
    this._taxRegime = taxRegime;
    this._companySize = companySize;
    this._createdAt = createdAt;
    this._createdBy = createdBy;
    this._updatedAt = updatedAt;
    this._updatedBy = updatedBy;
    this.validate();
  }

  private validate(): void {
    if (!this._id) throw new Error('ID is required');
    if (!this._name) throw new Error('Name is required');
    if (!this._document) throw new Error('Document is required');
    if (!this._address) throw new Error('Address is required');
    if (!this._type) throw new Error('Type is required');
    if (!this._status) throw new Error('Status is required');
    if (!this._userId) throw new Error('User ID is required');
    if (!this._createdBy) throw new Error('CreatedBy is required');

    if (this._name.length < 3 || this._name.length > 100) {
      throw new Error('Name must be between 3 and 100 characters');
    }

    const cnpjRegex = /^\d{14}$/;
    const cpfRegex = /^\d{11}$/;
    
    if (!cnpjRegex.test(this._document) && !cpfRegex.test(this._document)) {
      throw new Error(`Invalid Document format. Must be 14 or 11 digits, received: ${this._document}`);
    }

    if (this._tradeName && this._tradeName.length > 100) {
      throw new Error('Trade name must be at most 100 characters');
    }
  }

  get id(): string {
    return this._id;
  }

  get name(): string {
    return this._name;
  }

  get document(): string {
    return this._document;
  }

  get tradeName(): string | null {
    return this._tradeName;
  }

  get address(): Address {
    return this._address;
  }

  get classification(): SupplierClassification {
    return this._classification;
  }

  get email(): string {
    return this._email;
  }

  get type(): SupplierType {
    return this._type;
  }

  get status(): SupplierStatus {
    return this._status;
  }

  get userId(): string {
    return this._userId;
  }

  get stateRegistration(): string | undefined {
    return this._stateRegistration;
  }

  get municipalRegistration(): string | undefined {
    return this._municipalRegistration;
  }

  get taxRegime(): TaxRegime | undefined {
    return this._taxRegime;
  }

  get companySize(): CompanySize | undefined {
    return this._companySize;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get createdBy(): string {
    return this._createdBy;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  get updatedBy(): string {
    return this._updatedBy;
  }

  updateTradeName(tradeName: string | null): void {
    this._tradeName = tradeName;
    this._updatedAt = new Date();
  }

  updateStatus(status: SupplierStatus, updatedBy: string): void {
    this._status = status;
    this._updatedAt = new Date();
    this._updatedBy = updatedBy;
  }

  toJSON(): {
    id: string;
    name: string;
    document: string;
    tradeName: string | null;
    address: Record<string, string>;
    classification: SupplierClassification;
    email: string;
    type: SupplierType;
    status: SupplierStatus;
    userId: string;
    stateRegistration: string | undefined;
    municipalRegistration: string | undefined;
    taxRegime: TaxRegime | undefined;
    companySize: CompanySize | undefined;
    createdAt: string;
    createdBy: string;
    updatedAt: string;
    updatedBy: string;
  } {
    return {
      id: this._id,
      name: this._name,
      document: this._document,
      tradeName: this._tradeName,
      address: this._address.toJSON(),
      classification: this._classification,
      email: this._email,
      type: this._type,
      status: this._status,
      userId: this._userId,
      stateRegistration: this._stateRegistration,
      municipalRegistration: this._municipalRegistration,
      taxRegime: this._taxRegime,
      companySize: this._companySize,
      createdAt: this._createdAt.toISOString(),
      createdBy: this._createdBy,
      updatedAt: this._updatedAt.toISOString(),
      updatedBy: this._updatedBy,
    };
  }
}
