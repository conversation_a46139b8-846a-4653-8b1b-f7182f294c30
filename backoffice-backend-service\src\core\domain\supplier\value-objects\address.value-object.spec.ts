import { Address } from './address.value-object';

describe('Address Value Object', () => {
  const validAddress = {
    street: 'Rua Teste',
    city: 'Cidade Teste',
    zipCode: '12345-678',
    state: 'TS',
  };

  describe('constructor', () => {
    it('should create a valid address', () => {
      const address = new Address(
        validAddress.street,
        validAddress.city,
        validAddress.zipCode,
        validAddress.state,
      );

      expect(address.street).toBe(validAddress.street);
      expect(address.city).toBe(validAddress.city);
      expect(address.zipCode).toBe(validAddress.zipCode);
      expect(address.state).toBe(validAddress.state);
    });

    it('should throw error when street is empty', () => {
      expect(() => {
        new Address(
          '',
          validAddress.city,
          validAddress.zipCode,
          validAddress.state,
        );
      }).toThrow('Street is required');
    });

    it('should throw error when city is empty', () => {
      expect(() => {
        new Address(
          validAddress.street,
          '',
          validAddress.zipCode,
          validAddress.state,
        );
      }).toThrow('City is required');
    });

    it('should throw error when zip code is empty', () => {
      expect(() => {
        new Address(
          validAddress.street,
          validAddress.city,
          '',
          validAddress.state,
        );
      }).toThrow('Zip code is required');
    });

    it('should throw error when state is empty', () => {
      expect(() => {
        new Address(
          validAddress.street,
          validAddress.city,
          validAddress.zipCode,
          '',
        );
      }).toThrow('State is required');
    });

    it('should throw error when zip code format is invalid', () => {
      const invalidZipCodes = [
        '12345678', // Sem hífen
        '1234-5678', // Formato errado
        '12345-6789', // Formato errado
        '12345-67', // Formato errado
        '12345-67890', // Formato errado
      ];

      invalidZipCodes.forEach((invalidZipCode) => {
        expect(() => {
          new Address(
            validAddress.street,
            validAddress.city,
            invalidZipCode,
            validAddress.state,
          );
        }).toThrow('Invalid zip code format. Use: 00000-000');
      });
    });
  });

  describe('toJSON', () => {
    it('should return address as JSON object', () => {
      const address = new Address(
        validAddress.street,
        validAddress.city,
        validAddress.zipCode,
        validAddress.state,
      );

      const json = address.toJSON();

      expect(json).toEqual({
        street: validAddress.street,
        city: validAddress.city,
        zipCode: validAddress.zipCode,
        state: validAddress.state,
      });
    });
  });

  describe('getters', () => {
    it('should return correct values through getters', () => {
      const address = new Address(
        validAddress.street,
        validAddress.city,
        validAddress.zipCode,
        validAddress.state,
      );

      expect(address.street).toBe(validAddress.street);
      expect(address.city).toBe(validAddress.city);
      expect(address.zipCode).toBe(validAddress.zipCode);
      expect(address.state).toBe(validAddress.state);
    });
  });
});
