import { Test, TestingModule } from '@nestjs/testing';
import { CreatePayablesTypeUseCase } from './create-payables-type.use-case';
import {
  PayablesTypeRepositoryPort,
  PAYABLES_TYPE_REPOSITORY,
} from '@core/ports/repositories/payables-type-repository.port';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';
import { CreatePayablesTypeDto } from '@modules/finance/dto/create-payables-type.dto';
import { ConflictException } from '@nestjs/common';

describe('CreatePayablesTypeUseCase', () => {
  let useCase: CreatePayablesTypeUseCase;
  let repository: jest.Mocked<PayablesTypeRepositoryPort>;

  const mockPayablesType = new PayablesType(
    '123e4567-e89b-12d3-a456-************',
    'OFFICE',
    'Despesas de Escritório',
    '11111111-**************-************',
    '11111111-**************-************',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<PayablesTypeRepositoryPort> = {
      create: jest.fn(),
      findByCode: jest.fn(),
      findById: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findByUuid: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreatePayablesTypeUseCase,
        {
          provide: PAYABLES_TYPE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<CreatePayablesTypeUseCase>(CreatePayablesTypeUseCase);
    repository = module.get<jest.Mocked<PayablesTypeRepositoryPort>>(
      PAYABLES_TYPE_REPOSITORY,
    );
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    const createPayablesTypeDto: CreatePayablesTypeDto = {
      code: 'OFFICE',
      description: 'Despesas de Escritório',
      createdBy: '11111111-**************-************',
      updatedBy: '11111111-**************-************',
    };

    it('should create a new payables type successfully', async () => {
      repository.findByCode.mockResolvedValue(null);
      repository.create.mockResolvedValue(mockPayablesType);

      const findByCode = jest.spyOn(repository, 'findByCode');
      const create = jest.spyOn(repository, 'create');

      const result = await useCase.execute(createPayablesTypeDto);

      expect(findByCode).toHaveBeenCalledWith(createPayablesTypeDto.code);
      expect(create).toHaveBeenCalled();
      expect(result).toEqual(mockPayablesType);
    });

    it('should throw ConflictException if code already exists', async () => {
      repository.findByCode.mockResolvedValue(mockPayablesType);

      const findByCode = jest.spyOn(repository, 'findByCode');
      const create = jest.spyOn(repository, 'create');

      await expect(useCase.execute(createPayablesTypeDto)).rejects.toThrow(
        ConflictException,
      );
      expect(findByCode).toHaveBeenCalledWith(createPayablesTypeDto.code);
      expect(create).not.toHaveBeenCalled();
    });
  });
});
