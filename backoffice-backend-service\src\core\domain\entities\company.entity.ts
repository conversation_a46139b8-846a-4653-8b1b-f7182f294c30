import { AggregateRoot } from '../../base/aggregate-root';
import { CompanyCreatedEvent } from '../events/company-created.event';

interface CompanyAddress {
  street: string;
  number: string;
  complement?: string;
  district: string;
  city: string;
  state: string;
  zipCode: string;
}

export class Company extends AggregateRoot {
  constructor(
    public readonly id: number,
    public readonly uuid: string,
    public readonly cnpj: string,
    public readonly razaoSocial: string,
    public readonly address: CompanyAddress,
    public readonly phone: string,
    public readonly email: string,
    public readonly status: string,
    public readonly createdBy: string,
    public readonly updatedBy: string,
    public readonly createdAt: Date,
    public readonly updatedAt: Date,
  ) {
    super();
  }

  static create(
    id: number,
    uuid: string,
    cnpj: string,
    razaoSocial: string,
    address: CompanyAddress,
    phone: string,
    email: string,
    status: string,
    createdBy: string,
    updatedBy: string,
    createdAt: Date,
    updatedAt: Date,
  ): Company {
    const company = new Company(
      id,
      uuid,
      cnpj,
      razaoSocial,
      address,
      phone,
      email,
      status,
      createdBy,
      updatedBy,
      createdAt,
      updatedAt,
    );

    company.addEvent(new CompanyCreatedEvent(company));

    return company;
  }
}
