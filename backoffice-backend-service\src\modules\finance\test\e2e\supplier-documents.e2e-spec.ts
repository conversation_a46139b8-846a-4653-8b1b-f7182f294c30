import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../../app.module';
import { v4 as uuidv4 } from 'uuid';
import { fail } from 'assert';
import { ConfigModule } from '@nestjs/config';
import * as path from 'path';
import * as dotenv from 'dotenv';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
const fs = require('fs');

dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });
jest.setTimeout(120000);

// Utilitário para criar supplier e obter token
async function createSupplierAndToken(app: INestApplication) {
  const testEmail = `e2e-docs-${uuidv4().substring(0, 8)}@example.com`;
  const supplierEmail = `e2e-supplier-docs-${uuidv4().substring(0, 8)}@example.com`;
  const testPassword = 'Test@123456';

  // Registrar usuário
  const registerResponse = await request(app.getHttpServer())
    .post('/auth/register')
    .send({
      email: testEmail,
      password: testPassword,
      name: 'E2E Docs User',
      type: 'EMPLOYEE',
      cpf: '12345678901',
    });
  if (registerResponse.status !== 201) {
    fail(`Registration failed: ${registerResponse.status}`);
  }
  const registerAuthResponse = registerResponse.body;
  let keycloakUserId =
    registerAuthResponse.user?.keycloakId ||
    registerAuthResponse.user?.sub ||
    '';

  // Fallback: login e getUserInfo
  let accessToken: string | undefined;
  if (!keycloakUserId) {
    const tempLoginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ username: testEmail, password: testPassword });
    if (tempLoginResponse.status === 200) {
      accessToken = tempLoginResponse.body.access_token;
      if (accessToken) {
        const keycloakIdentityProviderService = app.get(KeycloakIdentityProviderService);
        try {
          const userInfo = await keycloakIdentityProviderService.getUserInfo(accessToken);
          keycloakUserId = userInfo.id;
        } catch (error) {
          // Continua sem keycloakUserId
        }
      }
    }
  }

  // Atribuir papel ADMIN
  if (keycloakUserId) {
    const keycloakIdentityProviderService = app.get(KeycloakIdentityProviderService);
    try {
      await keycloakIdentityProviderService.assignUserRoles(keycloakUserId, ['ADMIN']);
      await new Promise((resolve) => setTimeout(resolve, 5000));
    } catch (error) {
      // Continua mesmo se falhar
    }
  }

  // Login final para garantir token com role
  const loginResponse = await request(app.getHttpServer())
    .post('/auth/login')
    .send({ username: testEmail, password: testPassword });
  if (loginResponse.status !== 200) {
    fail(`Login failed: ${loginResponse.status}`);
  }
  accessToken = loginResponse.body.access_token;

  // Criar supplier
  const createDto = {
    name: 'E2E Supplier Docs',
    document: `${Math.floor(Math.random() * 1e14)}`.padStart(14, '1'),
    tradeName: 'E2E Docs Trade Name',
    address: {
      street: 'Rua Teste',
      city: 'Cidade Teste',
      zipCode: '12345-678',
      state: 'TS',
    },
    type: 'BANK',
    email: supplierEmail,
  };
  const supplierResponse = await request(app.getHttpServer())
    .post('/core/suppliers')
    .set('Authorization', `Bearer ${accessToken}`)
    .send(createDto);
  if (supplierResponse.status !== 201) {
    fail(`Supplier creation failed: ${supplierResponse.status}`);
  }
  const createdSupplierId = supplierResponse.body.id || supplierResponse.body.uuid;
  return { accessToken, createdSupplierId };
}

describe('Supplier Documents (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const mockStorageProvider: IStorageProvider = {
      upload: jest.fn().mockResolvedValue(undefined),
      getDownloadUrl: jest.fn().mockResolvedValue('https://fake-url.com/document-e2e.pdf'),
      getFileUrl: jest.fn().mockResolvedValue('https://fake-url.com/document-e2e.pdf'),
      getFileStream: jest.fn().mockResolvedValue({}),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          envFilePath: path.resolve(process.cwd(), '.env.test'),
          isGlobal: true,
        }),
        AppModule,
      ],
    })
      .overrideProvider('IStorageProvider')
      .useValue(mockStorageProvider)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        skipMissingProperties: false,
      }),
    );
    await app.init();
  });

  afterAll(async () => {
    if (app) await app.close();
  });

  it('should upload supplier document', async () => {
    const { accessToken, createdSupplierId } = await createSupplierAndToken(app);
    const documentIdentifier = 'document-e2e.pdf';
    const tempFilePath = path.join(__dirname, documentIdentifier);
    fs.writeFileSync(tempFilePath, 'fake document content');
    const documentsMetadata = [
      {
        responsible: 'João Silva',
        department: 'Financeiro',
        description: 'Documento principal',
        expirationDate: '2025-12-31',
      },
    ];
    let uploadResponse;
    try {
      uploadResponse = await request(app.getHttpServer())
        .post(`/core/suppliers/${createdSupplierId}/documents`)
        .set('Authorization', `Bearer ${accessToken}`)
        .field('documentsMetadata', JSON.stringify(documentsMetadata))
        .attach('documents', tempFilePath);
    } finally {
      fs.unlinkSync(tempFilePath);
    }
    expect(uploadResponse.status).toBe(201);
    expect(Array.isArray(uploadResponse.body)).toBe(true);
    expect(uploadResponse.body.length).toBeGreaterThan(0);
    const createdDocument = uploadResponse.body[0];
    expect(createdDocument).toHaveProperty('uuid');
    expect(createdDocument).toHaveProperty('responsible', documentsMetadata[0].responsible);
    expect(createdDocument).toHaveProperty('department', documentsMetadata[0].department);
    expect(createdDocument).toHaveProperty('description', documentsMetadata[0].description);
  });

  it('should list supplier documents', async () => {
    const { accessToken, createdSupplierId } = await createSupplierAndToken(app);
    // Primeiro, faz upload de um documento
    const documentIdentifier = 'document-e2e-list.pdf';
    const tempFilePath = path.join(__dirname, documentIdentifier);
    fs.writeFileSync(tempFilePath, 'fake document content for list');
    const documentsMetadata = [
      {
        responsible: 'Maria Souza',
        department: 'RH',
        description: 'Documento para listagem',
        expirationDate: '2026-01-01',
      },
    ];
    let uploadResponse;
    try {
      uploadResponse = await request(app.getHttpServer())
        .post(`/core/suppliers/${createdSupplierId}/documents`)
        .set('Authorization', `Bearer ${accessToken}`)
        .field('documentsMetadata', JSON.stringify(documentsMetadata))
        .attach('documents', tempFilePath);
    } finally {
      fs.unlinkSync(tempFilePath);
    }
    expect(uploadResponse.status).toBe(201);
    expect(Array.isArray(uploadResponse.body)).toBe(true);
    expect(uploadResponse.body.length).toBeGreaterThan(0);
    const createdDocument = uploadResponse.body[0];
    
    // GET documents
    const listResponse = await request(app.getHttpServer())
      .get(`/core/suppliers/${createdSupplierId}/documents`)
      .set('Authorization', `Bearer ${accessToken}`);
    expect(listResponse.status).toBe(200);
    expect(Array.isArray(listResponse.body)).toBe(true);
    expect(listResponse.body.length).toBeGreaterThan(0);
    const found = listResponse.body.find((d: any) => d.uuid === createdDocument.uuid);
    expect(found).toBeDefined();
    expect(found.responsible).toBe(documentsMetadata[0].responsible);
    expect(found.department).toBe(documentsMetadata[0].department);
    expect(found.description).toBe(documentsMetadata[0].description);
  });
}); 