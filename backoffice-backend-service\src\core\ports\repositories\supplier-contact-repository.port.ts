import { SupplierContact } from '@/core/domain/supplier/entities/supplier-contact.entity';

export interface SupplierContactRepositoryPort {
  create(contact: Omit<SupplierContact, 'id'>): Promise<SupplierContact>;
  update(uuid: string, contact: Partial<Omit<SupplierContact, 'id' | 'supplierId' | 'updatedAt' | 'createdAt' | 'deletedAt'>>): Promise<SupplierContact>;
  delete(uuid: string): Promise<void>;
  findBySupplierId(supplierId: string): Promise<SupplierContact[]>;
} 