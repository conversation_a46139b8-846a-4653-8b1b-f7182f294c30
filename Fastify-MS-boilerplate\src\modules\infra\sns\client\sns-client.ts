import { InvalidCredentialsException } from '../../../../shared/errors/custom-exceptions';
import { SNSClient } from '@aws-sdk/client-sns';

export function createSnsClient(): SNSClient {
  const region = process.env.AWS_REGION;
  const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
  const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;

  if (!(region && accessKeyId && secretAccessKey)) {
    throw new InvalidCredentialsException(
      'Missing one or more required AWS environment variables: AWS_REGION, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY'
    );
  }

  return new SNSClient({
    region: region,
    credentials: {
      accessKeyId: accessKeyId,
      secretAccessKey: secretAccessKey,
    },
  });
}
