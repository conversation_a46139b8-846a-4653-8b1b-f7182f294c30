import { Injectable, Inject, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { IUpdateDomainUseCase, UpdateDomainInput } from '../../domain/use-cases/update-domain.use-case.interface';
import { Domain } from '../../domain/entities/domain.entity';
import { IDomainRepository } from '../../domain/repositories/domain.repository.interface';

@Injectable()
export class UpdateDomainUseCase implements IUpdateDomainUseCase {
  constructor(
    @Inject('IDomainRepository')
    private readonly domainRepository: IDomainRepository,
  ) { }

  async execute(input: UpdateDomainInput): Promise<Domain> {
    const domain = await this.domainRepository.findByUuid(input.uuid);
    if (!domain) {
      throw new NotFoundException(`Domain with UUID ${input.uuid} not found`);
    }

    if (domain.customerUuid !== input.customerUuid) {
      throw new ForbiddenException(`Domain ${input.uuid} does not belong to customer ${input.customerUuid}`);
    }

    if (input.data.domain && input.data.domain !== domain.domain) {
      const existingDomain = await this.domainRepository.findByDomain(
        domain.customerUuid,
        input.data.domain,
      );
      if (existingDomain) {
        throw new ConflictException(`Domain ${input.data.domain} already exists for this customer`);
      }
    }
    console.log('input.data', input.data);
    const updated = await this.domainRepository.update(input.uuid, {
      ...input.data,
      updatedBy: input.updatedBy,
    });
    return updated;
  }
} 