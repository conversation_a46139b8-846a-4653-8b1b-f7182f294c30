# Feature: Listagem de Clientes com Paginação e Filtros

## Visão Geral

Esta documentação descreve a implementação da funcionalidade que permite listar clientes com paginação e filtros por nome corporativo (partial match) e CNPJ (exact match), desenvolvida para atender à seguinte história de usuário:

> **Como** Administrador Financeiro  
> **Eu quero** listar clientes com limit, offset, filtrar por corporateName (partial match) e cnpj (exact)  
> **Para que** eu encontre clientes específicos de forma eficiente

## Critérios de Aceite

- Validação de parâmetros:
  - Se `limit < 1` ou `offset < 0` → Retorna 400 Bad Request
- Resposta de sucesso:
  - 200 OK com `CustomerList` contendo campos:
    - `items`: Array de clientes
    - `limit`: Limite de itens por página
    - `offset`: Número de itens ignorados para paginação
    - `total`: Total de registros que atendem aos critérios

## Implementação

A implementação seguiu os princípios de Clean Architecture, com clara separação entre as camadas de domain, application e infrastructure:

### Domain Layer

1. **Interfaces e Entidades**
   - `Customer`: Entidade de domínio
   - `ICustomerRepository`: Interface do repositório
   - `CustomerFilterCriteria`: Interface para os critérios de filtro
   - `IListCustomersUseCase`: Interface do caso de uso

```typescript
// src/modules/customers/domain/use-cases/list-customers.use-case.interface.ts
export interface CustomerFilterCriteria {
  corporateName?: string;
  cnpj?: string;
}

export interface CustomerListResponse {
  items: Customer[];
  limit: number;
  offset: number;
  total: number;
}
```

### Application Layer

1. **Caso de Uso**
   - `ListCustomersUseCase`: Implementa a lógica de negócio para listar clientes
   - Validação de parâmetros `limit` e `offset`
   - Utilização do repositório para buscar os dados

```typescript
// src/modules/customers/application/use-cases/list-customers.use-case.ts
@Injectable()
export class ListCustomersUseCase implements IListCustomersUseCase {
  constructor(
    @Inject('ICustomerRepository')
    private readonly customerRepository: ICustomerRepository,
  ) {}

  async execute(
    criteria: CustomerFilterCriteria,
    limit: number,
    offset: number,
  ): Promise<CustomerListResponse> {
    if (limit < 1 || offset < 0) {
      throw new BadRequestException(
        'Limit must be >= 1 and offset must be >= 0',
      );
    }

    const { customers, total } = await this.customerRepository.listCustomers(
      criteria,
      limit,
      offset,
    );

    return {
      items: customers,
      limit,
      offset,
      total,
    };
  }
}
```

2. **Controller**
   - `CustomerController`: Recebe requisições HTTP e delega para o caso de uso
   - Implementação do endpoint `GET /customers`
   - Mapeamento de DTOs para o domínio e vice-versa

```typescript
// src/modules/customers/application/controllers/customer.controller.ts
@Get()
@HttpCode(HttpStatus.OK)
@ApiListCustomers()
async listCustomers(
  @Query() query: CustomerListQueryDto,
): Promise<CustomerListResponseDto> {
  const criteria: CustomerFilterCriteria = {
    corporateName: query.filterCorporateName,
    cnpj: query.filterCNPJ,
  };

  const result = await this.listCustomersUseCase.execute(
    criteria,
    query.limit ?? 10,
    query.offset ?? 0,
  );

  // ... Mapeamento para DTO de resposta
}
```

### Infrastructure Layer

1. **Repositório**
   - `CustomerRepository`: Implementação do repositório usando Prisma
   - Construção da query com filtros para nome corporativo (ILIKE) e CNPJ (exact match)
   - Execução de queries para listagem paginada e contagem total

```typescript
// src/modules/customers/infrastructure/repositories/customer.repository.ts
async listCustomers(
  criteria: CustomerFilterCriteria,
  limit: number,
  offset: number,
): Promise<{ customers: Customer[]; total: number }> {
  const where: Prisma.CustomerWhereInput = {};

  if (criteria.corporateName) {
    where.name = {
      contains: criteria.corporateName,
      mode: 'insensitive',  // ILIKE para partial match
    };
  }

  if (criteria.cnpj) {
    where.document = criteria.cnpj;  // Exact match
  }

  const [customers, total] = await Promise.all([
    this.prisma.customer.findMany({
      where,
      skip: offset,
      take: limit,
    }),
    this.prisma.customer.count({ where }),
  ]);

  // ... Mapeamento para entidades de domínio
}
```

2. **DTOs**
   - `CustomerListQueryDto`: Validação dos parâmetros de query
   - `CustomerResponseDto`: Mapeamento da entidade para resposta
   - `CustomerListResponseDto`: Estrutura de resposta com paginação

### Testes

1. **Testes Unitários**
   - Testes para o caso de uso com diferentes combinações de filtros
   - Testes para validação de parâmetros inválidos
   - Testes para o repositório com mocks do Prisma

2. **Testes de Integração**
   - Verificação da aplicação correta dos filtros
   - Verificação da paginação e contagem total

## Documentação OpenAPI (Swagger)

### Reorganização dos Decoradores Swagger

Como parte deste trabalho, foi implementada uma refatoração na organização dos decoradores Swagger do projeto:

1. **Criação de Decoradores Específicos**
   - Decoradores isolados por módulo em arquivos dedicados
   - Padrão de nomenclatura consistente (ex: `ApiListCustomers`, `ApiGetCustomer`)

2. **Estrutura de Diretórios**
   - `/src/infrastructure/swagger/decorators/`: Diretório base
   - Arquivos por módulo: `customers.swagger.ts`, `auth.swagger.ts`, etc.

3. **Implementação do Decorator**
```typescript
// src/infrastructure/swagger/decorators/customers.swagger.ts
export function ApiListCustomers() {
  return applyDecorators(
    ApiOperation({ summary: 'List customers with filtering and pagination' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Customers retrieved successfully',
      type: CustomerListResponseDto,
    }),
    ApiResponse({
      status: HttpStatus.BAD_REQUEST,
      description: 'Invalid pagination parameters',
    }),
  );
}
```

4. **Forma de Uso**
```typescript
@Get()
@HttpCode(HttpStatus.OK)
@ApiListCustomers()
async listCustomers(@Query() query: CustomerListQueryDto): Promise<CustomerListResponseDto> {
  // Implementação
}
```

5. **Benefícios**
   - Reutilização de código
   - Documentação consistente
   - Melhor organização e manutenibilidade
   - Padronização entre diferentes módulos

Os decoradores Swagger são importados diretamente de seus arquivos específicos em cada controller, seguindo o padrão:

```typescript
import { ApiListCustomers, ApiGetCustomer } from '@/infrastructure/swagger/decorators/customers.swagger';
```

## Resultado

A implementação resultou em um endpoint `GET /customers` que suporta:

- Paginação via parâmetros `limit` e `offset`
- Filtro parcial case-insensitive por nome corporativo
- Filtro exato por CNPJ
- Resposta paginada com contagem total

Exemplo de resposta:
```json
{
  "items": [
    {
      "uuid": "123e4567-e89b-12d3-a456-************",
      "corporateName": "ACME Corp",
      "document": "12345678901234",
      "email": "<EMAIL>",
      "phone": "******-456-7890",
      "address": {
        "street": "Main St",
        "number": "123",
        "city": "Anytown",
        "state": "ST",
        "zipCode": "12345-678"
      },
      "status": "ACTIVE",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "limit": 5,
  "offset": 0,
  "total": 1
}
```

Esta implementação segue as diretrizes do projeto para Clean Architecture e padrões de documentação Swagger, resultando em código de alta qualidade, testável e bem documentado. 