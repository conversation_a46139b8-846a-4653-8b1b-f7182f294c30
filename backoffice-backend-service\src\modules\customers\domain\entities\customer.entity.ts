export enum CustomerStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
}

export interface Customer {
  id?: number;
  uuid: string;
  razaoSocial: string;
  cnpj: string;
  email: string;
  phone?: string;
  address?: Record<string, unknown>;
  image?: string;
  status: CustomerStatus;
  userId: string | null;
  url: string;
  createdBy: string;
  updatedBy: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}
