import { Module, OnModuleInit, Type } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { EventEmitterService, EventData } from './event-emitter.service';

/**
 * Interface para handlers de eventos de domínio
 */
export interface DomainEventHandler<T = unknown> {
  handle(event: T): Promise<void> | void;
}

/**
 * Tipo para construtores de handlers de eventos
 */
export type EventHandlerConstructor = Type<DomainEventHandler>;

/**
 * Decorador para marcar uma classe como handler de um evento de domínio
 */
export function DomainEventHandler(eventName: string) {
  return function (target: Type<DomainEventHandler>) {
    Reflect.defineMetadata('domain_event_name', eventName, target);
  };
}

/**
 * Módulo para gerenciar e rotear eventos de domínio para seus respectivos handlers
 */
@Module({})
export class DomainEventsModule implements OnModuleInit {
  private static handlers: Map<string, EventHandlerConstructor[]> = new Map();

  constructor(
    private readonly moduleRef: ModuleRef,
    private readonly eventEmitter: EventEmitterService,
  ) {}

  /**
   * Registra um handler para um evento específico
   */
  static registerHandler(
    eventName: string,
    handlerClass: EventHandlerConstructor,
  ): void {
    const handlers = this.handlers.get(eventName) || [];
    handlers.push(handlerClass);
    this.handlers.set(eventName, handlers);
  }

  async onModuleInit(): Promise<void> {
    // Registra os handlers para eventos
    this.registerEventHandlers();

    // Subscreve aos eventos - precisa de um await para satisfazer o linter
    await new Promise<void>((resolve) => {
      this.eventEmitter.onAll().subscribe((event) => {
        void this.routeEventToHandlers(event);
      });
      resolve();
    });
  }

  /**
   * Processa cada evento roteando-o para todos os handlers registrados
   */
  private async routeEventToHandlers(event: EventData<unknown>): Promise<void> {
    const { type, payload } = event;
    const handlers = DomainEventsModule.handlers.get(type) || [];

    if (handlers.length === 0) {
      console.log(`Nenhum handler encontrado para o evento ${type}`);
      return;
    }

    for (const handlerClass of handlers) {
      try {
        const handler =
          await this.moduleRef.resolve<DomainEventHandler>(handlerClass);
        await handler.handle(payload);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'erro desconhecido';
        console.error(
          `Erro ao processar evento ${type} no handler ${handlerClass.name}: ${errorMessage}`,
        );
      }
    }
  }

  /**
   * Registra automaticamente todos os handlers baseados no decorador @DomainEventHandler
   */
  private registerEventHandlers(): void {
    try {
      // Obtém os metadados com verificação de tipo
      const providersMetadata = Reflect.getMetadata(
        'providers',
        DomainEventsModule,
      ) as unknown[];

      // Validação explícita do tipo de metadados
      if (!providersMetadata) {
        console.log('Nenhum provider encontrado para registro');
        return;
      }

      // Verificação de tipo de array
      if (!Array.isArray(providersMetadata)) {
        console.log('Metadados de providers não são um array válido');
        return;
      }

      // Usamos diretamente o providersMetadata que já foi tipado como unknown[]
      const providers = providersMetadata;

      if (providers.length === 0) {
        console.log('Nenhum provider encontrado para registro');
        return;
      }

      // Processa cada provider com verificação adequada de tipo
      for (let i = 0; i < providers.length; i++) {
        const provider = providers[i];

        // Verifica se o provider é uma função
        if (typeof provider !== 'function') {
          continue;
        }

        try {
          // Obtém o metadado do evento com segurança de tipo
          const eventNameMetadata = Reflect.getMetadata(
            'domain_event_name',
            provider,
          ) as string | undefined;

          // Verifica explicitamente se é uma string
          if (typeof eventNameMetadata !== 'string' || !eventNameMetadata) {
            continue;
          }

          // Agora temos certeza que é uma string
          const eventName: string = eventNameMetadata;

          // Registra o handler com o tipo correto usando asserção de tipo
          DomainEventsModule.registerHandler(
            eventName,
            provider as EventHandlerConstructor,
          );

          // Obtém o nome do handler de forma segura
          let providerName = 'UnknownHandler';

          try {
            if (
              'name' in provider &&
              provider.name &&
              typeof provider.name === 'string'
            ) {
              providerName = provider.name;
            }
          } catch {
            // Se não conseguir acessar o nome, usa o valor padrão
          }

          console.log(
            `Handler registrado: ${providerName} para o evento ${eventName}`,
          );
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : 'erro desconhecido';
          console.error(`Erro ao registrar handler de evento: ${errorMessage}`);
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'erro desconhecido';
      console.error(`Erro ao registrar event handlers: ${errorMessage}`);
    }
  }
}

/**
 * Função para simplificar a configuração do módulo com os handlers
 */
export function registerEventHandlers(...handlers: EventHandlerConstructor[]) {
  return {
    module: DomainEventsModule,
    providers: handlers,
    exports: handlers,
  };
}
