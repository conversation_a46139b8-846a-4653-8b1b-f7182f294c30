import { User } from '../../../domain/user.entity';
import { UserRepository } from '../../../ports/repositories/user-repository.interface';

export interface UpdateUserInput {
  id: string;
  name?: string;
  password?: string;
}

export class UpdateUserUseCase {
  constructor(private userRepository: UserRepository) {}

  async execute(input: UpdateUserInput): Promise<User> {
    const { id, name, password } = input;

    // Buscar o usuário existente
    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new Error(`Usuário com ID ${id} não encontrado`);
    }

    // Aplicar as atualizações na entidade de domínio
    if (name) {
      user.updateName(name);
    }

    if (password) {
      user.updatePassword(password);
    }

    // Persistir o usuário atualizado
    return this.userRepository.update(user);
  }
}
