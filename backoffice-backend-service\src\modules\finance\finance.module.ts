import { Module, Injectable, forwardRef } from '@nestjs/common';
import { SupplierController } from './controllers/supplier.controller';
import { CompanyService } from './services/company.service';
import { SupplierService } from './services/supplier.service';
import { PrismaModule } from '../../infrastructure/prisma/prisma.module';
import { EventsModule } from '../../infrastructure/events/events.module';
import { CreateCompanyUseCase } from '../../core/application/use-cases/company/create-company.use-case';
import { DeleteCompanyUseCase } from '../../core/application/use-cases/company/delete-company.use-case';
import { CreateSupplierUseCase } from '../../core/application/use-cases/supplier/create-supplier.use-case';
import { DeleteSupplierUseCase } from '../../core/application/use-cases/supplier/delete-supplier.use-case';
import { ListSuppliersUseCase } from '../../core/application/use-cases/supplier/list-suppliers.use-case';
import { ListSupplierByUuidUseCase } from '../../core/application/use-cases/supplier/list-supplier-by-uuid.use-case';
import { UpdateSupplierUseCase } from '../../core/application/use-cases/supplier/update-supplier.use-case';
import { UpdateSupplierStatusUseCase } from '../../core/application/use-cases/supplier/update-supplier-status.use-case';
import {
  COMPANY_REPOSITORY,
  SUPPLIER_REPOSITORY,
} from '../../core/constants/injection-tokens';
import { PrismaCompanyRepository } from '../../infrastructure/repositories/prisma-company.repository';
import { PrismaSupplierRepository } from '../../infrastructure/repositories/prisma-supplier.repository';
import { EmployeeModule } from './employee/employee.module';
import { PayablesTypeController } from './controllers/payables-type.controller';
import { PAYABLES_TYPE_REPOSITORY } from '@/core/ports/repositories/payables-type-repository.port';
import { PrismaPayablesTypeRepository } from '@/infrastructure/repositories/prisma-payables-type.repository';
import { PayablesTypeService } from './services/payables-type.service';
import { ListPayablesTypeByUuidUseCase } from '@/core/application/use-cases/payables-type/list-payable-type-by-uuid.use-case';
import { ListPayablesTypeUseCase } from '@/core/application/use-cases/payables-type/list-payables-type.use-case';
import { CreatePayablesTypeUseCase } from '@/core/application/use-cases/payables-type/create-payables-type.use-case';
import { UpdatePayablesTypeUseCase } from '@/core/application/use-cases/payables-type/update-payable-type.use-case';
import { DeletePayableTypeUseCase } from '@/core/application/use-cases/payables-type/delete-payable-type.use-case';
import { CostCenterModule } from './cost-center/cost-center.module';
import { AuthModule } from '../auth/auth.module';
import { DocumentModule } from '../documents/document.module';
import { CreateDocumentUseCase } from '../documents/application/use-cases/create-document.use-case';
import { ListDocumentsUseCase } from '../documents/application/use-cases/list-documents.use-case';
import { ListSupplierContactsUseCase } from '../../core/application/use-cases/supplier/list-supplier-contacts.use-case';
import { CreateSupplierContactUseCase } from '../../core/application/use-cases/supplier/create-supplier-contact.use-case';
import { SupplierContactRepositoryPort } from '../../core/ports/repositories/supplier-contact-repository.port';
import { PrismaService } from '../../infrastructure/prisma/prisma.service';
import { GetSupplierByUserIdUsecase } from '@/core/application/use-cases/supplier/get-supplier-by-user-id.use-case';
import { CreateContractUseCase } from '../documents/application/use-cases/create-contract.use-case';
import { ContractModule } from '../documents/contract.module';
import { ListContractUseCase } from '../documents/application/use-cases/list-contract.use-case';
import { ServiceController } from './controllers/service.controller';
import { CreateServiceUseCase } from '../../core/application/use-cases/service/create-service.use-case';
import { UpdateServiceUseCase } from '../../core/application/use-cases/service/update-service.use-case';
import { DeleteServiceUseCase } from '../../core/application/use-cases/service/delete-service.use-case';
import { FindServiceByIdUseCase } from '../../core/application/use-cases/service/find-service-by-id.use-case';
import { ListServicesUseCase } from '../../core/application/use-cases/service/list-services.use-case';
import { PrismaServiceRepository } from '../../infrastructure/repositories/prisma-service.repository';
import { UsersModule } from '../users/users.module';
import { ValidateSupplierActivationUseCase } from '../../core/application/use-cases/supplier/validate-supplier-activation.use-case';
import { DownloadContractUseCase } from '../documents/application/use-cases/download-contract.use-case';
import { DownloadDocumentUseCase } from '../documents/application/use-cases/download-document.use-case';
import { DeleteContractUseCase } from '../documents/application/use-cases/delete-contract.use-case';
import { UpdateContractUseCase } from '../documents/application/use-cases/update-contract.use-case';
import { PrismaUserRepository } from '@/infrastructure/repositories/prisma-user.repository';
import { UpdateSupplierContactUseCase } from '@/core/application/use-cases/supplier/update-supplier-contact.usecase';
import { DeleteSupplierContactUseCase } from '@/core/application/use-cases/supplier/delete-supplier-contact.usecase';

@Injectable()
class PrismaSupplierContactRepository implements SupplierContactRepositoryPort {
  constructor(private readonly prisma: PrismaService) { }

  async create(contact) {
    return this.prisma.supplierContact.create({ data: contact });
  }

  async update(id: string, contact) {
    return this.prisma.supplierContact.update({ where: { id }, data: contact });
  }

  async delete(id: string) {
    await this.prisma.supplierContact.delete({ where: { id } });
  }

  async findBySupplierId(supplierId: string) {
    return this.prisma.supplierContact.findMany({ where: { supplierId } });
  }

  
}

@Module({
  imports: [
    PrismaModule,
    EventsModule,
    EmployeeModule,
    CostCenterModule,
    ContractModule,
    forwardRef(() => AuthModule),
    DocumentModule,
    UsersModule
  ],
  controllers: [SupplierController, PayablesTypeController, ServiceController],
  providers: [
    CompanyService,
    SupplierService,
    PayablesTypeService,
    CreateCompanyUseCase,
    DeleteCompanyUseCase,
    CreateSupplierUseCase,
    DeleteSupplierUseCase,
    CreateContractUseCase,
    ListSuppliersUseCase,
    ListSupplierByUuidUseCase,
    UpdateSupplierUseCase,
    UpdateSupplierStatusUseCase,
    CreatePayablesTypeUseCase,
    ListContractUseCase,
    DeleteContractUseCase,
    ListPayablesTypeUseCase,
    ListPayablesTypeByUuidUseCase,
    UpdatePayablesTypeUseCase,
    DeletePayableTypeUseCase,
    CreateDocumentUseCase,
    GetSupplierByUserIdUsecase,
    ListDocumentsUseCase,
    DownloadDocumentUseCase,
    DownloadContractUseCase,
    ListSupplierContactsUseCase,
    CreateSupplierContactUseCase,
    UpdateSupplierContactUseCase,
    DeleteSupplierContactUseCase,
    CreateServiceUseCase,
    UpdateServiceUseCase,
    DeleteServiceUseCase,
    FindServiceByIdUseCase,
    ListServicesUseCase,
    ValidateSupplierActivationUseCase,
    {
      provide: COMPANY_REPOSITORY,
      useClass: PrismaCompanyRepository,
    },
    {
      provide: SUPPLIER_REPOSITORY,
      useClass: PrismaSupplierRepository,
    },
    {
      provide: PAYABLES_TYPE_REPOSITORY,
      useClass: PrismaPayablesTypeRepository,
    },
    {
      provide: 'SupplierContactRepository',
      useClass: PrismaSupplierContactRepository,
    },
    {
      provide: 'SERVICE_REPOSITORY',
      useClass: PrismaServiceRepository,
    },
    {
      provide: 'UserRepository',
      useClass: PrismaUserRepository,
    },
    {
      provide: 'SupplierRepository',
      useClass: PrismaSupplierRepository,
    },
    UpdateContractUseCase,
  ],
  exports: [CompanyService, SupplierService, PayablesTypeService],
})
export class FinanceModule { }
