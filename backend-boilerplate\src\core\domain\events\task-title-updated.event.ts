import { DomainEvent } from './domain-event';
import { Task } from '../task.entity';

export class TaskTitleUpdatedEvent extends DomainEvent {
  private readonly task: Task;
  private readonly newTitle: string;

  constructor(
    task: Task,
    newTitle: string,
    metadata?: {
      correlationId?: string;
      userId?: string;
    },
  ) {
    super('task.title.updated', {
      correlationId: metadata?.correlationId,
      userId: metadata?.userId || task.userId,
    });

    this.task = task;
    this.newTitle = newTitle;
  }

  getData(): object {
    return {
      taskId: this.task.id,
      userId: this.task.userId,
      newTitle: this.newTitle,
      updatedAt: this.task.updatedAt.toISOString(),
    };
  }
}
