import Fastify from 'fastify';
import { env, isDevelopment } from './shared/config/env';
import { errorHandler } from './shared/middleware/error-handler.middleware';
import { registerUserRoutes } from './modules/users/routes/user.routes';
import { registerAuthRoutes } from './modules/auth/routes/auth.routes';
import { registerNasaRoutes } from './modules/nasa/routes/nasa.routes';
import { testConnection } from './shared/database/connection';
import { idempotencyMiddleware } from './shared/middleware/idempotency.middleware';
import {
  userSwaggerPaths,
  userSwaggerComponents,
} from './modules/users/docs/user.docs';
import { sanitizeResponse } from './shared/utils/data-manipulation';
import './shared/tracing/tracing';
import { trace } from '@opentelemetry/api';
import logger from './shared/logger/logger';
import { securityMiddleware } from './shared/middleware/security.middleware';

const fastify = Fastify({
  logger: isDevelopment
    ? {
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true,
        },
      },
    }
    : true,
});

fastify.addHook(
  'preHandler',
  securityMiddleware({
    enableSqlInjectionProtection: true,
    enableXssProtection: true,
    enablePathTraversalProtection: true,
    enableInputSanitization: true,
    strictMode: false,
    logAttempts: true,
  })
);

fastify.setErrorHandler(errorHandler);

fastify.get('/health', async (request, reply) => {
  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: env.NODE_ENV,
    port: env.PORT,
  };
});

const setupRoutes = async () => {
  if (env.CORS_ENABLED) {
    await fastify.register(import('@fastify/cors'), {
      origin: isDevelopment ? true : false,
    });
  }

  // Register Swagger
  if (isDevelopment) {
    await fastify.register(import('@fastify/swagger'), {
      swagger: {
        info: {
          title: 'FastWhite API',
          description:
            'API Server com arquitetura Package by Feature + Zod validation',
          version: '1.0.0',
        },
        host: `localhost:${env.PORT}`,
        schemes: ['http'],
        consumes: ['application/json'],
        produces: ['application/json'],
        tags: [
          { name: 'Users', description: 'Operações relacionadas aos usuários' },
        ],
        paths: userSwaggerPaths,
        definitions: userSwaggerComponents.schemas,
      },
    });

    await fastify.register(import('@fastify/swagger-ui'), {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'full',
        deepLinking: false,
      },
      uiHooks: {
        onRequest: function (request, reply, next) {
          next();
        },
        preHandler: function (request, reply, next) {
          next();
        },
      },
      staticCSP: true,
      transformStaticCSP: (header) => header,
      transformSpecification: (swaggerObject, request, reply) => {
        return swaggerObject;
      },
      transformSpecificationClone: true,
    });
  }

  await fastify.register(require('@fastify/cookie'), {
    secret: env.COOKIE_SECRET as string,
    parseOptions: {},
  });

  await fastify.register(registerUserRoutes, { prefix: '/api/users' });
  await fastify.register(registerAuthRoutes, { prefix: '/api/auth' });
  await fastify.register(registerNasaRoutes, { prefix: '/nasa' });
};

const start = async () => {
  try {
    const dbConnected = await testConnection();
    if (!dbConnected) {
      throw new Error('Database connection failed');
    }

    await setupRoutes();

    await fastify.listen({
      host: '0.0.0.0',
      port: env.PORT,
    });

    logger.info(`Server started on http://localhost:${env.PORT}`);
    logger.info(`Environment: ${env.NODE_ENV}`);
    logger.info(`Database: ${env.DB_HOST}:${env.DB_PORT}/${env.DB_NAME}`);
  } catch (error) {
    fastify.log.error(error);
    process.exit(1);
  }
};

start();
