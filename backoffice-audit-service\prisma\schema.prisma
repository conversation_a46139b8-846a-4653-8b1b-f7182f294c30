// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["audit"]
}


model Audit {
  id        Int      @id @default(autoincrement())
  eventType String
  timestamp DateTime @default(now())
  username    String
  method     String
  endpoint  String
  body      Json?
  ip        String?
  userAgent String?

  @@map("audit")
  @@schema("audit")
}