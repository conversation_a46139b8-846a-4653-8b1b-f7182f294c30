import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';

@Injectable()
export class SesService {
  private readonly client: SESClient;
  private readonly logger = new Logger(SesService.name);

  constructor(private readonly configService: ConfigService) {
    this.client = new SESClient({
      region: this.configService.get<string>('AWS_REGION') || 'us-east-1',
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID') || '',
        secretAccessKey:
          this.configService.get<string>('AWS_SECRET_ACCESS_KEY') || '',
      },
    });
  }

  async sendEmail(
    to: string[],
    subject: string,
    htmlBody: string,
    textBody?: string,
    from?: string,
    replyTo?: string[],
  ): Promise<void> {
    try {
      const defaultSender =
        this.configService.get<string>('AWS_SES_DEFAULT_SENDER') ||
        '<EMAIL>';

      const command = new SendEmailCommand({
        Destination: {
          ToAddresses: to,
        },
        Message: {
          Body: {
            Html: {
              Charset: 'UTF-8',
              Data: htmlBody,
            },
            ...(textBody && {
              Text: {
                Charset: 'UTF-8',
                Data: textBody,
              },
            }),
          },
          Subject: {
            Charset: 'UTF-8',
            Data: subject,
          },
        },
        Source: from || defaultSender,
        ...(replyTo && {
          ReplyToAddresses: replyTo,
        }),
      });

      const response = await this.client.send(command);
      this.logger.log(`Email enviado via SES com ID: ${response.MessageId}`);
    } catch (error) {
      this.logger.error(
        `Erro ao enviar email via SES: ${error instanceof Error ? error.message : 'erro desconhecido'}`,
      );
      throw error;
    }
  }
}
