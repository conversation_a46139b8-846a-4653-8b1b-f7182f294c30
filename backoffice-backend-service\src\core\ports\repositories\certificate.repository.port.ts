import { Certificate } from '../../../modules/customers/domain/entities/certificate.entity';

export interface ICertificateRepository {
  create(certificate: Certificate): Promise<Certificate>;
  update(certificate: Certificate): Promise<Certificate>;
  findById(id: string): Promise<Certificate | null>;
  findByCustomerId(customerId: number): Promise<Certificate[]>;
  delete(id: string): Promise<void>;
}

export const CERTIFICATE_REPOSITORY = 'ICertificateRepository'; 