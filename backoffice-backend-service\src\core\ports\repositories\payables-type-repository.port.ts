import { PayablesType } from '@/core/domain/payables-type/entities/payables-type.entity';

export const PAYABLES_TYPE_REPOSITORY = 'PAYABLES_TYPE_REPOSITORY';

export interface PayablesTypeRepositoryPort {
  create(payablesType: PayablesType): Promise<PayablesType>;
  findByCode(code: string): Promise<PayablesType | null>;
  findById(id: number): Promise<PayablesType | null>;
  findByUuid(uuid: string): Promise<PayablesType | null>;
  findAll(): Promise<PayablesType[]>;
  update(payablesType: PayablesType): Promise<PayablesType>;
  delete(id: string): Promise<void>;
  findWithPagination(params: {
    limit: number;
    offset: number;
    cnpj?: string;
    name?: string;
  }): Promise<{ items: PayablesType[]; total: number }>;
}
