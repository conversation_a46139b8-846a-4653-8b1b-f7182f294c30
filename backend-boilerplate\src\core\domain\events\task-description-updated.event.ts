import { DomainEvent } from './domain-event';
import { Task } from '../task.entity';

export class TaskDescriptionUpdatedEvent extends DomainEvent {
  private readonly task: Task;
  private readonly newDescription: string;

  constructor(
    task: Task,
    newDescription: string,
    metadata?: {
      correlationId?: string;
      userId?: string;
    },
  ) {
    super('task.description.updated', {
      correlationId: metadata?.correlationId,
      userId: metadata?.userId || task.userId,
    });

    this.task = task;
    this.newDescription = newDescription;
  }

  getData(): object {
    return {
      taskId: this.task.id,
      userId: this.task.userId,
      newDescription: this.newDescription,
      updatedAt: this.task.updatedAt.toISOString(),
    };
  }
}
