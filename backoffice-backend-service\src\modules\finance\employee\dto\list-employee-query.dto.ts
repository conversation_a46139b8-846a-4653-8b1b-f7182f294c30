import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class ListEmployeeQueryDto {
  @ApiProperty({
    description: 'Numero de itens por pagina',
    example: 5,
    minimum: 1,
    required: false,
    default: 10,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  limit?: number = 10;

  @ApiProperty({
    description: 'Numero de itens a pular',
    example: 0,
    minimum: 0,
    required: false,
    default: 0,
  })
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @IsOptional()
  offset?: number = 0;

  @ApiProperty({
    description: 'Nome do colaborador',
    example: '<PERSON>',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Email do colaborador',
    example: '<EMAIL>',
    required: false,
  })
  @IsString()
  @IsOptional()
  email?: string;
}
