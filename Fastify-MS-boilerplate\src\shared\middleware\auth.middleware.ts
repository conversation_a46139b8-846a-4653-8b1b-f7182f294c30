import jwt from 'jsonwebtoken';
import { env } from '../config/env';
import { HttpErrors } from '../enums/errors';
import type { FastifyRequest, FastifyReply } from 'fastify';
import type { User } from '@/modules/users/entites/user.entity';

interface JwtPayload {
  userId: string;
  email: string;
  username: string;
  role: string;
}

declare module 'fastify' {
  interface FastifyRequest {
    user?: JwtPayload;
  }
}

export const verifyToken =
  (routeRole = ['user', 'admin'] as User['role'][]) =>
  async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    const jwtSecret = env.JWT_SECRET || 'default-secret';
    const {
      headers: { authorization },
    } = request;

    const hasAuthHeader =
      typeof authorization === 'string' && authorization.startsWith('Bearer ');

    if (!hasAuthHeader) {
      const responseData = HttpErrors.UNAUTHORIZED;
      return reply.status(responseData.status).send({
        success: false,
        message: responseData.message,
      });
    }

    const [_, token] = authorization.split(' ');

    const decoded = jwt.verify(token, jwtSecret) as
      | JwtPayload
      | jwt.JsonWebTokenError;

    if (decoded instanceof jwt.JsonWebTokenError) {
      const responseData = HttpErrors.UNAUTHORIZED;
      return reply.status(responseData.status).send({
        success: false,
        message: responseData.message,
      });
    }

    if (!decoded.role || !routeRole.includes(decoded.role as User['role'])) {
      const responseData = HttpErrors.FORBIDDEN;
      return reply.status(responseData.status).send({
        success: false,
        message: responseData.message,
      });
    }

    request.user = decoded;
  };
