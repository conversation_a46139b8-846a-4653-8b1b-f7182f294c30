import type { FastifyRequest, FastifyReply } from 'fastify';
import * as userController from '../../controllers/user.controller';
import * as userService from '../../services/user.service';
import {
  validateBody,
  validateQuery,
  validateParams,
} from '@/shared/validators/schema-validator';
import { HttpStatus } from '@/shared/enums/status-code';

jest.mock('../../services/user.service');
jest.mock('../../../../shared/validators/schema-validator');

const mockUserService = userService as jest.Mocked<typeof userService>;
const mockValidateBody = validateBody as jest.MockedFunction<
  typeof validateBody
>;
const mockValidateQuery = validateQuery as jest.MockedFunction<
  typeof validateQuery
>;
const mockValidateParams = validateParams as jest.MockedFunction<
  typeof validateParams
>;

describe('User Controller', () => {
  let mockRequest: Partial<FastifyRequest>;
  let mockReply: Partial<FastifyReply>;

  beforeEach(() => {
    // Limpa todos os mocks antes de cada teste
    jest.clearAllMocks();

    mockRequest = {};
    mockReply = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    // Setup default validator mocks
    mockValidateBody.mockReturnValue({ with: jest.fn() });
    mockValidateQuery.mockReturnValue({ with: jest.fn() });
    mockValidateParams.mockReturnValue({ with: jest.fn() });
  });

  afterEach(() => {
    // Limpa todos os mocks após cada teste (redundante mas garante limpeza)
    jest.clearAllMocks();
  });

  afterAll(() => {
    // Restaura todas as implementações originais após todos os testes
    jest.restoreAllMocks();
  });

  describe('create', () => {
    it('should create a user successfully', async () => {
      const userData = { name: 'John Doe', email: '<EMAIL>' };
      const createdUser = {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        username: 'johndoe',
        password: 'hashedpassword123',
        role: 'user' as const,
        isActive: true,
        createdAt: '2025-07-14T00:00:00.000Z',
        updatedAt: '2025-07-14T00:00:00.000Z',
      };

      mockValidateBody.mockReturnValue({
        with: jest.fn().mockReturnValue(userData),
      });
      mockUserService.create.mockResolvedValue(createdUser);

      await userController.create(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockValidateBody).toHaveBeenCalledWith(mockRequest);
      expect(mockUserService.create).toHaveBeenCalledWith(userData);
      expect(mockReply.status).toHaveBeenCalledWith(HttpStatus.CREATED);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
        message: 'User created successfully',
        data: createdUser,
      });
    });
  });

  describe('getById', () => {
    it('should retrieve a user by ID successfully', async () => {
      const userId = 1;
      const user = {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        username: 'johndoe',
        password: 'hashedpassword123',
        role: 'user' as const,
        isActive: true,
        createdAt: '2025-07-14T00:00:00.000Z',
        updatedAt: '2025-07-14T00:00:00.000Z',
      };

      mockValidateParams.mockReturnValue({
        with: jest.fn().mockReturnValue({ id: userId }),
      });
      mockUserService.getById.mockResolvedValue(user);

      await userController.getById(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockValidateParams).toHaveBeenCalledWith(mockRequest);
      expect(mockUserService.getById).toHaveBeenCalledWith(userId);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
        message: 'User retrieved successfully',
        data: user,
      });
    });
  });

  describe('getAll', () => {
    it('should retrieve all users with pagination successfully', async () => {
      const query = { page: 1, limit: 10, name: 'John' };
      const serviceResult = {
        data: [
          {
            id: 1,
            name: 'John Doe',
            email: '<EMAIL>',
            username: 'johndoe',
            password: 'hashedpassword123',
            role: 'user' as const,
            isActive: true,
            createdAt: '2025-07-14T00:00:00.000Z',
            updatedAt: '2025-07-14T00:00:00.000Z',
          },
        ],
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
      };

      mockValidateQuery.mockReturnValue({
        with: jest.fn().mockReturnValue(query),
      });
      mockUserService.getAll.mockResolvedValue(serviceResult);

      await userController.getAll(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockValidateQuery).toHaveBeenCalledWith(mockRequest);
      expect(mockUserService.getAll).toHaveBeenCalledWith(
        { name: 'John' },
        { page: 1, limit: 10 }
      );
      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
        message: 'Users retrieved successfully',
        data: serviceResult.data,
        pagination: {
          page: serviceResult.page,
          limit: serviceResult.limit,
          total: serviceResult.total,
          totalPages: serviceResult.totalPages,
        },
      });
    });

    it('should handle query without filters', async () => {
      const query = { page: 1, limit: 10 };
      const serviceResult = {
        data: [],
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      };

      mockValidateQuery.mockReturnValue({
        with: jest.fn().mockReturnValue(query),
      });
      mockUserService.getAll.mockResolvedValue(serviceResult);

      await userController.getAll(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockUserService.getAll).toHaveBeenCalledWith(
        {},
        { page: 1, limit: 10 }
      );
    });
  });

  describe('update', () => {
    it('should update a user successfully', async () => {
      const userId = 1;
      const userData = { name: 'Jane Doe' };
      const updatedUser = {
        id: 1,
        name: 'Jane Doe',
        email: '<EMAIL>',
        username: 'janedoe',
        password: 'hashedpassword123',
        role: 'user' as const,
        isActive: true,
        createdAt: '2025-07-14T00:00:00.000Z',
        updatedAt: '2025-07-14T00:00:00.000Z',
      };

      mockValidateParams.mockReturnValue({
        with: jest.fn().mockReturnValue({ id: userId }),
      });
      mockValidateBody.mockReturnValue({
        with: jest.fn().mockReturnValue(userData),
      });
      mockUserService.update.mockResolvedValue(updatedUser);

      await userController.update(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockValidateParams).toHaveBeenCalledWith(mockRequest);
      expect(mockValidateBody).toHaveBeenCalledWith(mockRequest);
      expect(mockUserService.update).toHaveBeenCalledWith(userId, userData);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
        message: 'User updated successfully',
        data: updatedUser,
      });
    });
  });

  describe('softDelete', () => {
    it('should soft delete a user successfully', async () => {
      const userId = 1;

      mockValidateParams.mockReturnValue({
        with: jest.fn().mockReturnValue({ id: userId }),
      });
      mockUserService.softDelete.mockResolvedValue(undefined);

      await userController.softDelete(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply
      );

      expect(mockValidateParams).toHaveBeenCalledWith(mockRequest);
      expect(mockUserService.softDelete).toHaveBeenCalledWith(userId);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
        message: 'User deleted successfully',
      });
    });
  });
});
