import { CustomerPaymentPreference } from '../entities/customer-payment-preference.entity';

export interface ICustomerPaymentPreferenceRepository {
  create(data: Partial<CustomerPaymentPreference>): Promise<CustomerPaymentPreference>;
  findById(id: string): Promise<CustomerPaymentPreference | null>;
  findByCustomerId(customerId: string): Promise<CustomerPaymentPreference[]>;
  findAll(): Promise<CustomerPaymentPreference[]>;
  update(id: string, data: Partial<CustomerPaymentPreference>): Promise<CustomerPaymentPreference>;
  delete(id: string): Promise<void>;
} 