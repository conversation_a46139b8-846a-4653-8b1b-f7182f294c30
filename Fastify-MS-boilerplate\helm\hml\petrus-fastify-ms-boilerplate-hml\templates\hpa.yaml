{{- if .Values.hpa.enabled -}}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: {{ .Values.name }}-pod
    chart: "{{.Chart.Name}}-{{.Chart.Version}}"
  name: {{ .Values.name }}-pod
  namespace: {{ .Release.Namespace }}
spec:
  maxReplicas: {{ .Values.hpa.maxReplicas }}
  minReplicas: {{ .Values.hpa.minReplicas }}
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ .Values.name }}-dpl
  metrics:
    {{- if .Values.hpa.averageMemUtilization }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.hpa.averageMemUtilization }}
    {{- end }}
    {{- if .Values.hpa.averageCpuUtilization }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.hpa.averageCpuUtilization }}
    {{- end }}
{{- end }}