# Environment variables para testes
# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/nest_boilerplate_test?schema=public"

# App
APP_PORT=3001
APP_ENV=test
APP_NAME=petrus-nest-boilerplate-test
APP_VERSION=1.0.0

# JWT
JWT_SECRET=test-secret-key
JWT_EXPIRATION=1h

# AWS (Mockados para testes)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=test-key-id
AWS_SECRET_ACCESS_KEY=test-secret-key

# SQS
AWS_SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/fake-account/test-queue

# SNS
AWS_SNS_TOPIC_ARN=arn:aws:sns:us-east-1:fake-account:test-topic

# RabbitMQ (Pode usar um container Docker para testes)
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_QUEUE=test_queue
RABBITMQ_EXCHANGE=test_exchange
RABBITMQ_ROUTING_KEY=test_routing_key

# OpenTelemetry
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
OTEL_SERVICE_NAME=petrus-nest-boilerplate-test 