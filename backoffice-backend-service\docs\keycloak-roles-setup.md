# Keycloak Roles Setup via API

Este guia mostra como criar e atribuir roles do seu `schema.prisma` no Keycloak via terminal/API.

## 1. Obter access_token de admin

```bash
export ADMIN_TOKEN=$(curl -s -X POST "http://localhost:8080/realms/backoffice/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=admin-cli" \
  -d "client_secret=SEU_CLIENT_SECRET" \
  -d "username=admin2" \
  -d "password=admin2" \
  -d "grant_type=password" | jq -r .access_token)
```

## 2. Descobrir o ID do client

```bash
export CLIENT_ID=$(curl -s -X GET "http://localhost:8080/admin/realms/backoffice/clients?clientId=admin-cli" \
  -H "Authorization: Bearer $ADMIN_TOKEN" | jq -r '.[0].id')
```

## 3. <PERSON><PERSON><PERSON> as roles do schema.prisma

```bash
for R<PERSON><PERSON> in ADMIN USER FINANCE_ADMIN FINANCE_USER; do
  curl -X POST "http://localhost:8080/admin/realms/backoffice/clients/$CLIENT_ID/roles" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"name": "'$ROLE'", "description": "Role '$ROLE' do schema.prisma"}'
done
```

## 4. Descobrir o ID do usuário

```bash
export USER_ID=$(curl -s -X GET "http://localhost:8080/admin/realms/backoffice/users?username=admin2" \
  -H "Authorization: Bearer $ADMIN_TOKEN" | jq -r '.[0].id')
```

## 5. Atribuir todas as roles ao usuário

```bash
for ROLE in ADMIN USER FINANCE_ADMIN FINANCE_USER; do
  ROLE_OBJ=$(curl -s -X GET "http://localhost:8080/admin/realms/backoffice/clients/$CLIENT_ID/roles/$ROLE" \
    -H "Authorization: Bearer $ADMIN_TOKEN")
  curl -X POST "http://localhost:8080/admin/realms/backoffice/users/$USER_ID/role-mappings/clients/$CLIENT_ID" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -d "[$ROLE_OBJ]"
done
```

---

**Obs:**
- Substitua `SEU_CLIENT_SECRET` pelo valor correto do seu ambiente.
- Instale o `jq` para facilitar o parsing de JSON: `brew install jq` (Mac) ou `sudo apt install jq` (Linux).
- Repita para outros usuários conforme necessário. 