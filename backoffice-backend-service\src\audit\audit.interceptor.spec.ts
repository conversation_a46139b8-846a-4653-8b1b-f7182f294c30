/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/unbound-method */
/* eslint-disable @typescript-eslint/no-unused-vars */

import { AuditInterceptor } from './audit.interceptor';
import { AuditPublisherService } from './audit-publisher.service';
import { ExecutionContext, CallHandler } from '@nestjs/common';
import { of } from 'rxjs';
import { AuditEvent, AuditEventType } from './audit.interface';

describe('AuditInterceptor', () => {
  let interceptor: AuditInterceptor;
  let auditPublisherService: jest.Mocked<AuditPublisherService>;
  let mockExecutionContext: jest.Mocked<ExecutionContext>;
  let mockCallHandler: jest.Mocked<CallHandler>;

  beforeEach(() => {
    auditPublisherService = {
      publish: jest.fn(),
    } as any;

    mockCallHandler = {
      handle: jest.fn().mockReturnValue(of('test response')),
    };

    interceptor = new AuditInterceptor(auditPublisherService);
  });

  const createMockRequest = (overrides = {}) => ({
    method: 'GET',
    url: '/api/test',
    body: {},
    headers: {},
    ip: '127.0.0.1',
    get: jest.fn((header: string) => {
      if (header === 'user-agent') return 'test-agent';
      return undefined;
    }),
    user: undefined,
    ...overrides,
  });

  const createMockExecutionContext = (request: any) =>
    ({
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn().mockReturnValue(request),
      }),
    }) as any;

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });

  it('should publish audit event with authenticated user', () => {
    const mockUser = {
      email: '<EMAIL>',
      username: 'testuser',
    };

    const mockRequest = createMockRequest({
      user: mockUser,
      method: 'POST',
      url: '/api/create',
      body: { name: 'test' },
    });

    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        eventType: AuditEventType.AUDIT_LOG,
        username: '<EMAIL>',
        method: 'POST',
        endpoint: '/api/create',
        body: { name: 'test' },
        ip: '127.0.0.1',
        userAgent: 'test-agent',
      }),
    );
  });

  it('should use username from user object when email is not available', () => {
    const mockUser = {
      username: 'testuser',
    };

    const mockRequest = createMockRequest({
      user: mockUser,
    });

    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        username: 'testuser',
      }),
    );
  });

  it('should extract username from request body when user is not authenticated', () => {
    const mockRequest = createMockRequest({
      body: { username: 'bodyuser' },
      user: undefined,
    });

    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        username: 'bodyuser',
      }),
    );
  });

  it('should extract email from request body when user is not authenticated', () => {
    const mockRequest = createMockRequest({
      body: { email: '<EMAIL>' },
      user: undefined,
    });

    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        username: '<EMAIL>',
      }),
    );
  });

  it('should use "unknown" as username when no user information is available', () => {
    const mockRequest = createMockRequest({
      body: {},
      user: undefined,
    });

    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        username: 'unknown',
      }),
    );
  });

  it('should use x-forwarded-for header when available', () => {
    const mockRequest = createMockRequest({
      headers: { 'x-forwarded-for': '***********' },
      ip: '127.0.0.1',
    });

    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        ip: '***********',
      }),
    );
  });

  it('should fallback to request.ip when x-forwarded-for is not available', () => {
    const mockRequest = createMockRequest({
      headers: {},
      ip: '127.0.0.1',
    });

    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        ip: '127.0.0.1',
      }),
    );
  });

  it('should handle missing user-agent gracefully', () => {
    const mockRequest = createMockRequest({
      get: jest.fn().mockReturnValue(undefined),
    });

    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        userAgent: '',
      }),
    );
  });

  it('should process request body through omitSensitiveFields', () => {
    const mockRequest = createMockRequest({
      body: {
        username: 'test',
        password: 'secret123',
        normalField: 'visible',
      },
    });

    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        body: {
          username: 'test',
          password: '[REDACTED]',
          normalField: 'visible',
        },
      }),
    );
  });

  it('should include timestamp in audit event', () => {
    const mockRequest = createMockRequest();
    mockExecutionContext = createMockExecutionContext(mockRequest);

    const dateSpy = jest
      .spyOn(Date.prototype, 'toISOString')
      .mockReturnValue('2023-01-01T00:00:00.000Z');

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        timestamp: '2023-01-01T00:00:00.000Z',
      }),
    );

    dateSpy.mockRestore();
  });

  it('should call next.handle() to continue request processing', () => {
    const mockRequest = createMockRequest();
    mockExecutionContext = createMockExecutionContext(mockRequest);

    const result = interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(mockCallHandler.handle).toHaveBeenCalled();
    expect(result).toBeDefined();
  });

  it('should handle different HTTP methods', () => {
    const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

    methods.forEach((method) => {
      const mockRequest = createMockRequest({ method });
      mockExecutionContext = createMockExecutionContext(mockRequest);

      interceptor.intercept(mockExecutionContext, mockCallHandler);

      expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
        expect.objectContaining({
          method,
        }),
      );
    });

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledTimes(
      methods.length,
    );
  });

  it('should handle complex request body', () => {
    const complexBody = {
      user: {
        name: 'John Doe',
        email: '<EMAIL>',
        preferences: {
          theme: 'dark',
          notifications: true,
        },
      },
      data: [1, 2, 3],
      metadata: {
        version: '1.0',
        timestamp: Date.now(),
      },
    };

    const mockRequest = createMockRequest({ body: complexBody });
    mockExecutionContext = createMockExecutionContext(mockRequest);

    interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(auditPublisherService.publish as jest.Mock).toHaveBeenCalledWith(
      expect.objectContaining({
        body: complexBody, // Should be passed through as-is since no sensitive fields
      }),
    );
  });
});
