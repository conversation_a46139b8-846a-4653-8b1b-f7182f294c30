{"timestamp":"2025-07-29 01:00:39.375","level":"info","message":"Server started on http://localhost:3000","service":"fastify-api","environment":"development"}
{"timestamp":"2025-07-29 01:00:39.376","level":"info","message":"Environment: development","service":"fastify-api","environment":"development"}
{"timestamp":"2025-07-29 01:00:39.376","level":"info","message":"Database: postgres:5432/fastwhite","service":"fastify-api","environment":"development"}
