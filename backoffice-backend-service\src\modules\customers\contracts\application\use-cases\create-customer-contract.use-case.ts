import { Injectable, Inject } from '@nestjs/common';
import { ContractDto, CreateContractDto, CreateCustomerContractsDto } from '../../infrastructure/dtos/customercontract.dto';
import { CustomerContract } from '../../domain/entities/customer-contract.entity';
import { ICustomerContractRepository } from '../../domain/repositories/customer-contract.repository.interface';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import {v4 as uuidv4} from 'uuid'
import { ContractStatus, ContractType } from '@prisma/client';

@Injectable()
export class CreateCustomerContractUseCase {
  constructor(
    @Inject('ICustomerContractRepository')
    private readonly repository: ICustomerContractRepository,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  async execute(customerUuid: string, dto: CreateContractDto): Promise<CustomerContract> {
    let filePath: string | undefined = undefined;
    let fileName: string | undefined = undefined;
    
    if (dto.file) {
      const contractUuid = uuidv4();
      const versionId = 1;
      fileName = dto.file.originalname;
      
      const key = `customer/${customerUuid}/${contractUuid}/v${versionId}/${fileName}`;
      await this.storageProvider.upload(dto.file.buffer, dto.file.mimetype, key);
      filePath = key;
    }

    const contractStatus = dto.isSigned ? ContractStatus.APPROVED : ContractStatus.PENDING;

    const contract = await this.repository.create({
      customerUuid,
      filePath,
      fileName,
      isSigned: dto.isSigned,
      // status: contractStatus,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    
    return contract;
  }

  async executeMultiple(
    customerUuid: string,
    files: Express.Multer.File[],
    userId: string,
    createContractsDto: CreateCustomerContractsDto,
  ): Promise<CustomerContract[]> {
    const results: CustomerContract[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const metadata = createContractsDto.contracts[i];

      const contractUuid = uuidv4();
      const versionId = 1;
      const fileName = metadata.contractIdentifier || file.originalname;
      
      const key = `customer/${customerUuid}/${contractUuid}/v${versionId}/${fileName}`;
      await this.storageProvider.upload(file.buffer, file.mimetype, key);

      const contractStatus = metadata.signed ? ContractStatus.APPROVED : ContractStatus.PENDING;

      const contract = await this.repository.createWithMetadata({
        customerUuid,
        filePath: key,
        fileName,
        status: contractStatus,
        isSigned: metadata.signed || false,
        contractIdentifier: metadata.contractIdentifier,
        contractType: metadata.contractType || ContractType.CERT_PLATFORM,
        expirationDate: metadata.expirationDate,
        uploadedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      results.push(contract);
    }

    return results;
  }
} 