import { SupplierResponseDto } from '@/modules/finance/dto/supplier-response.dto';
import { ApiProperty } from '@nestjs/swagger';

export class UserProfileDto {
  @ApiProperty({
    description: 'ID do usuário',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Nome de usuário',
    example: 'johndoe',
  })
  username: string;

  @ApiProperty({
    description: 'Email do usuário',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Nome completo do usuário',
    example: 'John Doe',
  })
  fullName: string;

  @ApiProperty({
    description: 'Roles do usuário',
    example: ['USER', 'ADMIN'],
  })
  roles: string[];

  @ApiProperty({
    description: 'UUID do cliente associado ao usuário',
    type: () => ({
      uuid: { type: 'string', description: 'UUID do cliente' },
    }),
    required: false,
  })
  customer?: {
    uuid?: string;
  };

  @ApiProperty({
    description: 'Informações do fornecedor associado ao usuário',
    type: () => ({
      uuid: { type: 'string', description: 'UUID do fornecedor' },
    }),
    required: false,
  })
  supplier?: {
    uuid?: string;
  };

  @ApiProperty({
    description: 'Informações do funcionário associado ao usuário',
    type: () => ({
      uuid: { type: 'string', description: 'UUID do funcionário' },
    }),
    required: false,
  })
  employee?: {
    uuid?: string;
  };

}
