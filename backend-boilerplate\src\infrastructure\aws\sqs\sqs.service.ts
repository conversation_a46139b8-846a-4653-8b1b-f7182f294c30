import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  SQSClient,
  SendMessageCommand,
  ReceiveMessageCommand,
  DeleteMessageCommand,
  SendMessageBatchCommand,
  SendMessageBatchRequestEntry,
  MessageAttributeValue,
} from '@aws-sdk/client-sqs';

// Interface para representar uma mensagem recebida do SQS
export interface SQSMessage<T = Record<string, unknown>> {
  messageId: string | undefined;
  body: T;
  receiptHandle: string | undefined;
  attributes?: Record<string, MessageAttributeValue>;
}

@Injectable()
export class SQSService {
  private readonly sqsClient: SQSClient;

  constructor(private readonly configService: ConfigService) {
    this.sqsClient = new SQSClient({
      region: this.configService.get<string>('AWS_REGION', 'us-east-1'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID', ''),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
          '',
        ),
      },
    });
  }

  /**
   * Envia uma mensagem para uma fila SQS
   * @param queueUrl URL da fila SQS
   * @param message Mensagem a ser enviada (será convertida para JSON)
   * @param delaySeconds Segundos de atraso (0-900)
   * @param messageAttributes Atributos adicionais da mensagem
   */
  async sendMessage(
    queueUrl: string,
    message: Record<string, unknown>,
    delaySeconds: number = 0,
    messageAttributes: Record<string, unknown> = {},
  ): Promise<string> {
    try {
      const command = new SendMessageCommand({
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify(message),
        DelaySeconds: delaySeconds,
        MessageAttributes: this.formatMessageAttributes(messageAttributes),
      });

      const response = await this.sqsClient.send(command);
      console.log(
        `Mensagem enviada para a fila ${queueUrl} com MessageId: ${response.MessageId}`,
      );

      return response.MessageId || '';
    } catch (error) {
      console.error('Erro ao enviar mensagem para SQS:', error);
      throw error;
    }
  }

  /**
   * Envia múltiplas mensagens para uma fila SQS de uma vez
   * @param queueUrl URL da fila SQS
   * @param messages Array de mensagens a serem enviadas
   */
  async sendMessageBatch(
    queueUrl: string,
    messages: {
      id: string;
      body: Record<string, unknown>;
      delaySeconds?: number;
    }[],
  ): Promise<{ successful: string[]; failed: string[] }> {
    try {
      const entries: SendMessageBatchRequestEntry[] = messages.map((msg) => ({
        Id: msg.id,
        MessageBody: JSON.stringify(msg.body),
        DelaySeconds: msg.delaySeconds || 0,
      }));

      const command = new SendMessageBatchCommand({
        QueueUrl: queueUrl,
        Entries: entries,
      });

      const response = await this.sqsClient.send(command);

      if (response.Successful && response.Failed) {
        const successful = response.Successful.map((s) => s.Id || '');
        const failed = response.Failed.map((f) => f.Id || '');

        console.log(
          `Mensagens enviadas em lote para ${queueUrl}. Sucesso: ${successful.length}, Falhas: ${failed.length}`,
        );

        return { successful, failed };
      }

      return { successful: [], failed: [] };
    } catch (error) {
      console.error('Erro ao enviar mensagens em lote para SQS:', error);
      throw error;
    }
  }

  /**
   * Recebe mensagens de uma fila SQS
   * @param queueUrl URL da fila SQS
   * @param maxMessages Número máximo de mensagens a receber (1-10)
   * @param waitTimeSeconds Tempo de espera longa em segundos (0-20)
   */
  async receiveMessages(
    queueUrl: string,
    maxMessages = 10,
    waitTimeSeconds = 20,
  ): Promise<SQSMessage[]> {
    try {
      const command = new ReceiveMessageCommand({
        QueueUrl: queueUrl,
        MaxNumberOfMessages: maxMessages,
        WaitTimeSeconds: waitTimeSeconds,
        MessageAttributeNames: ['All'],
      });

      const response = await this.sqsClient.send(command);

      if (!response.Messages || response.Messages.length === 0) {
        return [];
      }

      return response.Messages.map((message) => {
        // Criar objeto de resultado com valores padrão seguros
        return {
          messageId: message.MessageId,
          body: this.safeParseJSON(message.Body),
          receiptHandle: message.ReceiptHandle,
          attributes: message.MessageAttributes,
        };
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`Erro ao receber mensagens do SQS: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Faz o parse seguro de uma string JSON
   * @param jsonString String JSON para fazer o parse
   * @returns Um objeto com os dados parseados
   */
  private safeParseJSON(jsonString?: string): Record<string, unknown> {
    if (!jsonString) {
      return {};
    }

    try {
      const parsedContent = JSON.parse(jsonString);

      // Verificação explícita de tipo para objetos
      if (
        parsedContent &&
        typeof parsedContent === 'object' &&
        !Array.isArray(parsedContent)
      ) {
        // Construir um novo objeto com tipagem segura
        const result: Record<string, unknown> = {};

        // Usando Object.entries para iterar sobre as propriedades de forma segura
        // Isso evita erros de unsafe-assignment e unsafe-member-access
        for (const [k, v] of Object.entries(parsedContent)) {
          result[k] = v;
        }

        return result;
      }

      // Para tipos primitivos ou arrays, encapsular em um objeto tipado
      return { content: parsedContent };
    } catch {
      // Em caso de erro no parse, retornar um objeto com o conteúdo bruto
      return { rawContent: jsonString };
    }
  }

  /**
   * Exclui uma mensagem da fila após processamento
   * @param queueUrl URL da fila SQS
   * @param receiptHandle Recibo da mensagem
   */
  async deleteMessage(queueUrl: string, receiptHandle: string): Promise<void> {
    try {
      const command = new DeleteMessageCommand({
        QueueUrl: queueUrl,
        ReceiptHandle: receiptHandle,
      });

      await this.sqsClient.send(command);
      console.log(
        `Mensagem com receiptHandle ${receiptHandle} excluída da fila ${queueUrl}`,
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'erro desconhecido';
      console.error(`Erro ao excluir mensagem do SQS: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Exemplo de uso: Receber e processar mensagens de uma fila
   * @param queueUrl URL da fila SQS
   * @param handler Função para processar cada mensagem
   */
  async pollQueue(
    queueUrl: string,
    handler: (message: Record<string, unknown>) => Promise<void>,
  ): Promise<void> {
    try {
      const messages = await this.receiveMessages(queueUrl);

      for (const message of messages) {
        try {
          await handler(message.body);
          if (message.receiptHandle) {
            await this.deleteMessage(queueUrl, message.receiptHandle);
          } else {
            console.error(
              `Não foi possível excluir mensagem ${message.messageId}: receiptHandle não definido`,
            );
          }
        } catch (error) {
          console.error(
            `Erro ao processar mensagem ${message.messageId}:`,
            error,
          );
          // Não excluir a mensagem para que ela retorne à fila após o timeout de visibilidade
        }
      }
    } catch (error) {
      console.error('Erro ao processar fila SQS:', error);
    }
  }

  private formatMessageAttributes(
    attributes: Record<string, unknown>,
  ): Record<string, MessageAttributeValue> {
    const result: Record<string, MessageAttributeValue> = {};

    for (const [key, value] of Object.entries(attributes)) {
      let dataType = 'String';
      let stringValue = '';

      if (typeof value === 'number') {
        dataType = 'Number';
        stringValue = value.toString();
      } else if (typeof value === 'boolean') {
        stringValue = value.toString();
      } else if (value === null) {
        stringValue = 'null';
      } else if (value === undefined) {
        stringValue = '';
      } else if (typeof value === 'object') {
        try {
          // Objetos são serializados com JSON.stringify
          const jsonString = JSON.stringify(value);
          stringValue = jsonString || '[Objeto vazio]';
        } catch {
          // Ignoramos o erro de tipagem aqui
          stringValue = '[Objeto Não Serializável]';
        }
      } else if (typeof value === 'string') {
        stringValue = value;
      } else if (typeof value === 'function') {
        stringValue = '[Função]';
      } else {
        // Para qualquer outro tipo, usamos conversão segura
        try {
          // Conversão segura para evitar o erro no-base-to-string
          if (value !== undefined && value !== null) {
            stringValue = `${value}`;
          } else {
            stringValue = '[Valor desconhecido]';
          }
        } catch {
          // Ignoramos o erro de tipagem aqui
          stringValue = '[Valor desconhecido]';
        }
      }

      result[key] = {
        DataType: dataType,
        StringValue: stringValue,
      };
    }

    return result;
  }
}
