import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';

import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { AuthService as ApplicationAuthService } from '../../core/application/services/auth.service';
import { UserRepository } from '../../core/ports/repositories/user-repository.interface';
import { AuthenticateUserUseCase } from '../../core/application/use-cases/auth/authenticate-user.use-case';
import { RegisterUserUseCase } from '../../core/application/use-cases/auth/register-user.use-case';
import { Inject } from '@nestjs/common';
import { AuthenticateUserOutput } from 'src/core/application/use-cases/auth/authenticate-user.use-case';

@Injectable()
export class AuthService {
  private applicationService: ApplicationAuthService;

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    @Inject('UserRepository')
    private userRepository: UserRepository,
  ) {
    // Criando instâncias dos casos de uso necessários
    const authenticateUserUseCase = new AuthenticateUserUseCase(userRepository);
    const registerUserUseCase = new RegisterUserUseCase(userRepository);

    // Instanciando o serviço de aplicação com os casos de uso
    this.applicationService = new ApplicationAuthService(
      this.jwtService,
      authenticateUserUseCase,
      registerUserUseCase,
      userRepository,
    );
  }

  async validateUser(
    email: string,
    password: string,
  ): Promise<AuthenticateUserOutput | null> {
    const user = await this.applicationService.validateUser(email, password);
    return user;
  }

  async login(loginDto: LoginDto) {
    const user = await this.validateUser(loginDto.email, loginDto.password);

    if (!user) {
      throw new UnauthorizedException('Credenciais inválidas');
    }

    const payload = { email: user.email, sub: user.id, role: user.role };

    return {
      user,
      access_token: this.jwtService.sign(payload),
    };
  }

  async register(registerDto: RegisterDto) {
    try {
      const result = await this.applicationService.register(
        registerDto.name,
        registerDto.email,
        registerDto.password,
      );

      // Verificar se result.user existe antes de acessar suas propriedades
      if (result.success && result.user) {
        const payload = {
          email: result.user.email,
          sub: result.user.id,
          role: result.user.role,
        };

        return {
          message: result.message,
          user: result.user,
          access_token: this.jwtService.sign(payload),
        };
      }

      return {
        message: result.message,
        success: result.success,
      };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Erro ao registrar usuário',
      );
    }
  }
}
