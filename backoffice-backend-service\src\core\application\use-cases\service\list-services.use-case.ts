import { Injectable, Inject } from '@nestjs/common';
import { ServiceRepositoryPort } from '../../../ports/repositories/service-repository.port';
import { Service } from '../../../domain/service/entities/service.entity';
import { EntityType } from '../../../domain/service/enums/entity-type.enum';

export interface ListServicesParams {
  limit: number;
  offset: number;
  entityUuid?: string;
  entityType?: EntityType;
  type?: string;
}

export interface ListServicesResponse {
  items: Service[];
  total: number;
  limit: number;
  offset: number;
}

@Injectable()
export class ListServicesUseCase {
  constructor(
    @Inject('SERVICE_REPOSITORY')
    private readonly serviceRepository: ServiceRepositoryPort,
  ) {}

  async execute(params: ListServicesParams): Promise<ListServicesResponse> {
    const { items, total } = await this.serviceRepository.findWithPagination({
      limit: params.limit,
      offset: params.offset,
      entityUuid: params.entityUuid,
      entityType: params.entityType,
      type: params.type,
    });

    return {
      items,
      total,
      limit: params.limit,
      offset: params.offset,
    };
  }
} 