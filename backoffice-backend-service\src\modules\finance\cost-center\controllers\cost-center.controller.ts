import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  Delete,
  Patch,
  Param,
  Get,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';

import { Role } from '@/core/domain/role.enum';
import { CostCenterService } from '../services/cost-center.service';
import { CreateCostCenterDto } from '../dto/create-cost-center.dto';
import { CostCenterResponseDto } from '../dto/cost-center-response.dto';
import {
  ApiCreateCostCenter,
  ApiDeleteCostCenter,
  ApiUpdateCostCenter,
} from '@/infrastructure/swagger/decorators';
import { UpdateCostCenterDto } from '../dto/update-cost-center.dto';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
  };
}

@ApiTags('Cost Centers')
@Controller('finance/cost-centers')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CostCenterController {
  constructor(private readonly costCenterService: CostCenterService) {}

  @Post()
  @Roles(Role.FINANCE_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  @ApiCreateCostCenter()
  async create(
    @Body() createCostCenterDto: CreateCostCenterDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<CostCenterResponseDto> {
    createCostCenterDto.createdBy = req.user.id;
    createCostCenterDto.updatedBy = req.user.id;
    return this.costCenterService.create(createCostCenterDto);
  }

  @Delete(':uuid')
  @Roles(Role.FINANCE_ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiDeleteCostCenter()
  async delete(@Param('uuid') uuid: string) {
    return this.costCenterService.delete(uuid);
  }

  @Patch(':uuid')
  @Roles(Role.FINANCE_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiUpdateCostCenter()
  async update(@Param('uuid') uuid: string, @Body() dto: UpdateCostCenterDto) {
    return this.costCenterService.update(uuid, dto);
  }

  @Get()
  @Roles(Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'Lista todos os cost centers' })
  @ApiResponse({
    status: 200,
    description: 'Lista de cost centers',
    type: [CostCenterResponseDto],
  })
  async findAll() {
    const items = await this.costCenterService.findAll();
    return { items };
  }
}
