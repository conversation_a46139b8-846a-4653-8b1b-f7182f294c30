import { DomainEvent } from '../domain/events/domain-event';

/**
 * Classe abstrata AggregateRoot
 * Serve como base para todas as entidades que precisam publicar eventos de domínio
 */
export abstract class AggregateRoot {
  private _domainEvents: DomainEvent[] = [];

  /**
   * Obtém os eventos de domínio registrados
   */
  get domainEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }

  /**
   * Adiciona um evento de domínio à lista de eventos pendentes
   * @param domainEvent Evento de domínio a ser adicionado
   */
  protected addEvent(domainEvent: DomainEvent): void {
    this._domainEvents.push(domainEvent);
  }

  /**
   * Remove todos os eventos pendentes
   * Deve ser chamado após a publicação dos eventos
   */
  public clearEvents(): void {
    this._domainEvents = [];
  }
}
