import * as bcrypt from 'bcryptjs';

export class PasswordHasher {
  /**
   * Gera um hash seguro da senha usando bcrypt.
   */
  static async hash(password: string): Promise<string> {
    const salt = await bcrypt.genSalt(); // padrão: 10 rounds
    return bcrypt.hash(password, salt);
  }

  /**
   * Compara a senha em texto puro com o hash armazenado.
   */
  static async compare(plainText: string, hash: string): Promise<boolean> {
    return bcrypt.compare(plainText, hash);
  }
}
