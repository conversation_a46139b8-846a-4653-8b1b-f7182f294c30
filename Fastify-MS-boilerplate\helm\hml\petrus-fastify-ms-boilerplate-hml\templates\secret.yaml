{{- if .Values.env.secret }}
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ .Values.name }}-sec
  labels:
    app: {{ .Values.name }}
    helm.sh/chart: "{{.Chart.Name}}-{{.Chart.Version}}"
    buildTimestamp: "{{ now | unixEpoch }}"
spec:
  refreshInterval: 5m0s
  secretStoreRef:
    name: {{ .Values.secretStoreRef.name }}
    kind: {{ .Values.secretStoreRef.kind }}
  target:
    name: {{ .Values.name }}-sec
    creationPolicy: Owner
  data:
    {{- range $name, $value := .Values.env.secret }}
  - secretKey: {{ $name }}
    remoteRef:
      key: {{ $value }}
    {{- end }}
{{- end }}