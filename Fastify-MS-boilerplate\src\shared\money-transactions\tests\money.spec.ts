import { Money } from "../money";

describe("Money", () => {
  it("should create an instance with a valid number", () => {
    const money = new Money(1000);
    expect(money.getInCents().number()).toBe(100000); // 1000 reais em centavos
  });

  it("should create an instance with a valid string", () => {
    const money = new Money("1000");
    expect(money.getInCents().number()).toBe(100000); // 1000 reais em centavos
  });

  it("should throw an error for invalid values", () => {
    expect(() => new Money("invalid")).toThrow(
      "Invalid monetary value: invalid"
    );
  });

  it("should handle cents correctly", () => {
    const money = new Money(100, { isCents: true });
    expect(money.getInReais().number()).toBe(1); // 100 centavos = 1 real
  });

  it("should add money correctly", () => {
    const money1 = new Money(1000); // 1000 reais
    const money2 = new Money(500); // 500 reais
    const result = money1.add(money2);
    expect(result.getInCents().number()).toBe(150000); // 1500 reais em centavos
  });

  it("should subtract money correctly", () => {
    const money1 = new Money(1000); // 1000 reais
    const money2 = new Money(500); // 500 reais
    const result = money1.subtract(money2);
    expect(result.getInCents().number()).toBe(50000); // 500 reais em centavos
  });

  it("should multiply correctly", () => {
    const money = new Money(1000); // 1000 reais
    const result = money.multiply(0.1); // 10%
    expect(result.getInCents().number()).toBe(10000); // 10 reais em centavos
  });

  it("should compare correctly", () => {
    const money1 = new Money(1000); // 1000 reais
    const money2 = new Money(500); // 500 reais
    expect(money1.isGreaterThan(money2)).toBe(true);
    expect(money1.isLessThan(money2)).toBe(false);
    expect(money1.isEqualTo(money2)).toBe(false);
  });
});
