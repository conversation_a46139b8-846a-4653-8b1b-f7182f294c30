name: backoffice-backend-service-hml
# replicaCount: 1
image:
  repository: 267316525040.dkr.ecr.us-east-2.amazonaws.com/backoffice-backend-service
  tag: latest
  pullPolicy: Always
  containerPort: 3000
environment:
  NODE_ENV: production
  JWT_SECRET: 'seu_jwt_secret'
  JWT_EXPIRATION: 3600
  RABBITMQ_HOST: b-9ef1d7cd-a9b1-489d-9810-e20c98c49ea0.mq.us-east-2.on.aws
  RABBITMQ_PORT: 5671
  RABBITMQ_USERNAME: backoffice-mq
  KEYCLOAK_BASE_URL: 'https://backoffice-keycloak-hml.petrus-software.com'
  KEYCLOAK_REALM: 'cactus-backoffice'
  KEYCLOAK_CLIENT_ID: 'backoffice'
  # KEYCLOAK_CLIENT_ID: backend-dev-client
  # KEYCLOAK_CLIENT_SECRET: myclientsecret
  KEYCLOAK_ADMIN_USERNAME: petrus
  FRONTEND_URL: 'https://backoffice-frontend-hml.petrus-software.com'

  # AWS
  AWS_REGION: us-east-2
  AWS_S3_BUCKET: bucket-backoffice-hml
  # AWS_S3_BUCKET: s3-petrus-keys
resources:
  requests:
    cpu: 200m
    memory: 200Mi
  limits:
    cpu: 200m
    memory: 200Mi
livenessProbe:
  httpGet:
    path: {}
  initialDelaySeconds: 10
  failureThreshold: 3
  periodSeconds: 10
readinessProbe:
  tcpSocket: null
  initialDelaySeconds: 10
  periodSeconds: 10
  failureThreshold: 3
service:
  type: ClusterIP
  targetPort: 3000
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
  hosts:
    - name: backoffice-backend-service-hml.petrus-software.com
      path: /

#################################################################

env:
  secret:
    DATABASE_URL: hml/backoffice-identify-service/DATABASE_URL
    RABBITMQ_PASSWORD: hml/backoffice-identify-service/RABBITMQ_PASSWORD
    KEYCLOAK_CLIENT_SECRET: hml/backoffice-identify-service/KEYCLOAK_CLIENT_SECRET
    AWS_ACCESS_KEY_ID: hml/backoffice-identify-service/AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: hml/backoffice-identify-service/AWS_SECRET_ACCESS_KEY
    KEYCLOAK_ADMIN_PASSWORD: hml/backoffice-identify-service/KEYCLOAK_ADMIN_PASSWORD
secretStoreRef:
  name: aws-auth-css
  kind: ClusterSecretStore

hpa:
  enabled: false
  # minReplicas: {}
  # maxReplicas: {}
  # averageCpuUtilization: {}
  # averageMemUtilization: {}
# nodeSelector:
#   project: #ex: assaiclientes / marketplace
