# Database Seeder System

Sistema de seeder para popular o banco de dados com dados de teste durante o desenvolvimento.

## Estrutura

- `seeder.ts` - Funções individuais de seeding
- `setup.ts` - Script principal que executa migrações + seeder
- `migrate.ts` - Script apenas para migrações

## Funções Disponíveis

### `seedUsers()`
Cria usuários básicos para o sistema:
- Admin: `<EMAIL>` / `admin123`
- User: `<EMAIL>` / `user123`
- Demo: `<EMAIL>` / `demo123`

### `seedRandomUsers()`
Cria usuários adicionais com perfis variados:
- <PERSON>, <PERSON>, <PERSON>, <PERSON> (inativo), Test User

### `seedDevelopmentData()`
Cria usuários específicos para desenvolvimento (apenas em NODE_ENV=development):
- Developer: `<EMAIL>` / `dev123`
- Tester: `<EMAIL>` / `test123`
- QA: `<EMAIL>` / `qa123`

### `clearTestData()`
Remove todos os usuários de teste do banco de dados.

## Scripts NPM

```bash
# Executar apenas migrações
npm run db:migrate

# Executar apenas seeder
npm run db:seed

# Executar setup completo (migrações + seeder)
npm run db:setup

# Limpar dados de teste
npm run db:clear

# Abrir Drizzle Studio
npm run db:studio
```

## Uso com Docker Compose

O seeder é executado automaticamente quando o container sobe:

```yaml
command: sh -c "pnpm run db:setup && pnpm run dev"
```

## Personalização

Para adicionar novos seeders:

1. Crie uma nova função no `seeder.ts`
2. Adicione-a ao export do arquivo
3. Importe e chame no `setup.ts`
4. Opcionalmente, adicione um script no `package.json`

## Exemplo de Nova Função

```typescript
async function seedMyData() {
  console.log('🌱 Seeding my data...');
  
  try {
    // Verificar se já existe
    const existing = await db.select().from(myTable);
    if (existing.length > 0) {
      console.log('Data already exists, skipping');
      return;
    }

    // Criar dados
    const data = [
      { name: 'Example 1' },
      { name: 'Example 2' },
    ];

    await db.insert(myTable).values(data);
    console.log('✅ My data seeded successfully!');
    
  } catch (error) {
    console.error('❌ Failed to seed my data:', error);
    throw error;
  }
}
```

## Proteções

- Todas as funções verificam se os dados já existem antes de inserir
- Dados específicos de desenvolvimento só são criados em NODE_ENV=development
- Senhas são hashadas usando bcrypt com as configurações do ambiente
- Conexões são fechadas adequadamente após a execução 