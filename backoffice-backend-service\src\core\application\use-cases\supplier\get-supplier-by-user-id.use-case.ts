import { Supplier } from "@/core/domain/supplier/entities/supplier.entity";
import { SupplierRepositoryPort } from "@/core/ports/repositories/supplier-repository.port";
import { SUPPLIER_REPOSITORY } from "@/core/ports/repositories/supplier-repository.token";
import { Inject, Injectable, NotFoundException } from "@nestjs/common";

@Injectable()
export class GetSupplierByUserIdUsecase {
  constructor(
    @Inject(SUPPLIER_REPOSITORY)
    private readonly supplierRepository: SupplierRepositoryPort,
  ) {}

  async execute(userId: string): Promise<Supplier> {
    const existingSupplier = await this.supplierRepository.findByUserId(userId);

    if (!existingSupplier) {
      throw new NotFoundException('Fornecedor não encontrado');
    }

    return existingSupplier;
  }
}