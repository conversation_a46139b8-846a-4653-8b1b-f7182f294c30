/**
 * Complete Swagger Documentation for Authentication API
 * Manual Swagger definitions that work with Zod validation
 */

// Manual Swagger definitions (no validation, just documentation)
export const authSwaggerPaths = {
  '/api/auth/login': {
    post: {
      tags: ['Authentication'],
      summary: 'User Login',
      description: 'Authenticate user with email/username and password',
      consumes: ['application/json'],
      produces: ['application/json'],
      parameters: [
        {
          name: 'body',
          in: 'body',
          required: true,
          schema: {
            type: 'object',
            required: ['password'],
            properties: {
              email: {
                type: 'string',
                format: 'email',
                example: '<EMAIL>'
              },
              username: {
                type: 'string',
                example: 'john_doe123'
              },
              password: {
                type: 'string',
                minLength: 8,
                example: 'MySecurePass123!'
              }
            },
            example: {
              email: '<EMAIL>',
              password: 'MySecurePass123!'
            }
          }
        }
      ],
      responses: {
        200: {
          description: 'Login successful',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'Login successful' },
              user: { $ref: '#/definitions/UserProfile' },
              token: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
              refreshToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
            }
          }
        },
        401: {
          description: 'Invalid credentials',
          schema: { $ref: '#/definitions/ErrorResponse' }
        },
        400: {
          description: 'Validation failed',
          schema: { $ref: '#/definitions/ValidationError' }
        }
      }
    }
  },
  '/api/auth/refresh': {
    post: {
      tags: ['Authentication'],
      summary: 'Refresh Access Token',
      description: 'Get new access token using refresh token',
      consumes: ['application/json'],
      produces: ['application/json'],
      parameters: [
        {
          name: 'body',
          in: 'body',
          required: true,
          schema: {
            type: 'object',
            required: ['refreshToken'],
            properties: {
              refreshToken: {
                type: 'string',
                example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
              }
            }
          }
        }
      ],
      responses: {
        200: {
          description: 'Token refreshed successfully',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'Token refreshed successfully' },
              token: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
              refreshToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
            }
          }
        },
        401: {
          description: 'Invalid or expired refresh token',
          schema: { $ref: '#/definitions/ErrorResponse' }
        }
      }
    }
  },
  '/api/auth/logout': {
    post: {
      tags: ['Authentication'],
      summary: 'User Logout',
      description: 'Logout user and invalidate tokens',
      consumes: ['application/json'],
      produces: ['application/json'],
      parameters: [
        {
          name: 'body',
          in: 'body',
          required: true,
          schema: {
            type: 'object',
            required: ['token', 'refreshToken'],
            properties: {
              token: {
                type: 'string',
                example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
              },
              refreshToken: {
                type: 'string',
                example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
              }
            }
          }
        }
      ],
      responses: {
        200: {
          description: 'Logout successful',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'Logout successful' }
            }
          }
        },
        401: {
          description: 'Invalid tokens',
          schema: { $ref: '#/definitions/ErrorResponse' }
        }
      }
    }
  }
};

// Swagger schemas for routes (only examples, no validation)
export const authRouteSchemas = {
  login: {
    description: 'Authenticate user with email/username and password',
    tags: ['Authentication'],
    summary: 'User Login',
    body: {
      type: 'object',
      example: {
        email: '<EMAIL>',
        password: 'MySecurePass123!'
      }
    },
    response: {
      200: {
        description: 'Login successful',
        type: 'object'
      },
      401: {
        description: 'Invalid credentials',
        type: 'object'
      }
    }
  },

  refreshToken: {
    description: 'Get new access token using refresh token',
    tags: ['Authentication'],
    summary: 'Refresh Access Token',
    body: {
      type: 'object',
      example: {
        refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
      }
    }
  },

  logout: {
    description: 'Logout user and invalidate tokens',
    tags: ['Authentication'],
    summary: 'User Logout',
    body: {
      type: 'object',
      example: {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
      }
    }
  }
};

export const authSwaggerDocs = {
  // ===== LOGIN =====
  login: {
    description: 'Authenticate user with email/username and password',
    tags: ['Authentication'],
    summary: 'User Login',
    body: {
      type: 'object',
      required: ['password'],
      properties: {
        email: {
          type: 'string',
          format: 'email',
          description: 'User email address (either email or username required)',
          example: '<EMAIL>'
        },
        username: {
          type: 'string',
          minLength: 3,
          maxLength: 50,
          description: 'Username (either email or username required)',
          example: 'john_doe123'
        },
        password: {
          type: 'string',
          minLength: 8,
          description: 'User password',
          example: 'MySecurePass123!'
        }
      },
      example: {
        email: '<EMAIL>',
        password: 'MySecurePass123!'
      }
    },
    response: {
      200: {
        description: 'Login successful',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'Login successful' },
          user: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              email: { type: 'string', example: '<EMAIL>' },
              username: { type: 'string', example: 'john_doe123' },
              name: { type: 'string', example: 'John Doe' },
              role: { type: 'string', example: 'USER' },
              isActive: { type: 'boolean', example: true },
              createdAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' },
              updatedAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' }
            }
          },
          token: { 
            type: 'string', 
            description: 'JWT access token (expires in 15 minutes)',
            example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.signature'
          },
          refreshToken: { 
            type: 'string', 
            description: 'JWT refresh token (expires in 7 days)',
            example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.signature'
          }
        }
      },
      401: {
        description: 'Invalid credentials',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'You need to be authenticated to access this resource. Please log in.' }
        }
      },
      400: {
        description: 'Validation failed',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'Validation failed' },
          errors: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                field: { type: 'string', example: 'password' },
                message: { type: 'string', example: 'String must contain at least 8 character(s)' },
                code: { type: 'string', example: 'too_small' },
                minimum: { type: 'number', example: 8 },
                type: { type: 'string', example: 'string' }
              }
            }
          },
          details: { type: 'string', example: '1 validation error(s) found' }
        }
      }
    }
  },

  // ===== REFRESH TOKEN =====
  refreshToken: {
    description: 'Get new access token using refresh token',
    tags: ['Authentication'],
    summary: 'Refresh Access Token',
    body: {
      type: 'object',
      required: ['refreshToken'],
      properties: {
        refreshToken: {
          type: 'string',
          description: 'Valid refresh token',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.signature'
        }
      }
    },
    response: {
      200: {
        description: 'Token refreshed successfully',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'Token refreshed successfully' },
          token: { 
            type: 'string', 
            description: 'New JWT access token (expires in 15 minutes)',
            example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.new_signature'
          },
          refreshToken: { 
            type: 'string', 
            description: 'New JWT refresh token (expires in 7 days)',
            example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.new_signature'
          }
        }
      },
      401: {
        description: 'Invalid or expired refresh token',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'Invalid or expired refresh token.' }
        }
      },
      400: {
        description: 'Validation failed',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'Validation failed' },
          errors: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                field: { type: 'string', example: 'refreshToken' },
                message: { type: 'string', example: 'Required' },
                code: { type: 'string', example: 'invalid_type' }
              }
            }
          }
        }
      }
    }
  },

  // ===== LOGOUT =====
  logout: {
    description: 'Logout user and invalidate tokens',
    tags: ['Authentication'],
    summary: 'User Logout',
    body: {
      type: 'object',
      required: ['token', 'refreshToken'],
      properties: {
        token: {
          type: 'string',
          description: 'Current access token',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.signature'
        },
        refreshToken: {
          type: 'string',
          description: 'Current refresh token',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.signature'
        }
      }
    },
    response: {
      200: {
        description: 'Logout successful',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'Logout successful' }
        }
      },
      401: {
        description: 'Invalid tokens',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'You need to be authenticated to access this resource. Please log in.' }
        }
      },
      400: {
        description: 'Validation failed',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'Validation failed' },
          errors: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                field: { type: 'string', example: 'token' },
                message: { type: 'string', example: 'Required' },
                code: { type: 'string', example: 'invalid_type' }
              }
            }
          }
        }
      }
    }
  }
};

// ===== SWAGGER COMPONENTS =====
export const authSwaggerComponents = {
  schemas: {
    UserProfile: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        email: { type: 'string', format: 'email', example: '<EMAIL>' },
        username: { type: 'string', example: 'john_doe123' },
        name: { type: 'string', example: 'John Doe' },
        role: { type: 'string', enum: ['USER', 'ADMIN'], example: 'USER' },
        isActive: { type: 'boolean', example: true },
        createdAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' },
        updatedAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' }
      }
    },
    LoginRequest: {
      type: 'object',
      required: ['password'],
      properties: {
        email: { type: 'string', format: 'email', example: '<EMAIL>' },
        username: { type: 'string', example: 'john_doe123' },
        password: { type: 'string', minLength: 8, example: 'MySecurePass123!' }
      }
    },
    LoginResponse: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Login successful' },
        user: { $ref: '#/definitions/UserProfile' },
        token: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
        refreshToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
      }
    },
    RefreshTokenRequest: {
      type: 'object',
      required: ['refreshToken'],
      properties: {
        refreshToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
      }
    },
    RefreshTokenResponse: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Token refreshed successfully' },
        token: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
        refreshToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
      }
    },
    LogoutRequest: {
      type: 'object',
      required: ['token', 'refreshToken'],
      properties: {
        token: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
        refreshToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
      }
    },
    LogoutResponse: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Logout successful' }
      }
    },
    ValidationError: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Validation failed' },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string', example: 'password' },
              message: { type: 'string', example: 'String must contain at least 8 character(s)' },
              code: { type: 'string', example: 'too_small' }
            }
          }
        },
        details: { type: 'string', example: '1 validation error(s) found' }
      }
    },
    ErrorResponse: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Operation failed' }
      }
    }
  }
};
