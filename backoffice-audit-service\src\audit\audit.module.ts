import { Modu<PERSON> } from '@nestjs/common';
import { RabbitMQModule as RMQ } from '@golevelup/nestjs-rabbitmq';
import { RabbitmqService } from './rabbitmq/rabbitmq.service';
import { AuditService } from './service/audit.service';
import { PrismaAuditRepository } from './repository/prisma-audit.repository';
import { AuditController } from './controller/audit.controller';

@Module({
  imports: [
    RMQ.forRoot({
      uri:
        process.env.RABBITMQ_URI ??
        (() => {
          throw new Error('RABBITMQ_URI is not defined');
        })(),
      connectionInitOptions: { wait: false },
    }),
  ],
  providers: [
    RabbitmqService,
    AuditService,
    {
      provide: 'AuditRepository',
      useClass: PrismaAuditRepository,
    },
  ],
  controllers: [AuditController],
})
export class AuditModule {}
