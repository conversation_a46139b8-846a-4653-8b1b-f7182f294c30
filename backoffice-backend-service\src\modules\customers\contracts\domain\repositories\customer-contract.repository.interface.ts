import { CustomerContract } from '../entities/customer-contract.entity';
import { ContractStatus, ContractType } from '@prisma/client';

export interface ICustomerContractRepository {
  create(contract: { customerUuid: string; filePath?: string; fileName?: string; isSigned: boolean; createdAt?: Date; updatedAt?: Date }): Promise<CustomerContract>;
  createWithMetadata(contract: { 
    customerUuid: string; 
    filePath?: string; 
    fileName?: string;
    isSigned: boolean;
    status: ContractStatus;
    contractIdentifier: string;
    contractType?: ContractType;
    expirationDate?: string;
    uploadedBy: string;
    createdAt?: Date; 
    updatedAt?: Date 
  }): Promise<CustomerContract>;
  findByUuid(customerUuid: string, contractUuid: string): Promise<CustomerContract | null>;
  findByContractUuid(contractUuid: string): Promise<CustomerContract | null>;
  findAllByCustomer(customerUuid: string): Promise<CustomerContract[]>;
  update(customerUuid: string, contractUuid: string, data: Partial<CustomerContract>): Promise<CustomerContract>;
  delete(customerUuid: string, contractUuid: string): Promise<void>;
} 