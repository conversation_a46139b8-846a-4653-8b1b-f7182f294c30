import { EntityType } from '../enums/entity-type.enum';

export class Service {
  private readonly _id: string;
  private readonly _entityType: EntityType;
  private readonly _entityUuid: string;
  private _type: string;
  private _rate: string;
  private _description: string;
  private readonly _createdAt: Date;
  private readonly _createdBy: string;
  private _updatedAt: Date;
  private _updatedBy: string;
  private _deletedAt?: Date;

  constructor(
    id: string,
    entityType: EntityType,
    entityUuid: string,
    type: string,
    rate: string,
    description: string,
    createdBy: string,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
    updatedBy: string = createdBy,
    deletedAt?: Date
  ) {
    this._id = id;
    this._entityType = entityType;
    this._entityUuid = entityUuid;
    this._type = type;
    this._rate = rate;
    this._description = description;
    this._createdAt = createdAt;
    this._createdBy = createdBy;
    this._updatedAt = updatedAt;
    this._updatedBy = updatedBy;
    this._deletedAt = deletedAt;
    this.validate();
  }

  private validate(): void {
    if (!this._id) {
      throw new Error('ID is required');
    }
    if (!this._entityType) {
      throw new Error('Entity type is required');
    }
    if (!this._entityUuid) {
      throw new Error('Entity UUID is required');
    }
    if (!this._type || this._type.trim().length === 0) {
      throw new Error('Type is required');
    }
    if (!this._rate || this._rate.trim().length === 0) {
      throw new Error('Rate is required');
    }
    if (!this._description || this._description.trim().length === 0) {
      throw new Error('Description is required');
    }
    if (!this._createdBy) {
      throw new Error('Created by is required');
    }
    if (!this._updatedBy) {
      throw new Error('Updated by is required');
    }
  }

  // Getters
  get id(): string {
    return this._id;
  }

  get entityType(): EntityType {
    return this._entityType;
  }

  get entityUuid(): string {
    return this._entityUuid;
  }

  get type(): string {
    return this._type;
  }

  get rate(): string {
    return this._rate;
  }

  get description(): string {
    return this._description;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get createdBy(): string {
    return this._createdBy;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  get updatedBy(): string {
    return this._updatedBy;
  }

  get deletedAt(): Date | undefined {
    return this._deletedAt;
  }

  updateType(type: string, updatedBy: string): void {
    if (!type || type.trim().length === 0) {
      throw new Error('Type is required');
    }
    this._type = type;
    this._updatedBy = updatedBy;
    this._updatedAt = new Date();
  }

  updateRate(rate: string, updatedBy: string): void {
    if (!rate || rate.trim().length === 0) {
      throw new Error('Rate is required');
    }
    this._rate = rate;
    this._updatedBy = updatedBy;
    this._updatedAt = new Date();
  }

  updateDescription(description: string, updatedBy: string): void {
    if (!description || description.trim().length === 0) {
      throw new Error('Description is required');
    }
    this._description = description;
    this._updatedBy = updatedBy;
    this._updatedAt = new Date();
  }

  markAsDeleted(deletedBy: string): void {
    this._deletedAt = new Date();
    this._updatedBy = deletedBy;
    this._updatedAt = new Date();
  }

  toJSON(): any {
    return {
      id: this._id,
      entityType: this._entityType,
      entityUuid: this._entityUuid,
      type: this._type,
      rate: this._rate,
      description: this._description,
      createdAt: this._createdAt.toISOString(),
      createdBy: this._createdBy,
      updatedAt: this._updatedAt.toISOString(),
      updatedBy: this._updatedBy,
      deletedAt: this._deletedAt?.toISOString(),
    };
  }
} 