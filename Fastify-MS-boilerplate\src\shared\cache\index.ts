import redisClient from './redis-client';

export async function getCache<T = any>(key: string): Promise<T | null> {
  const value = await redisClient.get(key);
  return value ? JSON.parse(value) : null;
}

export async function setCache(key: string, value: any, ttlSeconds = 3600): Promise<void> {
  await redisClient.set(key, JSON.stringify(value), 'EX', ttlSeconds);
}

export async function invalidateCache(key: string): Promise<void> {
  await redisClient.del(key);
} 