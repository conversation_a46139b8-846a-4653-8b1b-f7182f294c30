apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.name }}-dpl
  labels:
    app: {{ .Values.name }}-dpl
    chart: "{{.Chart.Name}}-{{.Chart.Version}}"
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  {{- if .Values.replicaCount }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      app: {{ .Values.name }}-dpl
  template:
    metadata:
      annotations:
        timestamp: "{{ now | unixEpoch }}"
      labels:
        app: {{ .Values.name }}-dpl
        chart: "{{.Chart.Name}}-{{.Chart.Version}}"
        role: rolling-update
    spec:
      {{- if .Values.securityContext }}
      securityContext:
        runAsUser: {{ .Values.securityContext.runAsUser }}
        runAsGroup: {{ .Values.securityContext.runAsGroup }}
        runAsNonRoot: {{ .Values.securityContext.runAsNonRoot }} 
      {{- end }}
      containers:
      - name: {{ .Values.name }}-pod
        image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- if or .Values.environment .Values.env.secret }}
        envFrom:
          {{- if .Values.env.secret }}
          - secretRef:
              name: {{ .Values.name }}-sec
          {{- end }}
          {{- if .Values.environment }}
          - configMapRef:
              name: {{ .Values.name }}-config
          {{- end }}
        {{- end }}
        {{- if not (empty .Values.image.containerPort) }}
        ports:
        - containerPort: {{ .Values.image.containerPort }}
        resources:
        {{- if or (empty .Values.resources.requests.cpu) (empty .Values.resources.requests.memory)}}
          requests:
            cpu: {{ .Values.resources.requests.cpu }}
            memory: {{ .Values.resources.requests.memory }}
        {{- end }}
        {{- if or (empty .Values.resources.limits.cpu) (empty .Values.resources.limits.memory)}}
          limits:
            cpu: {{ .Values.resources.limits.cpu }}
            memory: {{ .Values.resources.limits.memory }}
        {{- end }}
        {{- if not (empty .Values.livenessProbe.httpGet.path) }}
        livenessProbe:
          httpGet:
            path: {{ .Values.livenessProbe.httpGet.path }}
            port: {{ .Values.image.containerPort }}
          initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
          failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
        {{- end }}
        {{- if not (empty .Values.readinessProbe.tcpSocket) }}
        readinessProbe:
          tcpSocket:
            port: {{ .Values.image.containerPort }}
          initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
          failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
          {{- end }}
        {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector:
        project: {{ .Values.nodeSelector.project }}
      {{- end }}
      {{- if not (empty .Values.hostAliases) }}
      hostAliases:
      {{- range $name, $value := .Values.hostAliases }}
      - ip: "{{ $value }}"
        hostnames:
        - "{{ $name }}"
      {{- end }}
      {{- end }}
      {{- if not (empty .Values.dnsConfig) }}
      dnsConfig:
        nameservers:
          {{- range .Values.dnsConfig.nameservers }}
          - {{ . }}
          {{- end }}
        searches:
          {{- range .Values.dnsConfig.searches }}
          - {{ . }}
          {{- end }}
      {{- end }}