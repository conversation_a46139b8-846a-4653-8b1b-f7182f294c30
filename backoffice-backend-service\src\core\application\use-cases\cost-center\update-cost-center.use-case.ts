import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { CostCenter } from '@core/domain/cost-center/entities/cost-center.entity';

import {
  CostCenterRepositoryPort,
  COST_CENTER_REPOSITORY,
} from '@core/ports/repositories/cost-center-repository.port';
import { UpdateCostCenterDto } from '@/modules/finance/cost-center/dto/update-cost-center.dto';

@Injectable()
export class UpdateCostCenterUseCase {
  constructor(
    @Inject(COST_CENTER_REPOSITORY)
    private readonly costCenterRepository: CostCenterRepositoryPort,
  ) {}

  async execute(uuid: string, dto: UpdateCostCenterDto): Promise<CostCenter> {
    const costCenter = await this.costCenterRepository.findByUuid(uuid);

    if (!costCenter) {
      throw new NotFoundException('Centro de custo não encontrado.');
    }

    const updatedCostCenter = new CostCenter({
      id: uuid,
      description: dto.description || costCenter.description,
      updatedBy: dto.updatedBy,
      updatedAt: new Date(),
      createdBy: costCenter.createdBy,
      createdAt: costCenter.createdAt,
    });

    return this.costCenterRepository.update(updatedCostCenter);
  }
}
