import { Test, TestingModule } from '@nestjs/testing';
import { DeleteSupplierUseCase } from './delete-supplier.use-case';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { NotFoundException } from '@nestjs/common';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '@prisma/client';

describe('DeleteSupplierUseCase', () => {
  let useCase: DeleteSupplierUseCase;
  let repository: jest.Mocked<SupplierRepositoryPort>;

  const mockSupplier = new Supplier(
    'test-id',
    'Test Supplier',
    '12345678901234',
    'Test Trade',
    new Address('Test Street', null, null, null, 'Test City', '12345-678', 'SP'),
    '<EMAIL>',
    SupplierClassification.CORE,
    SupplierType.GAME,
    SupplierStatus.ACTIVE,
    'user-id',
    'created-by',
    new Date('2024-01-01T00:00:00Z'),
    new Date('2024-01-01T00:00:00Z'),
    'created-by',
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SupplierRepositoryPort> = {
      findById: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByDocument: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findByUserId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeleteSupplierUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<DeleteSupplierUseCase>(DeleteSupplierUseCase);
    repository =
      module.get<jest.Mocked<SupplierRepositoryPort>>(SUPPLIER_REPOSITORY);
  });

  describe('execute', () => {
    it('should delete supplier when found by UUID', async () => {
      const findById = jest.spyOn(repository, 'findById');
      const deleteFn = jest.spyOn(repository, 'delete');

      findById.mockResolvedValue(mockSupplier);

      await useCase.execute({ uuid: 'test-id' });

      expect(findById).toHaveBeenCalledWith('test-id');
      expect(deleteFn).toHaveBeenCalledWith('test-id');
    });

    it('should throw SupplierNotFoundError when supplier does not exist', async () => {
      const findById = jest.spyOn(repository, 'findById');

      findById.mockResolvedValue(null);

      await expect(
        useCase.execute({ uuid: 'non-existent-id' }),
      ).rejects.toThrow(NotFoundException);
      expect(findById).toHaveBeenCalledWith('non-existent-id');
    });
  });
});
