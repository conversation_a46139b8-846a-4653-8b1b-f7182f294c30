import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { SECTOR_REPOSITORY } from '@/core/constants/injection-tokens';
import { SectorRepositoryPort } from '@/core/ports/repositories/sector-repository.port';

export interface DeleteSectorInput {
  uuid: string;
}

@Injectable()
export class DeleteSectorUseCase {
  constructor(
    @Inject(SECTOR_REPOSITORY)
    private readonly sectorRepository: SectorRepositoryPort,
  ) {}

  async execute(input: DeleteSectorInput): Promise<void> {
    const existingSector = await this.sectorRepository.findByUuid(input.uuid);

    if (!existingSector) {
      throw new NotFoundException('Setor não encontrado');
    }

    await this.sectorRepository.delete(input.uuid);
  }
}
