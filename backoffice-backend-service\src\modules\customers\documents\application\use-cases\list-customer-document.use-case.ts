import { Injectable, Inject } from '@nestjs/common';
import { ICustomerDocumentRepository } from '../../domain/repositories/customer-document.repository.interface';
import { CustomerDocument } from '../../domain/entities/customer-document.entity';
import { CUSTOMER_DOCUMENT_REPOSITORY } from '../../domain/constants/tokens';
import { CustomerDocumentListItemDto } from '../../domain/dtos/customer-document.dto';
import { DownloadCustomerDocumentUseCase } from './download-customer-document.use-case';

export interface ListCustomerDocumentResponse {
  items: CustomerDocumentListItemDto[];
  total: number;
  limit: number;
  offset: number;
}

@Injectable()
export class ListCustomerDocumentUseCase {
  constructor(
    @Inject(CUSTOMER_DOCUMENT_REPOSITORY)
    private readonly customerDocumentRepository: ICustomerDocumentRepository,
    private readonly downloadCustomerDocumentUseCase: DownloadCustomerDocumentUseCase,
  ) {}

  async execute(
    customerUuid: string,
    limit = 20,
    offset = 0,
  ): Promise<ListCustomerDocumentResponse> {
    const { documents, total } = await this.customerDocumentRepository.findAllByCustomer(
      customerUuid,
      limit,
      offset,
    );

    const documentsWithUrls = await Promise.all(
      documents.map(async (document) => {
        try {
          // Verificar se o documento tem arquivo antes de tentar gerar URL
          if (document.url && document.url.trim() !== '') {
            const { downloadUrl, fileName } = await this.downloadCustomerDocumentUseCase.execute(
              customerUuid,
              document.id
            );
            return CustomerDocumentListItemDto.fromEntity(document, fileName, downloadUrl);
          } else {
            // Documento sem arquivo - retorna com campos vazios mas presentes
            const fileName = document.fileName || `${document.name}.pdf`;
            return CustomerDocumentListItemDto.fromEntity(document, fileName, undefined);
          }
        } catch (error) {
          console.error('Erro ao gerar URL de download:', error);
          // Retorna com campos de download vazios mas presentes
          const fileName = document.fileName || `${document.name}.pdf`;
          return CustomerDocumentListItemDto.fromEntity(document, fileName, undefined);
        }
      })
    );

    return {
      items: documentsWithUrls,
      total,
      limit,
      offset,
    };
  }
}
