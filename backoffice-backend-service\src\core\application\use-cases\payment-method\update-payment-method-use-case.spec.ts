import { NotFoundException } from '@nestjs/common';
import { UpdatePaymentMethodUseCase } from './update-payment-method-use-case';
import { PaymentMethodRepository } from '../../../ports/repositories/payment-method-repository.interface';
import { PaymentMethod } from '../../../domain/payment-method';

describe('UpdatePaymentMethodUseCase', () => {
  let updatePaymentMethodUseCase: UpdatePaymentMethodUseCase;
  let paymentMethodRepository: jest.Mocked<PaymentMethodRepository>;

  beforeEach(() => {
    paymentMethodRepository = {
      findById: jest.fn(),
      findByLabel: jest.fn(),
      update: jest.fn(),
      findByUuid: jest.fn(),
    } as unknown as jest.Mocked<PaymentMethodRepository>;

    updatePaymentMethodUseCase = new UpdatePaymentMethodUseCase(
      paymentMethodRepository,
    );
  });

  it('should throw NotFoundException if payment method does not exist', async () => {
    paymentMethodRepository.findByUuid.mockResolvedValue(null);

    const input = {
      uuid: 'non-existent-uuid',
      paymentMethod: {
        label: 'Test Label',
        description: 'Test Description',
      },
      updatedBy: 'user1',
    };

    await expect(updatePaymentMethodUseCase.execute(input)).rejects.toThrow(
      new NotFoundException('Método de pagamento não encontrado'),
    );
  });

  it('should throw NotFoundException if label already exists for another payment method', async () => {
    paymentMethodRepository.findByUuid.mockResolvedValue({
      id: 1,
      uuid: 'existing-uuid',
      label: 'Existing Label',
    } as PaymentMethod);

    paymentMethodRepository.findByLabel.mockResolvedValue({
      id: 1,
      uuid: 'another-uuid',
      label: 'Duplicate Label',
    } as PaymentMethod);

    const input = {
      uuid: 'existing-uuid',
      paymentMethod: {
        label: 'Duplicate Label',
        description: 'Updated Description',
      },
      updatedBy: 'user1',
    };

    await expect(updatePaymentMethodUseCase.execute(input)).rejects.toThrow(
      new NotFoundException('Já existe um método de pagamento com este nome'),
    );
  });

  it('should update the payment method successfully', async () => {
    const existingPaymentMethod = {
      id: 1,
      uuid: 'existing-uuid',
      label: 'Existing Label',
      description: 'Existing Description',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user1',
      updatedBy: 'user1',
    } as PaymentMethod;

    paymentMethodRepository.findByUuid.mockResolvedValue(existingPaymentMethod);
    paymentMethodRepository.findByLabel.mockResolvedValue(null);

    const input = {
      uuid: 'existing-uuid',
      paymentMethod: {
        label: 'Updated Label',
        description: 'Updated Description',
      },
      updatedBy: 'user2',
    };

    const update = jest.spyOn(paymentMethodRepository, 'update');

    await updatePaymentMethodUseCase.execute(input);

    expect(update).toHaveBeenCalledWith(
      expect.objectContaining({
        uuid: 'existing-uuid',
        label: 'Updated Label',
        description: 'Updated Description',
        updatedBy: 'user2',
        updatedAt: expect.any(Date) as unknown,
      }),
    );
  });
});
