/**
 * Universal pipe function that handles both synchronous and asynchronous functions.
 * Automatically detects if any function returns a Promise and handles the execution accordingly.
 * If all functions are synchronous, returns the result directly.
 * If any function is asynchronous, returns a Promise with the final result.
 *
 * @param funcs - Functions to pipe (can be sync or async).
 * @returns A function that pipes the input through all functions.
 *
 * @example
 * // Example with only synchronous functions
 * const addOne = (x) => x + 1;
 * const double = (x) => x * 2;
 * const processSync = pipe(addOne, double);
 * console.log(processSync(5)); // Output: 12
 *
 * @example
 * // Example with mixed sync and async functions
 * const addOne = (x) => x + 1;
 * const doubleAsync = async (x) => x * 2;
 * const processAsync = pipe(addOne, doubleAsync);
 * processAsync(5).then(console.log); // Output: 12
 *
 * @example
 * // Example with database operations
 * const validateData = (data) => ({ ...data, validated: true });
 * const saveToDb = async (data) => { return { ...data, id: 123 }; };
 * const logResult = (data) => { console.log('Saved:', data); return data; };
 * 
 * const processUser = pipe(validateData, saveToDb, logResult);
 * processUser({ name: 'John' }).then(result => console.log(result));
 */
export const pipe = <T>(
  ...funcs: Array<(arg: T) => T | Promise<T>>
): ((arg: T) => T | Promise<T>) => {
  return (initialArg: T): T | Promise<T> => {
    let result: T | Promise<T> = initialArg;
    
    for (const func of funcs) {
      if (result instanceof Promise) {
        // If we already have a Promise, chain the next function
        result = result.then(func);
      } else {
        // Execute the function with the current result
        const funcResult = func(result);
        
        // If this function returns a Promise, all subsequent operations become async
        if (funcResult instanceof Promise) {
          result = funcResult;
        } else {
          result = funcResult;
        }
      }
    }
    
    return result;
  };
};

type CustomFunction = (...args: any[]) => any;
/**
 * Transforms a function that expects multiple arguments into a curried function.
 * A curried function is a function that can be called with fewer arguments than
 * it expects, returning a new function that accepts the remaining arguments.
 * This process continues until all arguments are provided, at which point
 * the original function is called and its result returned.
 *
 * @param {Function} fn - The function to curry.
 * @param {...any} args - The arguments to pass to the function.
 * @returns {Function} A curried version of the provided function.
 *
 * @example
 * // Example of a simple function to be curried
 * function add(a, b, c) {
 *   return a + b + c;
 * }
 *
 * // Currying the add function
 * const curriedAdd = curry(add);
 *
 * // Partially applying arguments
 * const addFive = curriedAdd(2, 3);
 *
 * // Calling the partially applied function with the remaining argument
 * console.log(addFive(5)); // Output: 10
 *
 * @example
 * // Using the curried function in a more granular way
 * const addToOne = curriedAdd(1);
 * const addToOneAndTwo = addToOne(2);
 *
 * console.log(addToOneAndTwo(3)); // Output: 6
 */
export function curry(fn: CustomFunction, ...args: any[]): CustomFunction {
  return fn.length <= args.length ? fn(...args) : curry.bind(null, fn, ...args);
}

/**
 * Ensures a function is only called once, regardless of how many times the returned function is called.
 * Subsequent calls to the returned function will have no effect, returning the value from the first call.
 *
 * @param {Function} func - The function to be executed only once.
 * @returns {Function} A function that encapsulates the original function, ensuring it is only executed once.
 *
 * @example
 * const logOnce = once((message) => console.log(message));
 * logOnce("Hello, world!"); // Logs "Hello, world!"
 * logOnce("This will not be logged."); // No effect on subsequent calls
 */
export function once<T extends (...args: any[]) => any>(
  func: T
): (...args: Parameters<T>) => ReturnType<T> {
  let called = false;
  let result: ReturnType<T>;

  return function (...args: Parameters<T>): ReturnType<T> {
    if (!called) {
      called = true;
      result = func(...args);
    }
    return result;
  };
}

/**
 * Generates the date of the previous month from a provided date.
 *
 * @param {string} date - The reference date in string format (YYYY-MM-DD).
 * @param {number} amount - The number of months to subtract to get the previous month's date.
 * @returns {string} - The date of the previous month in string format (YYYY-MM-DD).
 * @example
 * const oneMonthToPast = 1;
 * const pastMonthDate = getPastMonthDate('2024-03-12', oneMonthToPast);
 * console.log(pastMonthDate); // Output: '2024-02-12'
 */
export const getPastMonthDate = (date: string, amount: number): string => {
  const pastMonthDate = new Date(date);
  pastMonthDate.setMonth(pastMonthDate.getMonth() - amount);
  const pastMonthDateWithoutTime = pastMonthDate.toISOString().split("T")[0];

  return pastMonthDateWithoutTime;
};

/**
 * Generates an array of Date objects, each representing the first day of each month between two specified dates, inclusive.
 * This function assumes that `initialDate` and `finalDate` are valid Date objects and that `initialDate` is earlier than or equal to `finalDate`.
 *
 * @param {Date} initialDate - The start date from which to begin generating the array of dates.
 * @param {Date} finalDate - The end date at which to stop generating the array of dates.
 * @returns {Date[]} An array of Date objects, each representing the first day of a month between the initial and final dates, inclusive.
 * @example
 * const initialDate = new Date('2023-01-15');
 * const finalDate = new Date('2023-03-20');
 * const dateRange = getDateRange(initialDate, finalDate);
 * console.log(dateRange); // Output: [new Date('2023-01-01'), new Date('2023-02-01'), new Date('2023-03-01')]
 */
export const getDateRange = (initialDate: Date, finalDate: Date): Date[] => {
  const oneYearMonths = 12;
  const startYear = initialDate.getFullYear();
  const endYear = finalDate.getFullYear();
  const startMonth = initialDate.getMonth();
  const endMonth = finalDate.getMonth();

  const monthCount =
    (endYear - startYear) * oneYearMonths + (endMonth - startMonth) + 1;

  const dates = Array.from({ length: monthCount }, (_, index) => {
    const date = new Date(startYear, startMonth + index, 1);
    return date;
  });

  return dates;
};