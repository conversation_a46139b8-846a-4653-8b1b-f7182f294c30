import { Test } from '@nestjs/testing';
import { AuthController } from '../../auth.controller';
import { AuthService } from '../../auth.service';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { AuthTestUser } from './register.integration.spec';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../../infrastructure/utils/request.utils.service';
import {
  AUTH_TEST_CONFIG,
  mockConfigService,
  mockRequestUtilsService,
} from './auth-test.config';
import { JwtService } from '@nestjs/jwt';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
import { EventPublisherService } from '../../../../infrastructure/events/event-publisher.service';
import { EventsTestModule } from '../../../../infrastructure/events/test/events-test.module';
import { UsersService } from '../../../users/users.service';

// Interface for error responses - prefixed with underscore to indicate it's intentionally unused for now
interface _ErrorResponse {
  message: string;
  [key: string]: unknown;
}

describe('Auth Logout Integration Tests', () => {
  let authController: AuthController;
  let _authService: AuthService;
  let keycloakService: KeycloakService;

  const mockUserRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByEmail: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    updateUserKeycloakId: jest.fn(),
  };

  const mockPasswordResetTokenRepository = {
    create: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
  };

  const mockUsersService = {
    findOne: jest.fn(),
    create: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn().mockImplementation(() => 'mock-jwt-token'),
    decode: jest.fn(),
  };

  const _mockKeycloakIdentityProvider = {
    registerUser: jest.fn(),
    assignUserRoles: jest.fn(),
    authenticate: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    getUserInfo: jest.fn(),
    keycloakAdminUtils: {
      getAdminAuthHeaders: jest.fn().mockResolvedValue({
        Authorization: 'Bearer mock-token',
        'Content-Type': 'application/json',
      }),
    },
  };

  const mockEventPublisher = {
    publish: jest.fn(),
  };

  const mockEmailService = {
    sendPasswordResetEmail: jest.fn(),
  };

  beforeAll(async () => {
    console.log(
      `Using test user email from register test: ${AuthTestUser.email}`,
    );

    // Make sure tokens are available
    if (!AuthTestUser.accessToken) {
      AuthTestUser.accessToken = AUTH_TEST_CONFIG.mock.accessToken;
    }

    if (!AuthTestUser.refreshToken) {
      AuthTestUser.refreshToken = AUTH_TEST_CONFIG.mock.refreshToken;
    }

    const moduleRef = await Test.createTestingModule({
      imports: [EventsTestModule],
      controllers: [AuthController],
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: 'PasswordResetTokenRepository',
          useValue: mockPasswordResetTokenRepository,
        },
        {
          provide: 'EmailService',
          useValue: mockEmailService,
        },
        {
          provide: KeycloakService,
          useFactory: () => ({
            token: jest.fn().mockResolvedValue({
              access_token: AUTH_TEST_CONFIG.mock.accessToken,
              refresh_token: AUTH_TEST_CONFIG.mock.refreshToken,
              expires_in: 300,
            }),
            refreshToken: jest.fn().mockResolvedValue({
              access_token: AUTH_TEST_CONFIG.mock.accessToken,
            }),
            logout: jest.fn().mockResolvedValue(undefined),
            validateToken: jest.fn().mockResolvedValue(true),
            getUserInfo: jest.fn().mockResolvedValue({
              sub: AuthTestUser.keycloakId,
              preferred_username: AuthTestUser.email,
              email: AuthTestUser.email,
              name: AuthTestUser.name,
            }),
            getBaseUrl: jest
              .fn()
              .mockReturnValue(AUTH_TEST_CONFIG.keycloak.baseUrl),
            getRealm: jest
              .fn()
              .mockReturnValue(AUTH_TEST_CONFIG.keycloak.realm),
          }),
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: _mockKeycloakIdentityProvider,
        },
        {
          provide: EventPublisherService,
          useValue: mockEventPublisher,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RequestUtilsService,
          useValue: mockRequestUtilsService,
        },
      ],
    }).compile();

    authController = moduleRef.get<AuthController>(AuthController);
    _authService = moduleRef.get<AuthService>(AuthService);
    keycloakService = moduleRef.get<KeycloakService>(KeycloakService);
  }, 10000);

  describe('logout', () => {
    it('should successfully logout with a valid refresh token', async () => {
      if (!AuthTestUser.accessToken || !AuthTestUser.refreshToken) {
        console.warn('No tokens available, using mock tokens');
        AuthTestUser.accessToken = AUTH_TEST_CONFIG.mock.accessToken;
        AuthTestUser.refreshToken = AUTH_TEST_CONFIG.mock.refreshToken;
      }

      const refreshToken = AuthTestUser.refreshToken;

      const logoutDto = {
        refresh_token: refreshToken,
      };

      // Mock the JwtService.decode to return a valid token
      mockJwtService.decode.mockReturnValueOnce({
        sub: 'user-123',
        jti: 'token-123',
      });

      // Call logout method
      await authController.logout(logoutDto);

      // Use arrow function to avoid unbound method warning
      const logoutSpy = jest.spyOn(keycloakService, 'logout');
      expect(logoutSpy).toHaveBeenCalledWith(refreshToken);

      // Clear the shared tokens since they're now invalid
      AuthTestUser.accessToken = null;
      AuthTestUser.refreshToken = null;
    });

    it('should handle invalid refresh tokens gracefully', async () => {
      const invalidLogoutDto = {
        refresh_token: 'invalid-refresh-token',
      };

      // Mock an error when trying to logout with an invalid token
      jest
        .spyOn(keycloakService, 'logout')
        .mockRejectedValueOnce(new Error('Invalid token'));

      // Expect the logout to throw an error
      await expect(authController.logout(invalidLogoutDto)).rejects.toThrow();
    });

    it('should handle empty refresh tokens', async () => {
      const emptyLogoutDto = {
        refresh_token: '',
      };

      // Expect the logout to throw an error for empty token
      await expect(authController.logout(emptyLogoutDto)).rejects.toThrow();
    });
  });
});
