## Exemplo de uso do BullMQ: M<PERSON><PERSON>lo NASA

Este projeto inclui um exemplo prático de uso do BullMQ para processamento assíncrono de jobs, integrando com a API APOD da NASA e envio de email em background usando Resend.

---

### Como rodar com Docker Compose

Todos os serviços necessários (API, Redis, PostgreSQL e o worker BullMQ para NASA) já estão configurados no arquivo `docker-compose.yml`.

Para subir o ambiente completo, basta executar:

```
docker-compose up
```

Isso irá iniciar a API, o banco de dados, o Redis e o worker BullMQ automaticamente. Não é necessário rodar o worker manualmente em um terminal separado.

---

### Como testar o fluxo completo
1. Certifique-se de que todos os serviços estão rodando com o Docker Compose.
2. Em outro terminal, faça uma requisição para o endpoint:

```
curl -X POST http://localhost:3000/nasa/send-apod \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

3. O job será processado e você receberá um email com a imagem do dia da NASA.

---

### Observações importantes
- O campo `from` do email deve ser um endereço permitido pelo Resend (ex: `<EMAIL>` ou domínio verificado).
- Verifique o painel do Resend para logs de envio e possíveis bloqueios.
- O worker já é executado automaticamente pelo Docker Compose, facilitando a escalabilidade e o deploy.
- Para ambientes de produção, use variáveis de ambiente seguras e domínios verificados.

---
