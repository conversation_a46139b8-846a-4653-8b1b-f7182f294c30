import { Injectable, Inject } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import {
  CostCenterRepositoryPort,
  COST_CENTER_REPOSITORY,
} from '@core/ports/repositories/cost-center-repository.port';
import { CostCenter } from '@core/domain/cost-center/entities/cost-center.entity';
import { CreateCostCenterDto } from '@modules/finance/cost-center/dto/create-cost-center.dto';

@Injectable()
export class CreateCostCenterUseCase {
  constructor(
    @Inject(COST_CENTER_REPOSITORY)
    private readonly costCenterRepository: CostCenterRepositoryPort,
  ) {}

  async execute(dto: CreateCostCenterDto): Promise<CostCenter> {
    const costCenter = new CostCenter({
      id: uuidv4(),
      description: dto.description,
      createdBy: dto.createdBy,
      updatedBy: dto.updatedBy,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return this.costCenterRepository.create(costCenter);
  }
}
