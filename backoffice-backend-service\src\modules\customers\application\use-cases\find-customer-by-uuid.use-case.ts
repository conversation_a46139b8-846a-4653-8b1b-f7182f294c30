import { Injectable, NotFoundException, Inject } from '@nestjs/common';
import { IFindCustomerByUuidUseCase } from '../../domain/use-cases/find-customer-by-uuid.use-case.interface';
import { Customer } from '../../domain/entities/customer.entity';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';

@Injectable()
export class FindCustomerByUuidUseCase implements IFindCustomerByUuidUseCase {
  constructor(
    @Inject('ICustomerRepository')
    private readonly customerRepository: ICustomerRepository,
  ) {}

  async execute(uuid: string): Promise<Customer> {
    const customer = await this.customerRepository.findByUuid(uuid);

    if (!customer) {
      throw new NotFoundException(`Customer with UUID ${uuid} not found`);
    }

    return customer;
  }
}
