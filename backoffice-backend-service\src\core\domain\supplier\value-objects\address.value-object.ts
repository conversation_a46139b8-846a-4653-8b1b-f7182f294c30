export class Address {
  private readonly _street: string;
  private readonly _number: string | null = null; // Optional field
  private readonly _complement: string | null = null; // Optional field
  private readonly _neighborhood: string | null = null; // Optional field
  private readonly _city: string;
  private readonly _zipCode: string;
  private readonly _state: string;

  constructor(
    street: string,
    number: string | null,
    complement: string | null,
    neighborhood: string | null,
    city: string,
    zipCode: string,
    state: string
  ) {
    this._street = street;
    this._number = number || null;
    this._complement = complement || null;
    this._neighborhood = neighborhood || null;
    this._city = city;
    this._zipCode = zipCode;
    this._state = state;

    this.validate();
  }

  private validate(): void {
    if (!this._street) throw new Error('Street is required');
    if (!this._city) throw new Error('City is required');
    if (!this._zipCode) throw new Error('Zip code is required');
    if (!this._state) throw new Error('State is required');

    const zipCodeRegex = /^\d{5}-?\d{3}$/;
    if (!zipCodeRegex.test(this._zipCode)) {
      throw new Error('Invalid zip code. It must be in format XXXXX-XXX or XXXXXXXX.');
    }
  }

  get street(): string {
    return this._street;
  }

  get number(): string | null {
    return this._number;
  }

  get complement(): string | null {
    return this._complement;
  }

  get neighborhood(): string | null {
    return this._neighborhood;
  }

  get city(): string {
    return this._city;
  }

  get zipCode(): string {
    return this._zipCode;
  }

  get state(): string {
    return this._state;
  }

  toJSON(): Record<string, string> {
    return {
      street: this._street,
      number: this._number || '',
      complement: this._complement || '',
      neighborhood: this._neighborhood || '',
      city: this._city,
      zipCode: this._zipCode,
      state: this._state,
    };
  }
}
