import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MulterModule } from '@nestjs/platform-express';
import { DocumentsController } from './application/controllers/document.controller';
import { PrismaModule } from '../../infrastructure/prisma/prisma.module';
import { AwsModule } from '@/infrastructure/aws/aws.module';
import { FindDocumentByUuidUseCase } from './application/use-cases/find-document-by-uuid.use-case';
import { ListDocumentsUseCase } from './application/use-cases/list-documents.use-case';
import { DocumentRepository } from './infrastructure/repositories/document.repository';
import { CreateDocumentUseCase } from './application/use-cases/create-document.use-case';
import { S3StorageProvider } from '@/infrastructure/aws/s3/s3-storage.provider';
import { ArchiveDocumentUseCase } from './application/use-cases/archive-documento.use-case';
import { DownloadDocumentUseCase } from './application/use-cases/download-document.use-case';
import { AddDocumentVersionUseCase } from './application/use-cases/add-version-document.use-case';
import { ListDocumentVersionsUseCase } from './application/use-cases/list-document-version.use-case';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    AwsModule,
    MulterModule.register({
      limits: {
        fileSize: 20 * 1024 * 1024,
      },
    }),
  ],
  controllers: [DocumentsController],
  providers: [
    CreateDocumentUseCase,
    FindDocumentByUuidUseCase,
    ListDocumentsUseCase,
    ArchiveDocumentUseCase,
    DownloadDocumentUseCase,
    AddDocumentVersionUseCase,
    ListDocumentVersionsUseCase,
    {
      provide: 'IDocumentRepository',
      useClass: DocumentRepository,
    },
    {
      provide: 'IStorageProvider',
      useExisting: S3StorageProvider,
    },
  ],
  exports: ['IDocumentRepository', 'IStorageProvider', DownloadDocumentUseCase],
})
export class DocumentModule { }
