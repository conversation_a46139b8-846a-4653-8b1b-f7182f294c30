import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EmployeeRepositoryPort } from '@/core/ports/repositories/employee-repository.port';
import {
  Employee,
  Address,
  PersonalDocument,
  Dependent,
  WorkSchedule,
  Vacation,
} from '@/core/domain/entities/employee.entity';
import {
  ContractType,
  Seniority,
  Shift,
} from '@/core/domain/enums/employee.enum';
import { Prisma, Employee as PrismaEmployee } from '@prisma/client';

type PrismaEmployeeWithRelations = PrismaEmployee;

@Injectable()
export class PrismaEmployeeRepository implements EmployeeRepositoryPort {
  constructor(private readonly prisma: PrismaService) { }

  private toDomain(prismaEmployee: PrismaEmployeeWithRelations): Employee {
    return Employee.create(
      prismaEmployee.id,
      prismaEmployee.uuid,
      prismaEmployee.name,
      prismaEmployee.email,
      prismaEmployee.position,
      prismaEmployee.department,
      prismaEmployee.hireDate,
      prismaEmployee.address as unknown as Address,
      prismaEmployee.personalDocuments as unknown as PersonalDocument[],
      prismaEmployee.dependents as unknown as Dependent[],
      prismaEmployee.status,
      prismaEmployee.createdBy,
      prismaEmployee.updatedBy,
      prismaEmployee.createdAt,
      prismaEmployee.updatedAt,
      prismaEmployee.workSchedule as unknown as WorkSchedule,
      prismaEmployee.shift as Shift,
      prismaEmployee.grossSalary?.toNumber(),
      prismaEmployee.mealAllowance?.toNumber(),
      prismaEmployee.transportAllowance?.toNumber(),
      prismaEmployee.healthPlan ?? undefined,
      prismaEmployee.contractType as ContractType,
      prismaEmployee.seniority as Seniority,
      prismaEmployee.phone ?? undefined,
      prismaEmployee.birthDate ?? undefined,
      prismaEmployee.workHours ?? undefined,
      prismaEmployee.overtimeBank ?? undefined,
      prismaEmployee.vacations as unknown as Vacation[],
      prismaEmployee.userId ?? undefined,
    );
  }

  private toPrisma(employee: Employee): Omit<Prisma.EmployeeCreateInput, 'id'> {
    return {
      uuid: employee.uuid,
      name: employee.name,
      email: employee.email,
      position: employee.position,
      department: employee.department,
      hireDate: new Date(employee.hireDate),
      address: employee.address as unknown as Prisma.InputJsonValue,
      personalDocuments:
        employee.personalDocuments as unknown as Prisma.InputJsonValue,
      dependents: employee.dependents as unknown as Prisma.InputJsonValue,
      status: employee.status,
      createdBy: employee.createdBy,
      updatedBy: employee.updatedBy,
      workSchedule: employee.workSchedule as unknown as Prisma.InputJsonValue,
      shift: employee.shift,
      grossSalary: employee.grossSalary,
      mealAllowance: employee.mealAllowance,
      transportAllowance: employee.transportAllowance,
      healthPlan: employee.healthPlan,
      contractType: employee.contractType,
      seniority: employee.seniority,
      phone: employee.phone,
      birthDate: employee.birthDate ? new Date(employee.birthDate) : undefined,
      workHours: employee.workHours,
      overtimeBank: employee.overtimeBank,
      vacations: employee.vacations as unknown as Prisma.InputJsonValue,
      user: employee.userId ? { connect: { id: employee.userId } } : undefined,
    };
  }

  async create(employee: Employee): Promise<Employee> {
    const created = await this.prisma.employee.create({
      data: this.toPrisma(employee),
    });

    return this.toDomain(created);
  }

  async findByEmail(email: string): Promise<Employee | null> {
    const employee = await this.prisma.employee.findUnique({
      where: { email },
    });
    if (!employee || employee.deletedAt) return null;
    return this.toDomain(employee);
  }

  async findById(id: number): Promise<Employee | null> {
    const employee = await this.prisma.employee.findUnique({
      where: { id },
    });
    if (!employee || employee.deletedAt) return null;
    return this.toDomain(employee);
  }

  async findByUuid(uuid: string): Promise<Employee | null> {
    const employee = await this.prisma.employee.findUnique({
      where: { uuid },
    });
    if (!employee || employee.deletedAt) return null;
    return this.toDomain(employee);
  }

  async findAll(): Promise<Employee[]> {
    const employees = await this.prisma.employee.findMany({
      where: { deletedAt: null },
    });
    return employees.map((e) => this.toDomain(e));
  }

  async findWithPagination(params: {
    limit: number;
    offset: number;
    name?: string;
    email?: string;
  }): Promise<{ items: Employee[]; total: number }> {
    const where: Prisma.EmployeeWhereInput = { deletedAt: null };
    if (params.name) {
      where.name = {
        contains: params.name,
        mode: 'insensitive',
      };
    }
    if (params.email) {
      where.email = {
        contains: params.email,
        mode: 'insensitive',
      };
    }
    const [items, total] = await Promise.all([
      this.prisma.employee.findMany({
        where,
        skip: params.offset,
        take: params.limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.employee.count({ where }),
    ]);
    return {
      items: items.map((employee) => this.toDomain(employee)),
      total,
    };
  }

  async update(employee: Employee): Promise<Employee> {
    const data = this.toPrisma(employee);
    const updated = await this.prisma.employee.update({
      where: { uuid: employee.uuid },
      data: {
        name: data.name,
        email: data.email,
        position: data.position,
        department: data.department,
        hireDate: data.hireDate,
        address: data.address,
        personalDocuments: data.personalDocuments,
        dependents: data.dependents,
        status: data.status,
        updatedBy: data.updatedBy,
        workSchedule: data.workSchedule,
        shift: data.shift,
        grossSalary: data.grossSalary,
        mealAllowance: data.mealAllowance,
        transportAllowance: data.transportAllowance,
        healthPlan: data.healthPlan,
        contractType: data.contractType,
        seniority: data.seniority,
        phone: data.phone,
        birthDate: data.birthDate,
        workHours: data.workHours,
        overtimeBank: data.overtimeBank,
        vacations: data.vacations,
        user: data.user,
      },
    });
    return this.toDomain(updated);
  }

  async uploadPersonalDocument(uuid: string, number: string, filePath: string, fileName: string): Promise<PersonalDocument> {
    const employee = await this.prisma.employee.findUnique({
      where: { uuid },
    });

    if (!employee) throw new NotFoundException('Employee not found');

    const currentDocuments = (employee.personalDocuments as unknown as PersonalDocument[]) || [];
    const documentIndex = currentDocuments.findIndex(doc => doc.number === number);

    if (documentIndex === -1) throw new BadRequestException(`Document of number ${number} not found`);

    currentDocuments[documentIndex] = {
      ...currentDocuments[documentIndex],
      filePath,
      fileName
    };

    await this.prisma.employee.update({
      where: { uuid },
      data: {
        personalDocuments: currentDocuments as unknown as Prisma.InputJsonValue,
      },
    });

    return currentDocuments[documentIndex];
  }

  async findPersonalDocument(uuid: string, number: string): Promise<PersonalDocument | null> {
    const employee = await this.prisma.employee.findUnique({
      where: { uuid },
    });

    if (!employee) throw new NotFoundException('Employee not found');

    const currentDocuments = (employee.personalDocuments as unknown as PersonalDocument[]) || [];
    const document = currentDocuments.find(doc => doc.number === number);

    if (!document) return null;

    return document;
  }

  async deletePersonalDocument(uuid: string, number: string): Promise<void> {
    const employee = await this.prisma.employee.findUnique({
      where: { uuid },
    });

    if (!employee) throw new NotFoundException('Employee not found');

    const currentDocuments = (employee.personalDocuments as unknown as PersonalDocument[]) || [];
    const documentIndex = currentDocuments.findIndex(doc => doc.number === number);

    if (documentIndex === -1) throw new BadRequestException(`Document of number ${number} not found`);

    currentDocuments.splice(documentIndex, 1);

    await this.prisma.employee.update({
      where: { uuid },
      data: { personalDocuments: currentDocuments as unknown as Prisma.InputJsonValue },
    });
  }

  async delete(uuid: string): Promise<void> {
    await this.prisma.employee.update({
      where: { uuid },
      data: { deletedAt: new Date() },
    });
  }

  async findByUserId(userId: string): Promise<Employee | null> {
    const employee = await this.prisma.employee.findUnique({
      where: { userId },
    });
    if (!employee || employee.deletedAt) return null;
    return this.toDomain(employee);
  }
}
