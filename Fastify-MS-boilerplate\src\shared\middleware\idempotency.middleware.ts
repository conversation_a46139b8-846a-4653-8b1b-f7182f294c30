import type { FastifyRequest, FastifyReply } from 'fastify';
import redisClient from '../cache/redis-client';

/**
 * Middleware de idempotência para Fastify.
 * Lê a chave do header 'idempotency-key', salva e retorna resposta do cache se já existir.
 * TTL de 1 hora.
 */
export async function idempotencyMiddleware(request: FastifyRequest, reply: FastifyReply) {
  const idempotencyKey = request.headers['idempotency-key'];
  console.log('[Idempotency] Header recebido:', idempotencyKey);
  if (!idempotencyKey || typeof idempotencyKey !== 'string') {
    // Se não houver chave, segue normalmente
    return;
  }

  // Busca resposta salva no Redis
  const cached = await redisClient.get(`idempotency:${idempotencyKey}`);
  if (cached) {
    const { statusCode, payload, headers } = JSON.parse(cached);
    if (headers) {
      Object.entries(headers).forEach(([k, v]) => reply.header(k, v));
    }
    reply.code(statusCode).send(payload);
    // Interrompe o fluxo
    return reply;
  }

  // Intercepta o send para salvar a resposta no Redis
  const originalSend = reply.send.bind(reply);
  reply.send = function (payload) {
    const responseToCache = {
      statusCode: reply.statusCode,
      payload,
      headers: reply.getHeaders(),
    };
    void redisClient.set(
      `idempotency:${idempotencyKey}`,
      JSON.stringify(responseToCache),
      'EX',
      60 * 60 // 1 hora
    );
    return originalSend(payload);
  };
} 