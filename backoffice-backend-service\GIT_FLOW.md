# Fluxo de Trabalho Git

Este documento define o fluxo de trabalho Git adotado pela equipe para manter nosso código organizado e facilitar o trabalho colaborativo entre os desenvolvedores.

## Estrutura de Branches

```
      hotfix/PROJ-123 ------> main
     /                       /
    /         release-x.y.z /
   /                       /
  /   feature/PROJ-456    /
 /   /                   /
main -----> develop ----+
```

### Branches Principais

- **`main`**: Representa o código em produção.
  - Sempre estável e pronto para implantação
  - Protegida contra push direto
  - Toda mudança deve vir através de merge de uma branch `release` ou `hotfix`

- **`develop`**: Ambiente de desenvolvimento da sprint atual.
  - Contém todas as funcionalidades concluídas e em progresso para a próxima versão
  - Os desenvolvedores nunca devem commitar diretamente nesta branch
  - Novas funcionalidades são integradas através de Pull Requests de branches de `feature`

### Branches Temporárias

- **`feature/PROJ-XXX-descricao-curta`**: Para desenvolvimento de novas funcionalidades.
  - Sempre criada a partir de `develop`
  - Terminado o trabalho, é integrada de volta à `develop` via Pull Request
  - Exemplo: `feature/PROJ-123-implementar-autenticacao`

- **`bugfix/PROJ-XXX-descricao-curta`**: Para correção de bugs em desenvolvimento.
  - Criada a partir de `develop`
  - Corrigido o bug, é reintegrada à `develop` via Pull Request
  - Exemplo: `bugfix/PROJ-456-corrigir-validacao-formulario`

- **`release/x.y.z`**: Preparação para uma nova versão em produção.
  - Criada a partir de `develop` quando esta contém todas as funcionalidades planejadas para a versão
  - Apenas correções de bugs são permitidas nesta branch
  - Após testes e aprovação, é mesclada tanto para `main` quanto para `develop`

- **`hotfix/PROJ-XXX-descricao-curta`**: Para correções urgentes em produção.
  - Criada a partir de `main`
  - Após a correção, é mesclada tanto para `main` quanto para `develop`
  - Exemplo: `hotfix/PROJ-789-corrigir-erro-critico-autenticacao`

## Fluxo de Trabalho Completo

### Desenvolvimento de Funcionalidades

1. O desenvolvedor cria uma branch a partir de `develop`:
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/PROJ-XXX-descricao-curta
   ```

2. Trabalha na funcionalidade, fazendo commits regulares:
   ```bash
   git add .
   git commit -m "feat(PROJ-XXX): descrição detalhada da alteração"
   ```

3. Atualiza sua branch com as mudanças mais recentes de `develop` periodicamente:
   ```bash
   git checkout develop
   git pull
   git checkout feature/PROJ-XXX-descricao-curta
   git merge develop
   # Resolva conflitos se necessário
   ```

4. Ao finalizar, cria um Pull Request para `develop`
   - O título deve incluir o código da tarefa: `[PROJ-XXX] Descrição da funcionalidade`
   - Descrever claramente as alterações feitas
   - Solicitar revisão de pelo menos 2 desenvolvedores
   - Verificar que todos os testes estão passando

5. Após aprovação, a branch é mesclada à `develop` e pode ser excluída

### Correção de Bugs em Desenvolvimento

- Siga o mesmo fluxo das funcionalidades, mas use o prefixo `bugfix/` no nome da branch

### Publicação de Versão

1. Quando `develop` contém todas as funcionalidades planejadas para a próxima versão:
   ```bash
   git checkout develop
   git pull
   git checkout -b release/x.y.z
   ```

2. Apenas correções de bugs são permitidas nesta branch:
   ```bash
   git checkout -b bugfix/PROJ-XXX-correcao-bug
   # Após correção
   git checkout release/x.y.z
   git merge bugfix/PROJ-XXX-correcao-bug
   ```

3. Após testes e aprovação em ambiente de homologação, a branch é mesclada à `main` e à `develop`:
   ```bash
   # Mesclar na main
   git checkout main
   git merge release/x.y.z
   git tag -a x.y.z -m "Versão x.y.z"
   git push origin main --tags
   
   # Mesclar na develop
   git checkout develop
   git merge release/x.y.z
   git push origin develop
   ```

### Fluxo de Hotfix

1. Quando um bug crítico é encontrado em produção:
   ```bash
   git checkout main
   git pull
   git checkout -b hotfix/PROJ-XXX-descricao-problema
   ```

2. Corrige o problema:
   ```bash
   git add .
   git commit -m "fix(PROJ-XXX): descrição da correção"
   ```

3. Após testes, é mesclado para `main` e para `develop`:
   ```bash
   # Mesclar na main
   git checkout main
   git merge hotfix/PROJ-XXX-descricao-problema
   git tag -a x.y.z+1 -m "Hotfix x.y.z+1"
   git push origin main --tags
   
   # Mesclar na develop
   git checkout develop
   git merge hotfix/PROJ-XXX-descricao-problema
   git push origin develop
   ```

## Convenções de Mensagens de Commit

Seguimos o padrão de [Conventional Commits](https://www.conventionalcommits.org/):

```
tipo(escopo): mensagem
```

Onde:
- **tipo**: Indica a natureza da mudança
  - `feat`: Nova funcionalidade
  - `fix`: Correção de bug
  - `docs`: Documentação
  - `style`: Formatação (não afeta o código)
  - `refactor`: Refatoração de código
  - `test`: Testes
  - `chore`: Alterações em processos de build, ferramentas, etc.

- **escopo**: Área ou componente afetado (opcional), geralmente o código do JIRA
  - Exemplo: `feat(PROJ-123): implementação do login com Google`

## Integração com JIRA

1. Toda branch deve referenciar uma tarefa do JIRA no nome:
   - `feature/PROJ-123-nome-da-tarefa`
   - `bugfix/PROJ-456-descricao-bug`
   - `hotfix/PROJ-789-problema-urgente`

2. Mensagens de commit devem incluir o código da tarefa:
   - `feat(PROJ-123): implementação da funcionalidade X`
   - `fix(PROJ-456): correção do bug Y`

3. Ao criar um Pull Request, inclua o código JIRA no título:
   - `[PROJ-123] Implementação da funcionalidade X`

4. Configure webhooks para que o JIRA seja atualizado automaticamente quando:
   - Uma branch é criada para uma tarefa
   - Um Pull Request é criado/aprovado/mesclado
   - Commits são feitos referenciando a tarefa

## Boas Práticas

1. **Mantenha branches pequenas e focadas**
   - Prefira branches que implementem uma única funcionalidade ou correção
   - Branches grandes são difíceis de revisar e mais propensas a conflitos

2. **Atualize sua branch com frequência**
   - Rebase ou merge de `develop` regularmente para evitar conflitos maiores

3. **Escreva mensagens de commit claras**
   - Explique o que foi feito e por quê, não apenas como

4. **Revise o próprio código antes de solicitar revisão**
   - Verifique se seguiu o guia de estilo
   - Execute os testes antes de submeter o PR

5. **Nunca reescreva o histórico de branches públicas**
   - Use `git rebase` apenas em branches locais não compartilhadas

6. **Realize Code Reviews completos e construtivos**
   - Foque em melhorar o código, não em criticar o desenvolvedor
   - Verifique a qualidade do código, testes, e documentação

---

Este documento deve ser seguido por todos os membros da equipe. Caso tenha dúvidas, consulte o líder técnico do projeto. 