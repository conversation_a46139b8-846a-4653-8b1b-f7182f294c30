{"info": {"_postman_id": "a8124650-fe50-4560-b1c6-d0052ca1720d", "name": "Auth API", "description": "Coleção para testar o fluxo de autenticação da API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Registro", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"access_token\", jsonData.access_token);", "pm.environment.set(\"keycloak_id\", jsonData.user.keycloakId);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"senha123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}, "description": "Registra um novo usuário no sistema"}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"access_token\", jsonData.access_token);", "pm.environment.set(\"refresh_token\", jsonData.refresh_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"<EMAIL>\",\n    \"password\": \"senha123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Autentica um usuário e retorna tokens de acesso"}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"access_token\", jsonData.access_token);", "pm.environment.set(\"refresh_token\", jsonData.refresh_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refresh_token}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}, "description": "Renova o token de acesso usando o refresh token"}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refresh_token}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}, "description": "Encerra a sessão do usuário"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000/v1/backoffice", "type": "string"}]}