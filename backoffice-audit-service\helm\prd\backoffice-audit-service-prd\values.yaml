name: backoffice-audit-service-prd
# replicaCount: 1
image:
  repository: 321711906762.dkr.ecr.us-east-2.amazonaws.com/backoffice-audit-service
  tag: latest
  pullPolicy: Always
  containerPort: 3000
environment:
  NODE_ENV: production

resources:
  requests:
    cpu: 0
    memory: 0
  limits:
    cpu: 0.5
    memory: 200Mi
livenessProbe:
  httpGet:
    path: {}
  initialDelaySeconds: 10
  failureThreshold: 3
  periodSeconds: 10
readinessProbe:
  tcpSocket: null
  initialDelaySeconds: 10
  periodSeconds: 10
  failureThreshold: 3
service:
  type: ClusterIP
  targetPort: 3000
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
  hosts:
    - name: backoffice-audit-service.petrus-software.com
      path: /

######################################################################
# env:
#   secret: {}
# secretStoreRef:
#   name: aws-auth-css
#   kind: ClusterSecretStore

# secretStoreRef:
#   name: aws-auth-css
#   kind: ClusterSecretStore

hpa:
  enabled: false
  # minReplicas: {}
  # maxReplicas: {}
  # averageCpuUtilization: {}
  # averageMemUtilization: {}
# nodeSelector:
#   project: #ex: assaiclientes / marketplace
