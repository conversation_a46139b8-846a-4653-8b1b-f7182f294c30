import { Injectable, Inject, ConflictException, forwardRef } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { EMPLOYEE_REPOSITORY } from '@/core/ports/repositories/employee-repository.port';
import { EmployeeRepositoryPort } from '@/core/ports/repositories/employee-repository.port';
import { Employee, WorkSchedule } from '@/core/domain/entities/employee.entity';
import { CreateEmployeeDto } from '@/modules/finance/employee/dto/create-employee.dto';
import { UsersService } from '@/modules/users/users.service';
import { AuthService } from '@/modules/auth/auth.service';
import { Role } from '@/core/domain/role.enum';

@Injectable()
export class CreateEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
  ) { }

  async execute(dto: CreateEmployeeDto): Promise<Employee> {
    // Verifica se já existe usuário com o e-mail
    const user = await this.usersService.findByEmail(dto.email).catch(error => {
      if (error instanceof Error && error.message.includes('não encontrado')) {
        return null;
      }
      throw error;
    });
    if (user) {
      throw new ConflictException({
        code: 'Email já existente',
        message: 'O email do funcionário já existe como usuário',
      });
    }

    // Cria usuário
    const userCreated = await this.usersService.create({
      name: dto.name,
      email: dto.email,
      password: 'sut@t6@LuhKX29*C', // Senha temporária
      role: Role.EMPLOYEE,
    });

    // Cria Employee associado ao userId
    const employee = Employee.create(
      0,
      uuidv4(),
      dto.name,
      dto.email,
      dto.position,
      dto.department,
      new Date(dto.hireDate),
      dto.address,
      dto.personalDocuments,
      dto.dependents,
      dto.status,
      dto.createdBy ?? 'system',
      dto.updatedBy ?? 'system',
      new Date(),
      new Date(),
      dto.workSchedule as WorkSchedule | undefined,
      dto.shift,
      dto.grossSalary,
      dto.mealAllowance,
      dto.transportAllowance,
      dto.healthPlan,
      dto.contractType,
      dto.seniority,
      dto.phone,
      dto.birthDate ? new Date(dto.birthDate) : undefined,
      dto.workHours,
      dto.overtimeBank,
      dto.vacations,
      userCreated.id, // userId
    );
    const createdEmployee = await this.employeeRepository.create(employee);

    // Envia e-mail de onboarding
    await this.authService.forgotPassword({ email: userCreated.email });

    return createdEmployee;
  }
}
